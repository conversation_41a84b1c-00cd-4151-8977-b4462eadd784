{"name": "pro-web-f-int-vue-ts", "version": "0.1.0", "private": true, "type": "module", "scripts": {"lint": "eslint --ext .js,.vue src", "dev": "vue-tsc --noEmit --watch & vite --debug hmr", "serve": "vite", "build": "vite build", "preview": "vite preview", "prepare": "husky install"}, "engines": {"node": "20.10.0"}, "dependencies": {"decimal.js": "^10.4.3", "dompurify": "^3.0.6", "file-saver": "^2.0.5", "fuse.js": "^7.0.0", "jose": "^5.1.3", "mapbox-gl": "1.8.1", "mitt": "^3.0.1", "moment-timezone": "^0.5.43", "pinia": "^2.1.7", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "uuid": "^9.0.1", "vue": "2.7.15", "vue-class-component": "^7.2.6", "vue-flip": "^0.3.0", "vue-infinite-slide-bar": "^1.1.1", "vue-notification": "^1.3.20", "vue-property-decorator": "^9.1.2", "vue-router": "^3.4.9", "vue-shortkey": "^3.1.7", "vue-the-mask": "^0.11.1", "vuedraggable": "^2.24.3", "vuetify": "^1.5.24", "zxcvbn": "^4.4.2"}, "devDependencies": {"@types/mapbox-gl": "^2.4.2", "@types/node": "^20.10.0", "@types/stompjs": "^2.3.9", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitejs/plugin-vue2": "^2.3.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.20.1", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "3.1.0", "sass": "^1.77.0", "typescript": "^5.3.2", "vite": "^5.0.0", "vite-plugin-checker": "^0.6.2", "vue-tsc": "^1.8.22"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}