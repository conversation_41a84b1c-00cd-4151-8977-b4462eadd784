import '@/static/fontawesome-pro/css/all.min.css';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import Mitt from '@/utils/mitt';
import {
  getCompanyDetails,
  isAuthenticatedUser,
  routeList,
} from '@/utils/RouterUtils';
import { createPinia, PiniaVuePlugin } from 'pinia';
import Vue from 'vue';
import Notifications from 'vue-notification';
import Router, { NavigationGuardNext, RawLocation, Route } from 'vue-router';
import VueShortkey from 'vue-shortkey';
import VueTheMask from 'vue-the-mask';
import Vuetify from 'vuetify';
import '../node_modules/mapbox-gl/dist/mapbox-gl.css';
import '../node_modules/vuetify/dist/vuetify.min.css';
import App from './App.vue';
import Environment from './configuration/environment';
import { Portal } from './interface-models/Generic/Portal';
import './styles/vuetify-customised.scss';
import { registerComposables } from './utils/composables';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';

Vue.use(PiniaVuePlugin);
const pinia = createPinia();

// Below adds a catch to all route change promises. This is added so duplicate
// route navigation is "handled" and to stop errors showing in the console.
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location: RawLocation) {
  return (originalPush.call(this, location) as any).catch((err: Error) => err);
};

Vue.use(Router);

const router = new Router({
  routes: routeList,
});

router.beforeEach(async (to: Route, from: Route, next: NavigationGuardNext) => {
  // Set the users portal type.
  sessionManager.setPortalType();

  // If the user is not yet connected to the websocket we will attempt to get the company details by the users endpoint.
  if (
    !useWebsocketStore().websocket &&
    !from.name &&
    sessionManager.getPortalType() !== Portal.USER
  ) {
    getCompanyDetails(pinia);
  } else {
    Mitt.emit('isLoadingInitialCompanyDetails', false);
  }

  // If the route does not require authentication, allow them to proceed.
  if (!(to.meta?.requiresAuth ?? false)) {
    next();
    return;
  }

  // Check if the user is authenticated.
  const isAuthenticated: boolean = isAuthenticatedUser(pinia);

  // If the user is not authenticated and they attempt to go to a route that is not the login page, redirect them to the login page.
  if (!isAuthenticated && from.name !== 'login_index') {
    next('/login');
    return;
  }

  if (
    sessionManager.getPortalType() === Portal.USER &&
    !isAuthenticated &&
    from.name === 'login_index'
  ) {
    next('/login');
    return;
  }

  // If the user is on the client portal and they attempt to go to a route that is not within the bounds of the client portal, redirect them to the client portal.
  if (
    sessionManager.getPortalType() === Portal.CLIENT &&
    !(to.meta?.isClientPortal ?? false)
  ) {
    next({
      path: '/client-portal',
      query: { redirect: to.fullPath },
    });
    return;
  }

  // If the user is on the user portal and they attempt to go to a route that is not within the bounds of the user portal, redirect them to the user portal.
  if (
    sessionManager.getPortalType() === Portal.USER &&
    !(to.meta?.isUserPortal ?? false)
  ) {
    next({
      path: '/account-details',
      query: { redirect: to.fullPath },
    });
    return;
  }

  // If the user is on the operations portal and they attempt to go to a route that is not within the bounds of the operations portal, redirect them to the operations portal.
  if (
    sessionManager.getPortalType() === Portal.OPERATIONS &&
    (to.name === 'client_portal' || (to.meta?.isUserPortal ?? false))
  ) {
    next({
      path: '/',
      query: { redirect: to.fullPath },
    });
    return;
  }

  // If the user attempts to go to the developer maintenance page and the current environment is not a development environment, redirect them to correct portal home.
  if (to.name === 'maintenance') {
    const ENV: string | undefined = Environment.value('environment');
    if (ENV === 'local' || ENV === 'staging') {
      next();
    } else {
      next({
        path:
          sessionManager.getPortalType() === Portal.CLIENT
            ? '/client-portal'
            : sessionManager.getPortalType() === Portal.USER
              ? '/user-details'
              : '/',
        query: { redirect: to.fullPath },
      });
    }
    return;
  }

  // If the user tries to go to route admin_tools and its not a Godesta admin user
  // we push them back to root page
  if (to.name === 'admin_tools' && !sessionManager.getIsGodestaAdminUser()) {
    next({
      path: '/',
      query: { redirect: to.fullPath },
    });
    return;
  }

  next();
});

router.afterEach((to: Route) => {
  useAppNavigationStore().setCurrentRouteTitle(to.name as string);
});

Vue.use(Notifications);
Vue.use(Vuetify);
Vue.use(VueTheMask);
Vue.use(VueShortkey);

Vue.config.productionTip = false;

registerComposables();

new Vue({
  el: '#app',
  router,
  pinia,
  render: (h: any) => h(App),
} as any);
