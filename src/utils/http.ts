interface HttpResponseSuccess<T> {
  ok: boolean;
  data: T;
  statusCode: number;
  errorMessage?: never;
}
interface HttpResponseError {
  ok: false;
  data?: never; // data is undefined for error responses
  statusCode: number;
  errorMessage: string;
}
export type HttpResponse<T> = HttpResponseSuccess<T> | HttpResponseError;

import Environment from '@/configuration/environment';
export default function useHttp() {
  /**
   * Performs a GET request to the specified URL.
   * @param {string} url - The URL to send the request to.
   * @returns {Promise<HttpResponse<T>>} A promise that resolves to the HttpResponse object.
   */
  async function get<T>(url: string): Promise<HttpResponse<T>> {
    try {
      const endpoint: string = Environment.value('end_point') + url;
      const response = await fetch(endpoint);
      if (!response.ok) {
        const errorMessage = await extractErrorMessage(response);
        return {
          ok: false,
          statusCode: response.status,
          errorMessage,
        };
      }
      const data: T = await parseResponse(response);
      if (
        Environment.value('environment') === 'local' ||
        Environment.value('environment') === 'staging'
      ) {
        // console.log('Request GET:', url);
        // console.log('Response:', data);
      }

      return {
        ok: true,
        statusCode: response.status,
        data,
      };
    } catch (error: unknown) {
      let errorMessage = 'An unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return {
        ok: false,
        statusCode: 0,
        errorMessage: errorMessage,
      };
    }
  }

  /**
   * Performs a POST request to the specified URL.
   * @param {string} url - The URL to send the request to.
   * @param {any} body - The body of the request, typically an object.
   * @returns {Promise<HttpResponse<T>>} A promise that resolves to the HttpResponse object.
   */
  async function post<T>(url: string, body: any): Promise<HttpResponse<T>> {
    try {
      const endpoint: string = Environment.value('end_point') + url;
      const response = await fetch(endpoint, {
        method: 'POST', // *GET, POST, PUT, DELETE, etc.
        mode: 'cors', // no-cors, *cors, same-origin
        cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
        credentials: 'same-origin', // include, *same-origin, omit
        headers: {
          'Content-Type': 'application/json',
        },
        redirect: 'follow', // manual, *follow, error
        referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
        body: JSON.stringify(body),
      });
      if (!response.ok) {
        const errorMessage = await extractErrorMessage(response);
        return {
          ok: false,
          statusCode: response.status,
          errorMessage,
        };
      }
      const data: T = await parseResponse(response);

      if (
        Environment.value('environment') === 'local' ||
        Environment.value('environment') === 'staging'
      ) {
        // console.log('Request POST:', url, body);
        // console.log('Response:', data);
      }

      return { ok: true, statusCode: response.status, data };
    } catch (error: unknown) {
      let errorMessage = 'An unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return {
        ok: false,
        statusCode: 0,
        errorMessage,
      };
    }
  }

  /**
   * Parses the response body according to its content type.
   * @param {Response} response - The fetch response object.
   * @returns {Promise<any>} A promise that resolves to the parsed response data.
   */
  async function parseResponse(response: Response): Promise<any> {
    const contentType = response.headers.get('Content-Type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    } else {
      return response.text();
    }
  }

  /**
   * Extracts error message from response.
   * @param {Response} response - The fetch response object.
   * @returns {Promise<string>} A promise that resolves to the error message.
   */
  async function extractErrorMessage(response: Response): Promise<string> {
    try {
      const responseData = await parseResponse(response);
      return (
        responseData.message ||
        responseData.error ||
        JSON.stringify(responseData)
      );
    } catch (error: unknown) {
      return `Error parsing response: ${error}`;
    }
  }

  return { get, post };
}
