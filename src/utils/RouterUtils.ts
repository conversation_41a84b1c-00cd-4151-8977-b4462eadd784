import AdminTools from '@/admin_tools/admin_tools_index/index.vue';
import ConfirmUpdateDetails from '@/authentication/confirm_update_details/confirm_update_details.vue';
import SearchWrapperIndex from '@/client_portal/SearchIndex/search_wrapper_index.vue';
import ClientAccessIndex from '@/client_portal/index/client_access_index.vue';
import AccountingIndex from '@/components/admin/Accounting/accounting_index/accounting_index.vue';
import AdministrationIndex from '@/components/admin/Administration/administration_index.vue';
import ClientDetailsIndex from '@/components/admin/ClientDetails/client_details_index.vue';
import ClientIndex from '@/components/admin/ClientIndex/client_index.vue';
import DriverDetailsIndex from '@/components/admin/DriverDetails/driver_details_index.vue';
import TrailerDetailsIndex from '@/components/admin/FleetAsset/trailer/fleet_asset_trailer_index.vue';
import { default as TruckDetailsIndex } from '@/components/admin/FleetAsset/truck/fleet_asset_index.vue';
import FleetAssetOwnerIndex from '@/components/admin/FleetAssetOwner/fleet_asset_owner_index.vue';
import SubcontractorIndex from '@/components/admin/SubcontractorIndex/subcontractor_index.vue';
import Home from '@/components/home/<USER>';
import Maintenance from '@/components/maintenance/maintenance_index.vue';
import FleetTracking from '@/components/operations/FleetTracking/index.vue';
import JobList from '@/components/operations/OperationDashboard/components/JobList/index.vue';
import OperationsIndex from '@/components/operations/index/index.vue';
import Environment from '@/configuration/environment';
import {
  authenticateUser,
  isTokenValid,
} from '@/helpers/AuthenticationHelpers/LoginHelpers';
import { CompanyDetails } from '@/interface-models/Company/CompanyDetails';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import userPortalAccountDetails from '@/user_portal/user_portal_account_details/user_portal_account_details.vue';
import userPortalAccountSecurity from '@/user_portal/user_portal_account_security/user_portal_account_security.vue';
import useHttp from '@/utils/http';
import Mitt from '@/utils/mitt';
import { Pinia } from 'pinia';
import { RouteConfig } from 'vue-router';

export const routeList: RouteConfig[] = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/subcontractor',
    name: 'Subcontractor',
    component: SubcontractorIndex,
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/client',
    name: 'Client',
    component: ClientIndex,
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/client/client-details/:name/:id',
    name: 'Client Details',
    component: ClientDetailsIndex,
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/subcontractor/fleet-asset-owner/:name/:id',
    name: 'Owner',
    component: FleetAssetOwnerIndex,
    meta: {
      requiresAuth: true,
    },
    props: {
      isDialog: false,
    },
  },
  {
    path: '/subcontractor/driver/:name/:id',
    name: 'Driver',
    component: DriverDetailsIndex,
    meta: {
      requiresAuth: true,
    },
    props: {
      isDialog: false,
    },
  },

  {
    path: '/subcontractor/truck/:name/:id',
    name: 'Truck',
    component: TruckDetailsIndex,
    meta: {
      requiresAuth: true,
    },
    props: {
      isDialog: false,
    },
  },
  {
    path: '/subcontractor/trailer/:name/:id',
    name: 'Trailer',
    component: TrailerDetailsIndex,
    meta: {
      requiresAuth: true,
    },
    props: {
      isDialog: false,
    },
  },
  {
    path: '/client-portal',
    name: 'client_portal',
    component: ClientAccessIndex,
    meta: {
      requiresAuth: true,
      isClientPortal: true,
    },
  },
  {
    path: '/operations',
    name: 'operations_index',
    component: OperationsIndex,
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/account-recovery',
    name: 'account_recovery',
    component: () => {
      return import(
        '@/authentication/forgotten_password/forgotten_password_account_recovery.vue'
      );
    },
    props: (route) => ({
      username: route.query.username,
    }),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/passwordRecovery/:id',
    name: 'password_recovery',
    component: () => {
      return import(
        '@/authentication/forgotten_password/forgotten_password_password_recovery.vue'
      );
    },
    meta: {
      requiresAuth: false,
      isUserPortal: true,
    },
  },
  {
    path: '/login',
    name: 'login_index',
    component: () => {
      const subdomain = getSubdomain();
      if (subdomain === 'client') {
        return import(
          '@/client_portal/authentication/client_portal_authentication_index/index.vue'
        );
      } else if (subdomain === 'userportal') {
        return import(
          '@/user_portal/authentication/user_portal_authentication_index/index.vue'
        );
      } else {
        return import(
          '@/components/authentication/operations_portal_authentication_index/index.vue'
        );
      }
    },
  },

  {
    path: '/accounting',
    name: 'Accounting',
    component: AccountingIndex,
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/administration',
    name: 'Division',
    component: AdministrationIndex,
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/maintenance',
    name: 'maintenance',
    component: Maintenance,
    meta: {
      requiresAuth: true,
      developerRoute: true,
    },
  },
  {
    path: '/job-search/',
    name: 'Job/Quote Search',
    component: SearchWrapperIndex,
    meta: {
      requiresAuth: true,
    },
    props: {
      isClientPortal: false,
    },
  },
  {
    path: '/job-list/',
    name: 'job_list',
    component: JobList,
    meta: {
      requiresAuth: false,
    },
    props: {
      windowMode: true,
    },
  },
  {
    path: '/fleet-tracking/',
    name: 'fleet_tracking',
    component: FleetTracking,
    meta: {
      requiresAuth: false,
    },
    props: {
      windowMode: true,
    },
  },
  {
    path: '/forgot-password/:id',
    name: 'forgot_password',
    component: () =>
      import(
        '@/authentication/forgotten_password/forgotten_password_reset.vue'
      ),
    meta: {
      requiresAuth: false,
    },
  },
  {
    path: '/create-password/:id',
    name: 'create_password',
    component: () =>
      import('@/authentication/create_password/create_password.vue'),
    meta: {
      requiresAuth: false,
      isUserPortal: true,
    },
  },
  {
    path: '/admin-tools',
    name: 'Admin Tools',
    component: AdminTools,
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/account-details',
    name: 'user_account',
    component: userPortalAccountDetails,
    meta: {
      requiresAuth: true,
      isUserPortal: true,
    },
  },
  {
    path: '/account-security',
    name: 'user_account_security',
    component: userPortalAccountSecurity,
    meta: {
      requiresAuth: true,
      isUserPortal: true,
    },
  },
  {
    path: '/update-details/:id',
    name: 'update_details',
    component: ConfirmUpdateDetails,
    meta: {
      requiresAuth: false,
      isUserPortal: true,
    },
  },
];

export function getSubdomain() {
  const host = window.location.host;
  const hostParts = host.split('.');
  // Assuming your development environment URL structure is `subdomain.domain.tld:port`
  // Adjust the index or logic if your URL structure differs
  return hostParts.length > 2 ? hostParts[0] : null;
}

// // Sets the users portal type based on the endpoint.
// export function setPortalType(pinia: Pinia): void {
//   const host = window.location.hostname;
//   const authenticationStore = useAuthenticationStore(pinia);
//   if (!authenticationStore) {
//     return;
//   }
//   if (host.substring(0, 7) === 'client.') {
//     authenticationStore.setPortal(Portal.CLIENT);
//   } else if (host.substring(0, 11) === 'userportal.') {
//     authenticationStore.setPortal(Portal.USER);
//   } else {
//     authenticationStore.setPortal(Portal.OPERATIONS);
//   }
// }

/**
 * Find the users company details based on the users endpoint. This request is made to get information about the users company before they are authenticated.
 */
export async function getCompanyDetails(pinia: Pinia) {
  try {
    const ENV: string | undefined = Environment.value('environment');
    let companyDomain = window.location.hostname;
    if (companyDomain.substring(0, 7) === 'client.') {
      companyDomain = companyDomain.substring(7);
    } else if (companyDomain.substring(0, 11) === 'userportal.') {
      companyDomain = companyDomain.substring(11);
    }
    Mitt.emit('isLoadingInitialCompanyDetails', true);
    // useAuthenticationStore(pinia).setIsRequestingCompanyDetailsViaUrl(true);
    const http = useHttp();
    // http response data contains CompanyDetails. This company details response does not include divisions list.
    const clientCompanyRequest = await http.get<CompanyDetails>(
      'app/company/getCompanyDetails/' + companyDomain,
    );
    if (!clientCompanyRequest.ok) {
      if (ENV === 'local' || ENV === 'staging') {
        console.error(
          'No company associated with current endpoint: ' + companyDomain,
        );
      }
      Mitt.emit('isLoadingInitialCompanyDetails', false);
      // useAuthenticationStore(pinia).setIsRequestingCompanyDetailsViaUrl(false);
      return;
    } else if (clientCompanyRequest.data && clientCompanyRequest.data.name) {
      if (clientCompanyRequest.data.name) {
        useAppNavigationStore(pinia).setPageTitle(
          `${clientCompanyRequest.data.name} (powered by GoDesta)`,
        );
      }
      useCompanyDetailsStore(pinia).setCompanyDetails(
        clientCompanyRequest.data,
      );
    }
    Mitt.emit('isLoadingInitialCompanyDetails', false);
    // useAuthenticationStore(pinia).setIsRequestingCompanyDetailsViaUrl(false);
  } catch (e) {
    console.error(e);
  }
}

// Return boolean on whether the user is authenticated and on an active websocket connection.
export function isAuthenticatedUser(pinia: Pinia): boolean {
  const SESSION_STORAGE_TOKEN = 'token';
  const SESSION_STORAGE_MULTI_TOKEN = 'multiToken';

  try {
    const multiToken = sessionStorage.getItem(SESSION_STORAGE_MULTI_TOKEN);
    if (multiToken !== null) {
      useWebsocketStore(pinia).setMultiAuthToken(multiToken);
    }
    const token = sessionStorage.getItem(SESSION_STORAGE_TOKEN);
    // Validate the token's expiry time. Remove tokens from sessionStorage if
    // the token is expired or expiring soon
    if (isTokenValid(token)) {
      const websocketConnected = useWebsocketStore(pinia).websocket
        ? true
        : false;
      if (!websocketConnected) {
        const headers = { accessToken: token, id: 0 };
        authenticateUser(headers);
      }
      return true;
    } else {
      sessionStorage.removeItem(SESSION_STORAGE_MULTI_TOKEN);
      sessionStorage.removeItem(SESSION_STORAGE_TOKEN);
      return false;
    }
  } catch (e) {
    console.error(e);
    return false;
  }
}
