import { AddressingEvents } from '@/store/events/AddressingEvents';
import { AdjustmentChargeEvents } from '@/store/events/AdjustmentChargeEvents';
import { AllocationEvents } from '@/store/events/AllocationEvents';
import { AppNavigationEvents } from '@/store/events/AppNavigationEvents';
import { AttachmentEvents } from '@/store/events/AttachmentEvents';
import { AuthenticationEvents } from '@/store/events/AuthenticationEvents';
import { ClientDetailsEvents } from '@/store/events/ClientDetailsEvents';
import { ClientInvoiceEvents } from '@/store/events/ClientInvoiceEvents';
import { ClientPortalEvents } from '@/store/events/ClientPortalEvents';
import { CompanyDetailsEvents } from '@/store/events/CompanyDetailsEvents';
import { DataImportEvents } from '@/store/events/DataImportEvents';
import { DriverAppEvents } from '@/store/events/DriverAppEvents';
import { DriverDetailsEvents } from '@/store/events/DriverDetailsEvents';
import { DriverMessageEvents } from '@/store/events/DriverMessageEvents';
import { EquipmentHireInvoiceEvents } from '@/store/events/EquipmentHireInvoiceEvents';
import { FleetAssetEvents } from '@/store/events/FleetAssetEvents';
import { FleetAssetOwnerEvents } from '@/store/events/FleetAssetOwnerEvents';
import { FleetAssetOwnerInvoiceEvents } from '@/store/events/FleetAssetOwnerInvoiceEvents';
import { FleetMapEvents } from '@/store/events/FleetMapEvents';
import { FuelLevyEvents } from '@/store/events/FuelLevyEvents';
import { GpsEvents } from '@/store/events/GpsEvents';
import { InvoiceAdjustmentEvents } from '@/store/events/InvoiceAdjustmentEvents';
import { JobBookingEvents } from '@/store/events/JobBookingEvents';
import { JobEvents } from '@/store/events/JobEvents';
import { JobStatisticsEvents } from '@/store/events/JobStatisticsEvents';
import { RecurringJobEvents } from '@/store/events/RecurringJobEvents';
import { RootEvents } from '@/store/events/RootEvents';
import { ServiceRateEvents } from '@/store/events/ServiceRateEvents';
import { ServiceRateVariationEvents } from '@/store/events/ServiceRateVariationEvents';
import { SupportTicketEvents } from '@/store/events/SupportTicketEvents';
import { UserActivityEvents } from '@/store/events/UserActivityEvents';
import { ZoneToZoneEvents } from '@/store/events/ZoneToZoneEvents';
import mitt from 'mitt';

export type WebsocketEvents = RootEvents &
  AuthenticationEvents &
  RecurringJobEvents &
  SupportTicketEvents &
  InvoiceAdjustmentEvents &
  FleetAssetEvents &
  DriverAppEvents &
  FuelLevyEvents &
  ServiceRateEvents &
  AllocationEvents &
  JobEvents &
  CompanyDetailsEvents &
  ClientDetailsEvents &
  DriverDetailsEvents &
  FleetAssetOwnerEvents &
  AdjustmentChargeEvents &
  DataImportEvents &
  DriverMessageEvents &
  JobStatisticsEvents &
  GpsEvents &
  AddressingEvents &
  AttachmentEvents &
  AppNavigationEvents &
  UserActivityEvents &
  ClientInvoiceEvents &
  FleetAssetOwnerInvoiceEvents &
  EquipmentHireInvoiceEvents &
  FleetMapEvents &
  ClientPortalEvents &
  JobBookingEvents &
  ZoneToZoneEvents &
  ServiceRateVariationEvents;

const Mitt = mitt<WebsocketEvents>();

export default Mitt;
