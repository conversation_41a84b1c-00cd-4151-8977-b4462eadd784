import {
  returnEndOfDayFromEpoch,
  returnFormattedDate,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  getDriverFromDriverId,
  getFleetAssetFromFleetAssetId,
  getOwnerFromOwnerId,
  getPudOrderBasedOnEventTimes,
} from '@/helpers/JobDataHelpers/JobDataHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { requestAdditionalTravelMatrixData } from '@/helpers/RouteHelpers/AdditionalTravelHelpers';
import { setJobRouteProgress } from '@/helpers/RouteHelpers/JobRouteHelpers';
import {
  getServiceTypeById,
  returnServiceTypeLongNameFromId,
  returnServiceTypeShortNameFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import {
  returnClientCurrentExactJobStatus,
  returnCurrentExactJobStatus,
} from '@/helpers/StatusHelpers/StatusHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import RetrieveGpsDataRequest from '@/interface-models/Generic/Position/RetrieveGpsDataRequest';
import ORSRoute from '@/interface-models/Generic/Route/ORSRoute';
import {
  ServiceTypeRates,
  serviceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import timeDefinitions from '@/interface-models/Generic/TimeDefinitions/TimeDefinitions';
import JobDistanceTravelled from '@/interface-models/Jobs/JobDistanceTravelled';
import { ClientPerson } from '@/interface-models/User/ClientPerson';
import { useAddressingStore } from '@/store/modules/AddressingStore';
import { useClientPortalStore } from '@/store/modules/ClientPortalStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useGpsStore } from '@/store/modules/GpsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useRootStore } from '@/store/modules/RootStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { ClientShort } from '../Client/ClientDetails/ClientShort';
import { AdditionalChargeList } from '../Generic/Accounting/AdditionalChargeList';
import { JobAccountingDetails } from '../Generic/Accounting/JobAccountingDetails';
import JobPrimaryRate from '../Generic/Accounting/JobPrimaryRate';
import AddressAU from '../Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import Attachment from '../Generic/Attachment/Attachment';
import Communication from '../Generic/Communication/Communication';
import Dimensions from '../Generic/Dimensions/Dimensions';
import { Portal } from '../Generic/Portal';
import GpsMarkerDetails from '../Generic/Position/GpsMarkerDetails';
import ProofOfDelivery from '../Generic/ProofOfDelivery/ProofOfDelivery';
import ServiceTypeObject from '../Generic/ServiceTypes/ServiceTypeObject';
import ClientFuelSurchargeRate from '../ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '../ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import RateTableItems from '../ServiceRates/RateTableItems';
import StatusConfig from '../Status/StatusConfig';
import { AccountingStatus } from './AccountingStatus';
import AdditionalAsset from './AdditionalAsset';
import { AdditionalJobData } from './AdditionalJobData/AdditionalJobData';
import { CashSaleClientDetails } from './CashSalesDetails/CashSalesDetails';
import { JobStatusUpdate } from './Event/JobStatusUpdate';
import JobReferenceDetails from './JobReferenceDetails';
import { JobSourceType } from './JobSourceType';
import LegDuration from './LegDuration';
import { PUDItem } from './PUD/PUDItem';
import { WorkStatus } from './WorkStatus';
import { JobRangeDeterminantValues } from '@/interface-models/ServiceRates/FuelSurcharge/JobRangeDeterminantValues';

interface JobDetailsInterface {
  _id?: string;
  company: string;
  division: string;
  jobId?: number;
  client: ClientShort;
  clientDispatcher: ClientPerson;
  serviceTypeObject: ServiceTypeObject;
  serviceTypeId: number;
  jobReference: JobReferenceDetails[];
  pudItems: PUDItem[];
  recurringJobReference: string | null;
  recurringJobId: string | null;
  additionalEquipments: number[];
  plannedRoute: ORSRoute;
  attachments: Attachment[];
  notes: Communication[];
  statusList: number[];
  driverId: string;
  fleetAssetId: string;
  additionalAssets: AdditionalAsset[];
  accounting: JobAccountingDetails;
  eventList: JobStatusUpdate[];
  proofOfDelivery: ProofOfDelivery;
  legDurations: LegDuration;
  cashSaleClientDetails: CashSaleClientDetails | null;
  jobSourceType: JobSourceType | null;
  workStatus: WorkStatus;
  exportType: JobSourceType | null;
  serviceFailure: boolean;
  revenueStatus: AccountingStatus;
  expenseStatus: AccountingStatus;
  createdDate: number;

  workDate: number;
  distanceTravelled?: JobDistanceTravelled;
  // additionalJobData IS A FRONT END PROPERTY ONLY.
  additionalJobData: AdditionalJobData | undefined;
  isDirectToInvoice: boolean;
}

export class JobDetails implements JobDetailsInterface {
  public distanceTravelled?: JobDistanceTravelled | undefined;
  constructor(
    public _id?: string,
    public company: string = '',
    public division: string = '',
    public jobId?: number,
    public client: ClientShort = new ClientShort(),
    public clientDispatcher: ClientPerson = new ClientPerson(),
    public serviceTypeObject: ServiceTypeObject = new ServiceTypeObject(),
    public serviceTypeId: number = 0,
    public jobReference: JobReferenceDetails[] = [new JobReferenceDetails()],
    public pudItems: PUDItem[] = [],
    public recurringJobReference: string | null = null,
    public recurringJobId: string | null = null,
    public additionalEquipments: number[] = [],
    public plannedRoute: ORSRoute = new ORSRoute(),
    public attachments: Attachment[] = [],
    public notes: Communication[] = [],
    public statusList: number[] = [25],
    public driverId: string = '',
    public fleetAssetId: string = '',
    public additionalAssets: AdditionalAsset[] = [],
    public accounting: JobAccountingDetails = new JobAccountingDetails(),
    public eventList: JobStatusUpdate[] = [],
    public proofOfDelivery: ProofOfDelivery = new ProofOfDelivery(),
    public legDurations: LegDuration = new LegDuration(),
    public cashSaleClientDetails: CashSaleClientDetails | null = null,
    public jobSourceType: JobSourceType | null = null,
    public workStatus: WorkStatus = WorkStatus.BOOKED,
    public exportType: JobSourceType | null = null,
    public serviceFailure: boolean = false,
    public isDirectToInvoice: boolean = false,
    public createdDate: number = moment().valueOf(),
    // Read-only field - backend sets this and frontend should not change it
    public readonly workDate: number = moment().valueOf(),
    public revenueStatus: AccountingStatus = AccountingStatus.NOT_ACTIONED,
    public expenseStatus: AccountingStatus = AccountingStatus.NOT_ACTIONED,
    public additionalJobData:
      | AdditionalJobData
      | undefined = new AdditionalJobData(),
  ) {}

  get displayId(): string {
    if (this.recurringJobId) {
      return this.recurringJobId;
    } else {
      return `${this.jobId ? this.jobId : '-'}`;
    }
  }

  get isCashSale(): boolean {
    return this.client.id === 'CS';
  }

  // Return the most recent event from the eventList that matches the supplied eventString
  // If pudId is supplied, search for either an ARRIVED or FINISHED event for a PUD Item
  // If validatePudStatus is true, then return undefined if the most recent PUD status is not the same as the associated PUDItem.status
  public returnSpecifiedEvent(
    eventString: string,
    pudId: string = '',
    validatePudStatus: boolean = true,
  ): JobStatusUpdate | undefined {
    const events: JobStatusUpdate[] =
      pudId === ''
        ? this.eventList
        : this.eventList.filter((e) => e.pudId === pudId);

    const foundEvent: JobStatusUpdate[] = events.filter(
      (event) => event.updatedStatus === eventString,
    );

    let latestEvent =
      foundEvent.length > 0 ? foundEvent[foundEvent.length - 1] : undefined;

    if (latestEvent && validatePudStatus) {
      const pudItem =
        pudId === ''
          ? undefined
          : this.pudItems.find((x: PUDItem) => x.pudId === pudId);

      switch (eventString) {
        case 'FINISHED':
          if (pudItem && pudItem.status !== 'FINISHED') {
            latestEvent = undefined;
          }
          break;
        case 'ARRIVED':
          if (pudItem && !pudItem.status) {
            latestEvent = undefined;
          }
          break;
      }
    }
    return latestEvent;
  }

  public pudListFromPartialData() {
    for (const pud of this.pudItems) {
      if (!pud.createdByDriver) {
        continue;
      }

      pud.pudId = pud.pudId !== null ? pud.pudId : '';
      pud.legTypeFlag = pud.legTypeFlag !== null ? pud.legTypeFlag : '';
      pud.pickupTime = pud.pickupTime !== null ? pud.pickupTime : '';
      pud.pickupDate = pud.pickupDate !== null ? pud.pickupDate : null;
      pud.epochTime = pud.epochTime !== null ? pud.epochTime : 0;
      pud.timeDefinition = pud.timeDefinition !== null ? pud.timeDefinition : 0;
      pud.loadTime = pud.loadTime !== null ? pud.loadTime : 1800000;
      pud.address = pud.address !== null ? pud.address : new AddressAU();
      pud.notes = pud.notes !== null ? pud.notes : [];
      pud.siteContactName =
        pud.siteContactName !== null ? pud.siteContactName : '';
      pud.siteContactLandLineNumber =
        pud.siteContactLandLineNumber !== null
          ? pud.siteContactLandLineNumber
          : '';
      pud.siteContactMobileNumber =
        pud.siteContactMobileNumber !== null ? pud.siteContactMobileNumber : '';
      pud.pickupReference =
        pud.pickupReference !== null ? pud.pickupReference : [];
      pud.dropoffReference =
        pud.dropoffReference !== null ? pud.dropoffReference : [];
      pud.customerDeliveryName =
        pud.customerDeliveryName !== null ? pud.customerDeliveryName : '';
      pud.payloadDescription =
        pud.payloadDescription !== null ? pud.payloadDescription : '';
      pud.dimensions =
        pud.dimensions !== null ? pud.dimensions : new Dimensions();
      pud.weight = pud.weight !== null ? pud.weight : 0;
      pud.attachments = pud.attachments !== null ? pud.attachments : [];
      pud.zoneReference = pud.zoneReference !== null ? pud.zoneReference : -1;
      pud.status = pud.status !== null ? pud.status : null;
      pud.createdByDriver =
        pud.createdByDriver !== null ? pud.createdByDriver : false;
    }
  }

  get serviceTypeLongName(): string {
    return returnServiceTypeLongNameFromId(this.serviceTypeId);
  }
  get serviceTypeShortName(): string {
    return returnServiceTypeShortNameFromId(this.serviceTypeId);
  }
  // Return the serviceTypeId from fleetAsset accounting.
  get fleetAssetServiceTypeId(): number | null {
    const serviceTypeId =
      this.accounting?.fleetAssetRates[0]?.rate?.serviceTypeId ?? null;
    return serviceTypeId !== null && serviceTypeId !== 4
      ? serviceTypeId
      : this.serviceTypeId;
  }

  // Return the rateTypeId from fleetAsset accounting.
  get fleetAssetRateTypeId(): number | null {
    const rateTypeId =
      this.accounting?.fleetAssetRates[0]?.rate?.rateTypeId ?? null;
    return rateTypeId !== null && rateTypeId !== 4
      ? rateTypeId
      : this.serviceTypeObject.rateTypeId;
  }

  get fleetAssetServiceTypeShortName(): string {
    const serviceTypeId =
      this.accounting?.fleetAssetRates[0]?.rate?.serviceTypeId ?? null;

    if (!serviceTypeId) {
      return this.serviceTypeShortName;
    }
    const foundServiceType = getServiceTypeById(serviceTypeId);
    return foundServiceType?.shortServiceTypeName ?? '-';
  }
  get fleetAssetServiceTypeLongName(): string {
    const serviceTypeId =
      this.accounting?.fleetAssetRates[0]?.rate?.serviceTypeId ?? null;

    if (!serviceTypeId) {
      return this.serviceTypeLongName;
    }
    const foundServiceType = getServiceTypeById(serviceTypeId);
    return foundServiceType?.longServiceTypeName ?? '-';
  }

  get fleetAssetRateTypeName(): string {
    const rateTypeId =
      this.accounting?.fleetAssetRates[0]?.rate?.rateTypeId ?? null;

    if (!rateTypeId) {
      return this.rateTypeName;
    }
    return (
      serviceTypeRates.find(
        (item: ServiceTypeRates) => item.rateTypeId === rateTypeId,
      )?.longName ?? '-'
    );
  }

  get rateTypeId(): number {
    return this.serviceTypeObject.rateTypeId;
  }
  set rateTypeId(value: number) {
    this.serviceTypeObject.rateTypeId = value;
  }

  get rateTypeName() {
    const rateType = serviceTypeRates.find(
      (item: ServiceTypeRates) =>
        item.rateTypeId === this.serviceTypeObject.rateTypeId,
    );

    return rateType?.longName || '-';
  }

  get numberOfLegs(): number {
    return this.pudItems.filter(
      (x: PUDItem) => x.legTypeFlag === 'P' || x.legTypeFlag === 'D',
    ).length;
  }

  get isOutsideHire(): boolean {
    return (
      this.additionalJobData !== undefined &&
      this.additionalJobData.isOutsideHire
    );
  }

  get driverName() {
    if (
      this.additionalJobData !== undefined &&
      this.additionalJobData.driverName &&
      this.additionalJobData.driverName !== '-'
    ) {
      return this.additionalJobData.driverName;
    }
    const driverDetails = getDriverFromDriverId(this.driverId);
    return driverDetails?.displayName ?? '-';
  }

  get requiresApproval(): boolean {
    const bookedByClient: boolean = this.statusList.includes(57);
    const isCompletedActionRequired: boolean = this.statusList.includes(45);
    const hasStopsMissingData: boolean = this.pudItems.some(
      (pud) => pud.createdByDriver,
    );
    return bookedByClient || isCompletedActionRequired || hasStopsMissingData;
  }

  get currentJobStatus() {
    const jobStatuses = useRootStore()
      .statusTypeList.filter((x: StatusConfig) => x.category.includes(5))
      .map((s: StatusConfig) => s.sid);

    const statusIndex = this.statusList.findIndex((statusId) =>
      jobStatuses.includes(statusId),
    );
    if (statusIndex === -1) {
      return '-';
    }
    if (this.statusList.includes(57)) {
      return 'Pending';
    } else if (
      this.workStatus >= WorkStatus.BOOKED &&
      this.workStatus <= WorkStatus.ACCEPTED
    ) {
      return 'Booked';
    } else if (this.workStatus === WorkStatus.IN_PROGRESS) {
      return 'In-progress';
    } else if (this.workStatus === WorkStatus.DRIVER_COMPLETED) {
      return 'Completed - Pending Review';
    } else if (this.workStatus >= WorkStatus.COMPLETED) {
      return 'Completed';
    } else {
      return '-';
    }
  }

  // If the first pud has been started, we should use this as the jobDate (as it more accurately portrays when the job was actually completed)
  // If it has not been started, we should use the first pud item epoch time
  get jobDate() {
    if (this.jobRunEpoch !== undefined) {
      return returnFormattedDate(this.jobRunEpoch, 'DD/MM/YY');
    } else {
      return '-';
    }
  }
  // If the first pud has been started, we should use this as the jobDate (as it
  // more accurately portrays when the job was actually completed). If it has not
  // been started, we should use the first pud item epoch time
  get jobRunEpoch(): number {
    let jobStartDate: number;
    if (!this.pudItems[0]) {
      jobStartDate = moment().valueOf();
    } else {
      // Find first arrived event
      const firstPudId = this.pudItems[0].pudId;
      const firstArrivedEvent = this.returnSpecifiedEvent(
        'ARRIVED',
        firstPudId,
      );

      if (firstArrivedEvent && firstArrivedEvent.correctEventTime !== 0) {
        jobStartDate = firstArrivedEvent.correctEventTime;
      } else {
        jobStartDate = this.pudItems[0].epochTime
          ? this.pudItems[0].epochTime
          : moment().valueOf();
      }
    }
    return jobStartDate;
  }

  // Returns the FINISHED event time of the last pud, or the epoch time of the
  // last pud if no such event exists
  get jobFinishEpoch(): number {
    const lastPud = this.pudItems[this.pudItems.length - 1];
    if (!lastPud) {
      return moment().valueOf();
    }
    const lastFinishedEvent = this.returnSpecifiedEvent(
      'FINISHED',
      lastPud.pudId,
    );
    if (lastFinishedEvent && lastFinishedEvent.correctEventTime !== 0) {
      return lastFinishedEvent.correctEventTime;
    }
    return lastPud.epochTime ? lastPud.epochTime : moment().valueOf();
  }

  get csrAssignedId() {
    if (
      this.additionalJobData !== undefined &&
      this.additionalJobData.csrAssignedId &&
      this.additionalJobData.csrAssignedId !== '-'
    ) {
      return this.additionalJobData.csrAssignedId;
    }
    const fleetAssetStore = useFleetAssetStore();
    const csrAssignedId = fleetAssetStore.csrAssignedIdMap.get(
      this.fleetAssetId,
    );
    return csrAssignedId ? csrAssignedId : '-';
    return '';
  }

  get fromSuburb() {
    return this.pudItems[0] ? this.pudItems[0].address.suburb : '';
  }

  get toSuburb() {
    return this.pudItems[this.pudItems.length - 1]
      ? this.pudItems[this.pudItems.length - 1].address.suburb
      : '';
  }

  get allJobReferences() {
    if (this.jobReference && this.jobReference.length > 0) {
      return this.jobReference
        .map((ref) => ref?.reference ?? '')
        .filter((reference) => reference !== '')
        .join(', ');
    }
    return '';
  }

  get completedLegs(): string {
    const pickUpsAndDropOffs = this.pudItems.filter(
      (x: PUDItem) => x.legTypeFlag === 'P' || x.legTypeFlag === 'D',
    );
    const legsLength = pickUpsAndDropOffs.length;
    const completedLegs = pickUpsAndDropOffs.filter(
      (x: PUDItem) => x.status === 'FINISHED',
    ).length;
    return completedLegs + ' of ' + legsLength;
  }

  public addAdditionalJobData(): void {
    let csrAssignedId = '-';
    let vehicleRegistrationDetails = '-';
    // let vehiclePayloadWeight = '-';
    // let vehicleDimensions = '-';
    let isOutsideHire = false;
    let ownerName = '-';
    let ownerAffiliationId = '-';
    let manualProgressionRequired = false;
    const fleetAsset: FleetAssetSummary | undefined =
      getFleetAssetFromFleetAssetId(this.fleetAssetId);
    if (fleetAsset) {
      const ownerDetails: FleetAssetOwnerSummary | undefined =
        getOwnerFromOwnerId(fleetAsset.fleetAssetOwnerId);
      isOutsideHire = ownerDetails ? ownerDetails.isOutsideHire : false;

      csrAssignedId = fleetAsset.csrAssignedId;
      vehicleRegistrationDetails = isOutsideHire
        ? 'OUTSIDE HIRE'
        : fleetAsset.registrationNumber;

      // vehiclePayloadWeight = fleetAsset.payload
      //   ? `Payload (kg): ${fleetAsset.payload}`
      //   : 'Payload (kg): -';

      const foundOwner = getOwnerFromOwnerId(fleetAsset.fleetAssetOwnerId);
      if (foundOwner) {
        ownerName = foundOwner.name;
        ownerAffiliationId = foundOwner.affiliation;
      }
      // const dim: Dimensions = fleetAsset.dimensions;
      // vehicleDimensions = `Dimensions(m): ${
      //   dim.length ? dim.length : '-'
      // } (L) x ${dim.width ? dim.width : '-'} (W) x ${
      //   dim.height ? dim.height : '-'
      // } (H)`;
    }
    let driverName = '-';
    let driverContactNumber = '-';
    const driverDetails = getDriverFromDriverId(this.driverId);
    if (driverDetails) {
      if (isOutsideHire) {
        driverName = 'O/H: ' + ownerName;
      } else {
        driverName = driverDetails.displayName;
      }
      if (driverDetails.mobile) {
        driverContactNumber = driverDetails.mobile;
      }
      manualProgressionRequired = !driverDetails.mobileAppRequired;
    }

    this.additionalJobData = new AdditionalJobData(
      driverName,
      driverContactNumber,
      csrAssignedId,
      vehicleRegistrationDetails,
      isOutsideHire,
      ownerName,
      ownerAffiliationId,
      manualProgressionRequired,
    );
  }

  public removeAdditionalJobData(): void {
    delete this.additionalJobData;

    // removes client side unique ids from each pudItem.
    for (const pud of this.pudItems) {
      delete pud.uniqueId;
    }
  }

  /**
   * Called from JobDetailsDialog. Fetches the planned route using this job's
   * id, sets it to the object, and returns it.
   * @returns
   */
  public async requestCurrentRouteData(): Promise<ORSRoute | null> {
    const result = await useJobStore().getPlannedRouteByJobId(this.jobId);
    if (result?.orsRoute) {
      this.plannedRoute = result.orsRoute;
    }
    return result?.orsRoute ?? null;
  }

  public async getPlannedRoute(): Promise<ORSRoute | null> {
    const points: number[][] = [];
    if (this.pudItems.length > 1) {
      let atLeastOneInvalidAddress = false;
      for (const pudItem of this.pudItems) {
        // Validate address for presence of geoLocation or addressId
        const addressIsValid = pudItem.addressIsValid;
        if (addressIsValid) {
          const point: number[] = [
            pudItem.address.geoLocation[0],
            pudItem.address.geoLocation[1],
          ];
          points.push(point);
        } else {
          // Log error and set flag to show notification
          console.error(
            'Invalid address found in planned route',
            pudItem,
            `addressIsValid: ${addressIsValid}`,
          );
          atLeastOneInvalidAddress = true;
          break;
        }
      }
      // If any address is invalid, show a notification and return null
      if (atLeastOneInvalidAddress) {
        showNotification(
          'One or more legs has an invalid address. Please review.',
        );
        return null;
      }
      const result = await useAddressingStore().getPlannedRoute(points);
      return result?.response ?? null;
    } else {
      this.plannedRoute = new ORSRoute();
      return this.plannedRoute;
    }
  }

  // Send matrix request to our routing service
  public async getRouteProgressMatrix() {
    try {
      // first check if the job is complete or the last stop has a status
      // defined. If the condition is true we have all the information that is
      // required on the job itself and no matrix request is required.
      if (
        this.workStatus >= WorkStatus.DRIVER_COMPLETED ||
        this.pudItems[this.pudItems.length - 1].status
      ) {
        setJobRouteProgress(this, null, true);
        return;
      }
      const points: number[][] = [];
      if (this.pudItems.length === 0) {
        return;
      }
      // We do not require finished stops as all variables for calculations are
      // present on the job.
      const stops = this.pudItems.filter(
        (stop: PUDItem) =>
          (stop.legTypeFlag === 'P' || stop.legTypeFlag === 'D') && stop.pudId,
      );
      const currentTime = moment().valueOf();

      const isClientPortal = sessionManager.getPortalType() === Portal.CLIENT;

      //  SET MOCK GPS COORDINATE (TOOWONG QLD LOCATION). USED FOR TESTING ONLY
      // if (this.driverId && this.fleetAssetId) {
      //   const mockGpsCoordinates: any = {
      //     latitude: -27.48115544,
      //     longitude: 152.98416478,
      //     timestamp: currentTime
      //   };

      //   if (!isClientPortal) {
      //     useRootStore(). allGpsPositions.set(
      //       this.fleetAssetId,
      //       mockGpsCoordinates
      //     );
      //   } else {
      //     useClientPortalStore().allGpsPositions.set(
      //       this.fleetAssetId,
      //       mockGpsCoordinates
      //     );
      //   }
      // }

      //  Check if the job is started and also check if any puds have arrived status. We will use these values to find an initial GPS coordinates to calculate durations
      const jobStarted: boolean = this.workStatus === WorkStatus.IN_PROGRESS;
      const arrivedPudItems = stops.filter(
        (x: PUDItem) => x.status === 'ARRIVED' || x.status === 'FINISHED',
      );
      // get the drivers GPS coordinates
      const driverCoordinates: GpsMarkerDetails | undefined = !isClientPortal
        ? this.fleetAssetId
          ? useGpsStore().allGpsPositions.get(this.fleetAssetId)
          : undefined
        : this.fleetAssetId
          ? useClientPortalStore().allGpsPositions.get(this.fleetAssetId)
          : undefined;

      // If the drivers gps coordinates are greater then 20 minutes old we will consider them old and invalid
      const driversCoordinatesValid: boolean =
        driverCoordinates && driverCoordinates.timestamp
          ? currentTime - driverCoordinates.timestamp < 60 * 20 * 1000
          : false;

      // If the drivers Gps coordinates are valid and the job has started but no stops are yet actioned we use the the drivers gps coordinates as the starting location
      if (
        jobStarted &&
        points.length === 0 &&
        driverCoordinates &&
        driversCoordinatesValid &&
        arrivedPudItems.length < 1 &&
        driverCoordinates.latitude &&
        driverCoordinates.longitude
      ) {
        // add drivers gps coordinates to points array
        points.push([driverCoordinates.longitude, driverCoordinates.latitude]);
      }

      for (let i = 0; i < stops.length; i++) {
        // This pudItem in iteration
        const pudItem: PUDItem = stops[i];

        if (i < stops.length - 1 && stops[i + 1].status) {
          continue;
        }
        // Gps coordinates for this pudItem
        const geoLocation: number[] = pudItem.address.geoLocation;
        // validate address
        if (!pudItem.addressIsValid) {
          return;
        }
        // find finished event for this pud
        const finishedEvent: JobStatusUpdate | undefined =
          this.returnSpecifiedEvent('FINISHED', pudItem.pudId);

        if (finishedEvent) {
          // check that this pud is not the last
          if (i === stops.length - 1) {
            points.push(geoLocation);
            continue;
          }
          const nextPudId = stops[i + 1].pudId;
          const nextPudArrivedEvent: JobStatusUpdate | undefined =
            this.returnSpecifiedEvent('ARRIVED', nextPudId);
          // If current iteration pud item is finished and the next pud is not yet arrived, we should get the driver's GPS coordinates.
          if (!nextPudArrivedEvent) {
            // If driver coordinates exist and they are later than the finished event time, we add the driver's GPS coordinates instead of the pud coordinate.
            if (
              driverCoordinates &&
              finishedEvent.correctEventTime < driverCoordinates.timestamp &&
              driverCoordinates.latitude &&
              driverCoordinates.longitude
            ) {
              points.push([
                driverCoordinates.longitude,
                driverCoordinates.latitude,
              ]);
            } else {
              points.push(geoLocation);
            }
          } else {
            points.push(geoLocation);
          }
        }
        if (!finishedEvent) {
          points.push(geoLocation);
        }
      }

      if (points.length > 1) {
        const result = await useAddressingStore().getMatrixInfo(points);
        if (result) {
          setJobRouteProgress(this, result.response);
        }
      } else if (points.length === 1) {
        setJobRouteProgress(this, null, false, true);
      }
    } catch (error) {
      console.log(error);
      return;
    }
  }

  public setPudStandbyRates(clientCommonAddressList: ClientCommonAddress[]) {
    // check if pud item is a nicknamed address. If true check that standby rate is required
    for (const pud of this.pudItems) {
      const longitude = pud.address.geoLocation[0];
      const latitude = pud.address.geoLocation[1];
      const nicknameAddress = clientCommonAddressList.find(
        (address: ClientCommonAddress) =>
          address.address.geoLocation[0] === longitude &&
          address.address.geoLocation[1] === latitude,
      );
      if (nicknameAddress && nicknameAddress.isStandbyRate) {
        pud.isStandbyRate = nicknameAddress.isStandbyRate;
      } else {
        pud.isStandbyRate = false;
      }
    }
  }

  /**
   * Adds or updates the client fuel surcharge rate in the accounting's
   * additional charges.
   *
   * If the `additionalCharges` property does not exist, it initializes it as a
   * new `AdditionalChargeList`. Then, it sets the `clientFuelSurcharge`
   * property to the provided `fuelSurcharge` value, or to `undefined` if
   * `fuelSurcharge` is `null`.
   *
   * @param fuelSurcharge - The client fuel surcharge rate to add, or `null` to
   * remove it.
   */
  public addClientFuelSurchargeRate(
    fuelSurcharge: ClientFuelSurchargeRate | null,
  ) {
    if (!this.accounting.additionalCharges) {
      this.accounting.additionalCharges = new AdditionalChargeList();
    }
    this.accounting.additionalCharges.clientFuelSurcharge = fuelSurcharge
      ? fuelSurcharge
      : undefined;
  }

  public addClientRateToJob(
    serviceRate: ClientServiceRate,
    rateToApply: RateTableItems,
  ) {
    const clientRates =
      this.accounting.clientRates.length < 1
        ? new JobPrimaryRate()
        : this.accounting.clientRates[0];

    // const clientRates: JobPrimaryRate = new JobPrimaryRate();
    clientRates.rate = rateToApply;
    clientRates.validFromDate =
      serviceRate && serviceRate.validFromDate
        ? serviceRate.validFromDate
        : null;
    clientRates.validToDate =
      serviceRate && serviceRate.validToDate ? serviceRate.validToDate : null;
    clientRates.rateTableName = serviceRate ? serviceRate.name : '';
    this.accounting.clientRates = [];
    this.accounting.clientRates.push(clientRates);
  }

  get currentExactJobStatus(): string {
    return returnCurrentExactJobStatus(
      this.workStatus,
      this.revenueStatus,
      this.expenseStatus,
    );
  }

  get clientCurrentExactJobStatus(): string {
    return returnClientCurrentExactJobStatus(this.workStatus);
  }

  // Returns the most recent dispatch note
  get dispatchNote(): Communication | undefined {
    if (!this.notes) {
      return;
    }
    // Filter for Job Notes only then sort
    const dispatchNotes = this.notes.filter(
      (note) =>
        note.type?.id === 3 &&
        note.type.communicationDetails.visibleTo.includes(7),
    );
    if (dispatchNotes && dispatchNotes.length > 0) {
      return dispatchNotes.reduce((p, c) => (p.epoch > c.epoch ? p : c));
    }
  }

  // Update times for pudItems using the route response. Called in Book Job
  // screen and Rebook Job Dialog components.
  public setPudTimesFromPlannedRoute(plannedRoute: ORSRoute) {
    const firstPudTimeDefinition = this.pudItems[0].timeDefinition;
    const firstPudArrivalTime = this.pudItems[0].epochTime;

    this.pudItems.forEach((pudItem: PUDItem, index: number) => {
      if (index === 0) {
        return;
      }
      const previousPudArrivalTime = this.pudItems[index - 1].epochTime;
      const previousPudLoadTime = this.pudItems[index - 1].loadTime;
      if (
        !plannedRoute ||
        !plannedRoute.routes ||
        !plannedRoute.routes[0] ||
        !plannedRoute.routes[0].segments ||
        !pudItem.epochTime ||
        !previousPudArrivalTime
      ) {
        return;
      }

      const travelDurationInMilliseconds: number = moment
        .duration(
          plannedRoute.routes[0].segments[index - 1].duration,
          'seconds',
        )
        .asMilliseconds();

      if (pudItem.timeDefinition === 0 && firstPudTimeDefinition <= 3) {
        pudItem.epochTime =
          previousPudArrivalTime +
          travelDurationInMilliseconds +
          previousPudLoadTime;
        pudItem.pickupTime = returnFormattedTime(pudItem.epochTime);
        pudItem.pickupDate = returnStartOfDayFromEpoch(pudItem.epochTime);
        return;
      }

      const foundDefinition = timeDefinitions.find(
        (item) => item.id === firstPudTimeDefinition,
      );

      if (firstPudTimeDefinition !== 0) {
        if (
          index === 1 &&
          firstPudArrivalTime &&
          foundDefinition &&
          foundDefinition.end &&
          foundDefinition.start
        ) {
          const difference = foundDefinition.end - foundDefinition.start;
          const endOfFlexTime = firstPudArrivalTime + difference;

          pudItem.epochTime =
            endOfFlexTime + travelDurationInMilliseconds + previousPudLoadTime;

          pudItem.pickupTime = returnFormattedTime(pudItem.epochTime);
          pudItem.pickupDate = returnStartOfDayFromEpoch(pudItem.epochTime);
          return;
        } else {
          pudItem.epochTime =
            previousPudArrivalTime +
            travelDurationInMilliseconds +
            previousPudLoadTime;

          pudItem.pickupTime = returnFormattedTime(pudItem.epochTime);
          pudItem.pickupDate = returnStartOfDayFromEpoch(pudItem.epochTime);

          return;
        }
      }
    });
  }

  /**
   * Use the locations of the pudItems to request the travel time to and from
   * the depot location.
   * @param useExistingDurations If true, we will only set the distances and not
   * the durations (as distances are not currently saved on the job)
   */
  public async requestDepotDurations(
    useExistingDurations: boolean = false,
  ): Promise<void> {
    if (this.pudItems.length < 2) {
      this.legDurations = new LegDuration();
      return;
    }
    const firstPudLocation = this.pudItems[0].address.geoLocation;
    const lastPudLocation =
      this.pudItems[this.pudItems.length - 1].address.geoLocation;
    const matrixData = await requestAdditionalTravelMatrixData(
      firstPudLocation,
      lastPudLocation,
    );
    // Set responses to the job details object
    if (!useExistingDurations && matrixData?.durations) {
      this.setDepotDurations(matrixData.durations);
    }
  }

  /**
   * Handles response to 'requestDepotDurations'. Sets the depot durations to the
   * legDurations property on this JobDetails object.
   * @param durations The durations from the depot to the first PUD, depot to
   * the last PUD, and return to the first PUD.
   */
  public setDepotDurations(durations: number[][]) {
    const asMillis = (secs: number) =>
      Math.trunc(moment.duration(secs, 'seconds').asMilliseconds());
    const depotToFirstPud = asMillis(durations[0][1]);
    const depotToLastPud = asMillis(durations[0][2]);
    const returnToFirstPud = asMillis(durations[2][1]);

    this.legDurations = new LegDuration(
      depotToFirstPud,
      depotToLastPud,
      returnToFirstPud,
    );
  }

  // check if the jobs events match the order of puds defined in the job.
  public isProgressInOrder(): boolean {
    try {
      let isProgressInOrder = true;
      // get a list of arrived pudIds and confirm order against jobs pud list.
      const arrivedEventPudIds: string[] = [];
      for (const event of this.eventList) {
        if (event.pudId && event.updatedStatus === 'ARRIVED') {
          arrivedEventPudIds.push(event.pudId);
        }
      }
      // filter puds by pickup and dropoffs
      const pudItems: PUDItem[] = this.pudItems.filter(
        (pud: PUDItem) => pud.legTypeFlag === 'P' || pud.legTypeFlag === 'D',
      );
      // confirm arrived events pudId matches index of pud in job
      for (let i = 0; i < arrivedEventPudIds.length; i++) {
        if (!isProgressInOrder) {
          break;
        }
        isProgressInOrder = arrivedEventPudIds[i] === pudItems[i].pudId;
      }
      return isProgressInOrder;
    } catch (error) {
      console.error(error);
      return true;
    }
  }

  /**
   * Reorders the puds on this job so they align with the order of the event
   * times.
   */
  public async reorderPudsBasedOnEventTimes(): Promise<ORSRoute | null> {
    this.pudItems = getPudOrderBasedOnEventTimes(this.pudItems, this.eventList);
    return this.getPlannedRoute();
  }

  /**
   * Returns a RetrieveGpsDataRequest object based on the current job details.
   * If all required properties are available, it constructs the request using
   * the first arrived event and final finished event. If only the first arrived
   * event is available, it constructs the request using the arrival time and
   * end of day. Otherwise, it constructs the request using the jobId.
   * @returns The RetrieveGpsDataRequest object or undefined if the required properties are not available.
   */
  public returnJobGpsRequest(): RetrieveGpsDataRequest | undefined {
    if (
      this.jobId &&
      this.pudItems.length &&
      this.driverId &&
      this.fleetAssetId
    ) {
      const validPudItems = this.pudItems.filter((p) =>
        ['P', 'D'].includes(p.legTypeFlag),
      );
      const firstPudId = validPudItems[0].pudId;
      const lastPudId = validPudItems[validPudItems.length - 1].pudId;
      // Get first arrived event and final finished event to find range
      const firstArrivedEvent = this.returnSpecifiedEvent(
        'ARRIVED',
        firstPudId,
      );
      const lastFinishedEvent = this.returnSpecifiedEvent(
        'FINISHED',
        lastPudId,
      );
      let request: RetrieveGpsDataRequest;
      if (firstArrivedEvent && lastFinishedEvent) {
        // Minus 30 seconds off the first arrived event so we have some lead
        // time before arrival
        const thirtyMinsInMs = 30 * 60000;

        // If we find both events, use them as request range
        request = {
          startEpoch: firstArrivedEvent.correctEventTime - thirtyMinsInMs,
          endEpoch: lastFinishedEvent.correctEventTime,
          driverId: this.driverId,
          fleetAssetId: this.fleetAssetId,
        };
      } else if (firstArrivedEvent && !lastFinishedEvent) {
        // If we can only find one event, then use the arrival time and end of day for request range
        request = {
          startEpoch: firstArrivedEvent.correctEventTime,
          endEpoch: returnEndOfDayFromEpoch(firstArrivedEvent.correctEventTime),
          driverId: this.driverId,
          fleetAssetId: this.fleetAssetId,
        };
      } else {
        // Else use the jobId
        request = {
          jobId: this.jobId,
        };
      }
      return request;
    }
  }

  /**
   * Used in the booking screen to determine if the job is a booking from a
   * quote. If the job has an applied quote id, it is a booking from a quote, and should update t
   */
  public get isBookingFromQuote(): boolean {
    return !!this.additionalJobData?.appliedQuoteId;
  }

  /**
   * Returns a JobRangeDeterminantValues containing summaries of distances and
   * time taken, used for computing ranged fuel rates.
   */
  get fuelRangeDeterminantValues(): JobRangeDeterminantValues {
    return {
      planned: this.distanceTravelled?.planned ?? 0,
      gpsEstimate: this.distanceTravelled?.gpsEstimate ?? 0,
      suburbCentres: this.distanceTravelled?.suburbCentres ?? 0,
      timeTaken: this.jobFinishEpoch - this.jobRunEpoch || 0,
    };
  }
}
export default JobDetails;
