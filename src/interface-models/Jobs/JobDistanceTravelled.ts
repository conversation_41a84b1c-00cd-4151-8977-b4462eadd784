export default interface JobDistanceTravelled {
  /**
   * The planned distance for the job in kilometres. This is the distance that was
   * expected to be travelled based on the planned route from the routing service
   */
  planned: number;

  /**
   * The estimated distance travelled for the job in kilometres. This is the
   * distance calculated from the GPS captured throughout the course of a job.
   */
  gpsEstimate: number;

  /**
   * The estimated distance travelled for the job in kilometres, based on the
   * centre of the suburbs that the stops of the job are located in. This is
   * used to provide a more consistent distance measurement such that clients
   * have greater consistency across invoices.
   */
  suburbCentres: number;
}
