import type { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import type { ClientRelatedContact } from '@/interface-models/Client/ClientRelatedContact';
import type { CurrentClientServiceRateResponse } from '@/interface-models/Client/CurrentClientServiceRateResponse';
import type UnassignedPudItem from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItem';
import type { QuoteDetails } from '@/interface-models/Jobs/Quote/QuoteDetails';
import type ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import type { ClientPersonWithAuthDetails } from '@/interface-models/User/ClientPerson';

export interface ClientJobBookingData {
  clientId: string;
  commonAddresses: ClientCommonAddress[] | null;
  relatedContacts: ClientRelatedContact[] | null;
  clientPersons: ClientPersonWithAuthDetails[] | null;
  quoteDetails: QuoteDetails[] | null;
  unassignedPudItems: UnassignedPudItem[] | null;
  currentServiceRates: CurrentClientServiceRateResponse | null;
  currentFuelSurcharges: ClientFuelSurchargeRate[] | null;
  serviceRateVariations: ClientServiceRateVariations[] | null;
  /**
   * Used for booking a job with unassigned PUDs from Point Manager. Set in
   * KeyDetailsDialog (not set in ClientDetailsStore when requesting)
   */
  unassignedPudIds?: string[];
}
