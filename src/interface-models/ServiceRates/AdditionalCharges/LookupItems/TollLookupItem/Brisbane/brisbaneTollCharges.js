const brisbane = [{
		geoPointEastBound: '-27.605823242521442, 152.92095992714167',
		geoPointWestBound: '-27.605898410595742, 152.9207567498088',
		id: 1,
		marker: '-27.605880, 152.920772',
		name: 'Ipswich Motorway',
		group: 'Logan Motorway M2',
		validExits: [{
				code: 'Z',
				direction: 'E',
				heading: 'E',
				id: 2
			},
			{
				code: 'Z',
				direction: 'E',
				heading: 'E',
				id: 3
			},
			{
				code: 'Z',
				direction: 'E',
				heading: 'E',
				id: 4
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 5
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 6
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 7
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 8
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 9
			},
			{
				code: 'H+L2',
				direction: 'E',
				heading: 'E',
				id: 10
			},
			{
				code: 'H+L',
				direction: 'E',
				heading: 'E',
				id: 11
			},
			{
				code: 'H+L',
				direction: 'E',
				heading: 'E',
				id: 12
			},
			{
				code: 'H+CR2',
				direction: 'N',
				heading: 'E',
				id: 13
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 14
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 16
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 17
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 18
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 19
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 20
			},
			{
				code: 'H+K+M',
				direction: 'N',
				heading: 'E',
				id: 22
			},
			{
				code: 'H+K+M',
				direction: 'N',
				heading: 'E',
				id: 23
			},
			{
				code: 'H+K+M',
				direction: 'N',
				heading: 'E',
				id: 24
			}
		]
	},
	{
		geoPointEastBound: '-27.60772679127144, 152.92405486106873',
		geoPointWestBound: '-27.607826617482228, 152.92380273342133',
		id: 2,
		marker: '-27.607773, 152.923931',
		name: 'Formation St',
		group: 'Logan Motorway M2',
		validExits: [{
			code: 'Z',
			direction: 'W',
			heading: 'W',
			id: 1
		}]
	},
	{
		geoPointEastBound: '-27.61202398964358, 152.9306423664093',
		geoPointWestBound: '-27.612179664849837, 152.93059810996056',
		id: 3,

		marker: '-27.612092, 152.930614',
		name: 'Boundary Rd',
		group: 'Logan Motorway M2',
		validExits: [{
				code: 'Z',
				direction: 'W',
				heading: 'W',
				id: 1
			},
			{
				code: 'Z',
				direction: 'E',
				heading: 'E',
				id: 4
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 5
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 6
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 7
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 8
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 9
			},
			{
				code: 'H+L2',
				direction: 'E',
				heading: 'E',
				id: 10
			},
			{
				code: 'H+L',
				direction: 'E',
				heading: 'E',
				id: 11
			},
			{
				code: 'H+L',
				direction: 'E',
				heading: 'E',
				id: 12
			},
			{
				code: 'H+CR2',
				direction: 'N',
				heading: 'E',
				id: 13
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 14
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 16
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 17
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 18
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 19
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 20
			},
			{
				code: 'H+K+M',
				direction: 'N',
				heading: 'E',
				id: 22
			},
			{
				code: 'H+K+M',
				direction: 'N',
				heading: 'E',
				id: 23
			},
			{
				code: 'H+K+M',
				direction: 'N',
				heading: 'E',
				id: 24
			}
		]
	},
	{
		geoPointEastBound: '-27.624522459535925, 152.94185802340508',
		geoPointWestBound: '-27.62464603492637, 152.941664904356',
		id: 4,

		marker: '-27.624590, 152.941748',
		name: 'Centenary Motorway',
		group: 'Logan Motorway M2',
		validExits: [{
				code: 'Z',
				direction: 'W',
				heading: 'W',
				id: 1
			},
			{
				code: 'Z',
				direction: 'W',
				heading: 'W',
				id: 3
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 5
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 6
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 7
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 8
			},
			{
				code: 'H',
				direction: 'E',
				heading: 'E',
				id: 9
			},
			{
				code: 'H+L2',
				direction: 'E',
				heading: 'E',
				id: 10
			},
			{
				code: 'H+L',
				direction: 'E',
				heading: 'E',
				id: 11
			},
			{
				code: 'H+L',
				direction: 'E',
				heading: 'E',
				id: 12
			},
			{
				code: 'H+CR2',
				direction: 'N',
				heading: 'E',
				id: 13
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 14
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 16
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 17
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 18
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 19
			},
			{
				code: 'H+K',
				direction: 'N',
				heading: 'E',
				id: 20
			},
			{
				code: 'H+K+M',
				direction: 'N',
				heading: 'E',
				id: 22
			},
			{
				code: 'H+K+M',
				direction: 'N',
				heading: 'E',
				id: 23
			},
			{
				code: 'H+K+M',
				direction: 'N',
				heading: 'E',
				id: 24
			}
		]
	},
	{
		geoPointEastBound: '-27.640986463099264, 152.98551969230175',
		geoPointWestBound: '-27.641393368386673, 152.98544492572546',
		id: 5,

		marker: '-27.641211, 152.985474',
		name: 'Stapylton Rd',
		group: 'Logan Motorway M2',
		validExits: [{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 1
			},
			{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 3
			},
			{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 4
			},
			{
				code: 'H1',
				direction: 'E',
				heading: 'E',
				id: 6
			},
			{
				code: 'H1',
				direction: 'E',
				heading: 'E',
				id: 7
			},
			{
				code: 'H1',
				direction: 'E',
				heading: 'E',
				id: 8
			},
			{
				code: 'H1',
				direction: 'E',
				heading: 'E',
				id: 9
			},
			{
				code: 'H1+L2',
				direction: 'E',
				heading: 'E',
				id: 10
			},
			{
				code: 'H1+L',
				direction: 'E',
				heading: 'E',
				id: 11
			},
			{
				code: 'H1+L',
				direction: 'E',
				heading: 'E',
				id: 12
			},
			{
				code: 'H1+CR2',
				direction: 'N',
				heading: 'E',
				id: 13
			},
			{
				code: 'H1+K',
				direction: 'N',
				heading: 'E',
				id: 14
			},
			{
				code: 'H1+K',
				direction: 'N',
				heading: 'E',
				id: 16
			},
			{
				code: 'H1+K',
				direction: 'N',
				heading: 'E',
				id: 17
			},
			{
				code: 'H1+K',
				direction: 'N',
				heading: 'E',
				id: 18
			},
			{
				code: 'H1+K',
				direction: 'N',
				heading: 'E',
				id: 19
			},
			{
				code: 'H1+K',
				direction: 'N',
				heading: 'E',
				id: 20
			},
			{
				code: 'H1+K+M',
				direction: 'N',
				heading: 'E',
				id: 22
			},
			{
				code: 'H1+K+M',
				direction: 'N',
				heading: 'E',
				id: 23
			},
			{
				code: 'H1+K+M',
				direction: 'N',
				heading: 'E',
				id: 24
			}
		]
	},
	{
		geoPointEastBound: '-27.641756314745614, 153.01195621490479',
		geoPointWestBound: '-27.64191432374989, 153.01183015108109',
		id: 6,

		marker: '-27.641818, 153.011888',
		name: 'Paradise Rd',
		group: 'Logan Motorway M2',
		validExits: [{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 1
			},
			{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 3
			},
			{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 4
			},
			{
				code: 'H2',
				direction: 'W',
				heading: 'W',
				id: 5
			},
			{
				code: 'P1',
				direction: 'E',
				heading: 'E',
				id: 7
			},
			{
				code: 'P1',
				direction: 'E',
				heading: 'E',
				id: 8
			},
			{
				code: 'P1',
				direction: 'E',
				heading: 'E',
				id: 9
			},
			{
				code: 'P1+L2',
				direction: 'E',
				heading: 'E',
				id: 10
			},
			{
				code: 'P1+L',
				direction: 'E',
				heading: 'E',
				id: 11
			},
			{
				code: 'P1+L',
				direction: 'E',
				heading: 'E',
				id: 12
			},
			{
				code: 'P1+CR2',
				direction: 'N',
				heading: 'E',
				id: 13
			},
			{
				code: 'P1+K',
				direction: 'N',
				heading: 'E',
				id: 14
			},
			{
				code: 'P1+K',
				direction: 'N',
				heading: 'E',
				id: 16
			},
			{
				code: 'P1+K',
				direction: 'N',
				heading: 'E',
				id: 17
			},
			{
				code: 'P1+K',
				direction: 'N',
				heading: 'E',
				id: 18
			},
			{
				code: 'P1+K',
				direction: 'N',
				heading: 'E',
				id: 19
			},
			{
				code: 'P1+K',
				direction: 'N',
				heading: 'E',
				id: 20
			},
			{
				code: 'P1+K+M',
				direction: 'N',
				heading: 'E',
				id: 22
			},
			{
				code: 'P1+K+M',
				direction: 'N',
				heading: 'E',
				id: 23
			},
			{
				code: 'P1+K+M',
				direction: 'N',
				heading: 'E',
				id: 24
			}
		]
	},
	{
		geoPointEastBound: '-27.646718638942502, 153.0417287349701',
		geoPointWestBound: '-27.647143936599626, 153.04159730672836',
		id: 7,

		marker: '-27.646959, 153.041645',
		name: 'Beaudesert Rd',
		group: 'Logan Motorway M2',
		validExits: [{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 1
			},
			{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 3
			},
			{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 4
			},
			{
				code: 'H2',
				direction: 'W',
				heading: 'W',
				id: 5
			},
			{
				code: 'P2',
				direction: 'W',
				heading: 'W',
				id: 6
			},
			{
				code: 'Z',
				direction: 'E',
				heading: 'E',
				id: 8
			},
			{
				code: 'Z',
				direction: 'E',
				heading: 'E',
				id: 9
			},
			{
				code: 'L2',
				direction: 'E',
				heading: 'E',
				id: 10
			},
			{
				code: 'L',
				direction: 'E',
				heading: 'E',
				id: 11
			},
			{
				code: 'L',
				direction: 'E',
				heading: 'E',
				id: 12
			},
			{
				code: 'CR2',
				direction: 'N',
				heading: 'E',
				id: 13
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'E',
				id: 14
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'E',
				id: 16
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'E',
				id: 17
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'E',
				id: 18
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'E',
				id: 19
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'E',
				id: 20
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'E',
				id: 22
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'E',
				id: 23
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'E',
				id: 24
			}
		]
	},
	{
		geoPointEastBound: '-27.652157097189523, 153.07024732232094',
		geoPointWestBound: '-27.65227351382841, 153.07014405727386',
		id: 8,

		marker: '-27.652205, 153.070207',
		name: 'Wembley Rd',
		group: 'Logan Motorway M2',
		validExits: [{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 1
			},
			{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 3
			},
			{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 4
			},
			{
				code: 'H2',
				direction: 'W',
				heading: 'W',
				id: 5
			},
			{
				code: 'P2',
				direction: 'W',
				heading: 'W',
				id: 6
			},
			{
				code: 'Z',
				direction: 'W',
				heading: 'W',
				id: 7
			},
			{
				code: 'Z',
				direction: 'E',
				heading: 'E',
				id: 9
			},
			{
				code: 'L2',
				direction: 'E',
				heading: 'E',
				id: 10
			},
			{
				code: 'L',
				direction: 'E',
				heading: 'E',
				id: 11
			},
			{
				code: 'L',
				direction: 'E',
				heading: 'E',
				id: 12
			},
			{
				code: 'CR2',
				direction: 'N',
				heading: 'W',
				id: 13
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 14
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 16
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 17
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 18
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 19
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 20
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'W',
				id: 22
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'W',
				id: 23
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'W',
				id: 24
			}
		]
	},
	{
		geoPointEastBound: '-27.668078297556836, 153.124558031559',
		geoPointWestBound: '-27.66823686240313, 153.12460094690323',
		id: 9,
		marker: '-27.668195, 153.124594',
		name: 'Kingston Rd',
		group: 'Logan Motorway M2',
		validExits: [{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 1
			},
			{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 3
			},
			{
				code: 'H',
				direction: 'W',
				heading: 'W',
				id: 4
			},
			{
				code: 'H2',
				direction: 'W',
				heading: 'W',
				id: 5
			},
			{
				code: 'P2',
				direction: 'W',
				heading: 'W',
				id: 6
			},
			{
				code: 'Z',
				direction: 'W',
				heading: 'W',
				id: 7
			},
			{
				code: 'Z',
				direction: 'W',
				heading: 'W',
				id: 8
			},
			{
				code: 'L2',
				direction: 'E',
				heading: 'E',
				id: 10
			},
			{
				code: 'L',
				direction: 'E',
				heading: 'E',
				id: 11
			},
			{
				code: 'L',
				direction: 'E',
				heading: 'E',
				id: 12
			},
			{
				code: 'CR2',
				direction: 'N',
				heading: 'W',
				id: 13
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 14
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 16
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 17
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 18
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 19
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'W',
				id: 20
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'W',
				id: 22
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'W',
				id: 23
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'W',
				id: 24
			}
		]
	},
	{
		geoPointEastBound: '-27.665174797953178, 153.139957934618',
		geoPointWestBound: '-27.66540047669454, 153.13993111252785',
		id: 10,
		marker: '-27.665293, 153.139940',
		name: 'Loganlea Rd',
		group: 'Logan Motorway M2',
		validExits: [{
				code: 'L1+H',
				direction: 'W',
				heading: 'W',
				id: 1
			},
			{
				code: 'L1+H',
				direction: 'W',
				heading: 'W',
				id: 3
			},
			{
				code: 'L1+H',
				direction: 'W',
				heading: 'W',
				id: 4
			},
			{
				code: 'L1+H2',
				direction: 'W',
				heading: 'W',
				id: 5
			},
			{
				code: 'L1+P2',
				direction: 'W',
				heading: 'W',
				id: 6
			},
			{
				code: 'L1',
				direction: 'W',
				heading: 'W',
				id: 7
			},
			{
				code: 'L1',
				direction: 'W',
				heading: 'W',
				id: 8
			},
			{
				code: 'L1',
				direction: 'W',
				heading: 'W',
				id: 9
			},
			{
				code: 'L',
				direction: 'E',
				heading: 'E',
				id: 11
			},
			{
				code: 'L',
				direction: 'E',
				heading: 'E',
				id: 12
			},
			{
				code: 'L1+CR2',
				direction: 'N',
				heading: 'W',
				id: 13
			},
			{
				code: 'L1+K',
				direction: 'N',
				heading: 'W',
				id: 14
			},
			{
				code: 'L1+K',
				direction: 'N',
				heading: 'W',
				id: 16
			},
			{
				code: 'L1+K',
				direction: 'N',
				heading: 'W',
				id: 17
			},
			{
				code: 'L1+K',
				direction: 'N',
				heading: 'W',
				id: 18
			},
			{
				code: 'L1+K',
				direction: 'N',
				heading: 'W',
				id: 19
			},
			{
				code: 'L1+K',
				direction: 'N',
				heading: 'W',
				id: 20
			},
			{
				code: 'L1+K+M',
				direction: 'N',
				heading: 'W',
				id: 22
			},
			{
				code: 'L1+K+M',
				direction: 'N',
				heading: 'W',
				id: 23
			},
			{
				code: 'L1+K+M',
				direction: 'N',
				heading: 'W',
				id: 24
			}
		]
	},
	{
		geoPointEastBound: '-27.6844237,153.1798714',
		geoPointWestBound: '-27.68479938565361, 153.1795010715723',
		id: 11,

		marker: '-27.684736, 153.179507',
		name: 'Drews Rd',
		group: 'Logan Motorway M2',
		validExits: [{
				code: 'L+H',
				direction: 'W',
				heading: 'W',
				id: 1
			},
			{
				code: 'L+H',
				direction: 'W',
				heading: 'W',
				id: 3
			},
			{
				code: 'L+H',
				direction: 'W',
				heading: 'W',
				id: 4
			},
			{
				code: 'L+H2',
				direction: 'W',
				heading: 'W',
				id: 5
			},
			{
				code: 'L+P2',
				direction: 'W',
				heading: 'W',
				id: 6
			},
			{
				code: 'L',
				direction: 'W',
				heading: 'W',
				id: 7
			},
			{
				code: 'L',
				direction: 'W',
				heading: 'W',
				id: 8
			},
			{
				code: 'L',
				direction: 'W',
				heading: 'W',
				id: 9
			},
			{
				code: 'L',
				direction: 'W',
				heading: 'W',
				id: 10
			},
			{
				code: 'Z',
				direction: 'E',
				heading: 'E',
				id: 12
			},
			{
				code: 'L+CR2',
				direction: 'N',
				heading: 'W',
				id: 13
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 14
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 16
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 17
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 18
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 19
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 20
			},
			{
				code: 'L+K+M',
				direction: 'N',
				heading: 'W',
				id: 22
			},
			{
				code: 'L+K+M',
				direction: 'N',
				heading: 'W',
				id: 23
			},
			{
				code: 'L+K+M',
				direction: 'N',
				heading: 'W',
				id: 24
			}
		]
	},
	{
		geoPointEastBound: '-27.68532666578453, 153.18524435162544',
		geoPointWestBound: '-27.685687091165818, 153.18545758724213',
		id: 12,

		marker: '-27.685580, 153.185437',
		name: 'Pacific Mwy (Logan)',
		group: 'Logan Motorway M2',
		validExits: [{
				code: 'L+H',
				direction: 'W',
				heading: 'W',
				id: 1
			},
			{
				code: 'L+H',
				direction: 'W',
				heading: 'W',
				id: 3
			},
			{
				code: 'L+H',
				direction: 'W',
				heading: 'W',
				id: 4
			},
			{
				code: 'L+H2',
				direction: 'W',
				heading: 'W',
				id: 5
			},
			{
				code: 'L+P2',
				direction: 'W',
				heading: 'W',
				id: 6
			},
			{
				code: 'L',
				direction: 'W',
				heading: 'W',
				id: 7
			},
			{
				code: 'L',
				direction: 'W',
				heading: 'W',
				id: 8
			},
			{
				code: 'L',
				direction: 'W',
				heading: 'W',
				id: 9
			},
			{
				code: 'L',
				direction: 'W',
				heading: 'W',
				id: 10
			},
			{
				code: 'Z',
				direction: 'W',
				heading: 'W',
				id: 11
			},
			{
				code: 'L+CR2',
				direction: 'N',
				heading: 'W',
				id: 13
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 14
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 16
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 17
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 18
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 19
			},
			{
				code: 'L+K',
				direction: 'N',
				heading: 'W',
				id: 20
			},
			{
				code: 'L+K+M',
				direction: 'N',
				heading: 'W',
				id: 22
			},
			{
				code: 'L+K+M',
				direction: 'N',
				heading: 'W',
				id: 23
			},
			{
				code: 'L+K+M',
				direction: 'N',
				heading: 'W',
				id: 24
			}
		]
	},
	{
		geoPointNorthBound: '-27.613514778621276, 153.07872511446476',
		geoPointSouthBound: '-27.613584890955273, 153.07892963290215',
		id: 13,

		marker: '-27.613550, 153.078815',
		name: 'Compton Rd',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'CR1+H',
				direction: 'W',
				heading: 'S',
				id: 1
			},
			{
				code: 'CR1+H',
				direction: 'W',
				heading: 'S',
				id: 2
			},
			{
				code: 'CR1+H',
				direction: 'W',
				heading: 'S',
				id: 3
			},
			{
				code: 'CR1+H',
				direction: 'W',
				heading: 'S',
				id: 4
			},
			{
				code: 'CR1+H2',
				direction: 'W',
				heading: 'S',
				id: 5
			},
			{
				code: 'CR1+P2',
				direction: 'W',
				heading: 'S',
				id: 6
			},
			{
				code: 'CR1',
				direction: 'W',
				heading: 'S',
				id: 7
			},
			{
				code: 'CR1',
				direction: 'E',
				heading: 'S',
				id: 8
			},
			{
				code: 'CR1',
				direction: 'E',
				heading: 'S',
				id: 9
			},
			{
				code: 'CR1+L2',
				direction: 'E',
				heading: 'S',
				id: 10
			},
			{
				code: 'CR1+L',
				direction: 'E',
				heading: 'S',
				id: 11
			},
			{
				code: 'CR1+L',
				direction: 'E',
				heading: 'S',
				id: 12
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'N',
				id: 14
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'N',
				id: 16
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'N',
				id: 17
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'N',
				id: 18
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'N',
				id: 19
			},
			{
				code: 'K',
				direction: 'N',
				heading: 'N',
				id: 20
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'N',
				id: 22
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'N',
				id: 23
			},
			{
				code: 'K+M',
				direction: 'N',
				heading: 'N',
				id: 24
			}
		]
	},
	{
		geoPointNorthBound: '-27.584638349279096, 153.1003350019455',
		geoPointSouthBound: '-27.584685895595065, 153.10041211545467',
		id: 14,

		marker: '-27.584649, 153.100376',
		name: 'Logan Rd',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 1
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 3
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 4
			},
			{
				code: 'K+H2',
				direction: 'W',
				heading: 'S',
				id: 5
			},
			{
				code: 'K+P2',
				direction: 'W',
				heading: 'S',
				id: 6
			},
			{
				code: 'K',
				direction: 'W',
				heading: 'S',
				id: 7
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 8
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 9
			},
			{
				code: 'K+L2',
				direction: 'E',
				heading: 'S',
				id: 10
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 11
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 12
			},
			{
				code: 'K',
				direction: 'S',
				heading: 'S',
				id: 13
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 16
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 17
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 18
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 19
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 20
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 22
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 23
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 24
			}
		]
	},
	{
		geoPointNorthBound: '-27.581275583799254, 153.10307756066322',
		geoPointSouthBound: '-27.581370679326266, 153.10315802693367',
		id: 15,

		marker: '-27.581321, 153.103124',
		name: 'Pacific Mwy (Gateway)',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 16
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 17
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 18
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 19
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 20
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 22
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 23
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 24
			}
		]
	},
	{
		geoPointNorthBound: '-27.577614343320086, 153.10557536780834',
		geoPointSouthBound: '-27.577634551800966, 153.10572423040867',
		id: 16,

		marker: '-27.577624, 153.105627',
		name: 'Miles Platting Rd',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 1
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 3
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 4
			},
			{
				code: 'K+H2',
				direction: 'W',
				heading: 'S',
				id: 5
			},
			{
				code: 'K+P2',
				direction: 'W',
				heading: 'S',
				id: 6
			},
			{
				code: 'K',
				direction: 'W',
				heading: 'S',
				id: 7
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 8
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 9
			},
			{
				code: 'K+L2',
				direction: 'E',
				heading: 'S',
				id: 10
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 11
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 12
			},
			{
				code: 'K',
				direction: 'S',
				heading: 'S',
				id: 13
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 14
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 15
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 17
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 18
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 19
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 20
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 22
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 23
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 24
			}
		]
	},
	{
		geoPointNorthBound: '-27.545067917153503, 153.11718598008156',
		geoPointSouthBound: '-27.54506197171951, 153.11737641692162',
		id: 17,

		marker: '-27.545065, 153.117267',
		name: 'Mount Gravatt Capalaba Rd',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 1
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 3
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 4
			},
			{
				code: 'K+H2',
				direction: 'W',
				heading: 'S',
				id: 5
			},
			{
				code: 'K+P2',
				direction: 'W',
				heading: 'S',
				id: 6
			},
			{
				code: 'K',
				direction: 'W',
				heading: 'S',
				id: 7
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 8
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 9
			},
			{
				code: 'K+L2',
				direction: 'E',
				heading: 'S',
				id: 10
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 11
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 12
			},
			{
				code: 'K',
				direction: 'S',
				heading: 'S',
				id: 13
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 14
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 15
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 16
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 18
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 19
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 20
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 22
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 23
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 24
			}
		]
	},
	{
		geoPointNorthBound: '-27.50358355435371, 153.12436625361443',
		geoPointSouthBound: '-27.503609724131454, 153.12454998493195',
		id: 18,

		marker: '-27.503594, 153.124460',
		name: 'Old Cleveland Rd',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 1
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 3
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 4
			},
			{
				code: 'K+H2',
				direction: 'W',
				heading: 'S',
				id: 5
			},
			{
				code: 'K+P2',
				direction: 'W',
				heading: 'S',
				id: 6
			},
			{
				code: 'K',
				direction: 'W',
				heading: 'S',
				id: 7
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 8
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 9
			},
			{
				code: 'K+L2',
				direction: 'E',
				heading: 'S',
				id: 10
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 11
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 12
			},
			{
				code: 'K',
				direction: 'S',
				heading: 'S',
				id: 13
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 14
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 15
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 16
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 17
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 19
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 20
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 22
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 23
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 24
			}
		]
	},
	{
		geoPointNorthBound: '-27.47391492095676, 153.11687216162682',
		geoPointSouthBound: '-27.473946452137067, 153.11699621379375',
		id: 19,

		marker: '-27.473928, 153.116933',
		name: 'Wynnum Rd',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 1
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 3
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 4
			},
			{
				code: 'K+H2',
				direction: 'W',
				heading: 'S',
				id: 5
			},
			{
				code: 'K+P2',
				direction: 'W',
				heading: 'S',
				id: 6
			},
			{
				code: 'K',
				direction: 'W',
				heading: 'S',
				id: 7
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 8
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 9
			},
			{
				code: 'K+L2',
				direction: 'E',
				heading: 'S',
				id: 10
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 11
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 12
			},
			{
				code: 'K',
				direction: 'S',
				heading: 'S',
				id: 13
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 14
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 15
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 16
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 17
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 18
			},
			{
				code: 'Z',
				direction: 'N',
				heading: 'N',
				id: 20
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 22
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 23
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 24
			}
		]
	},
	{
		geoPointNorthBound: '-27.45796255656476, 153.1082260608673',
		geoPointSouthBound: '-27.457876874497675, 153.10840174555779',
		id: 20,

		marker: '-27.457926, 153.108324',
		name: 'Port of Brisbane Mwy',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 1
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 3
			},
			{
				code: 'K+H',
				direction: 'W',
				heading: 'S',
				id: 4
			},
			{
				code: 'K+H2',
				direction: 'W',
				heading: 'S',
				id: 5
			},
			{
				code: 'K+P2',
				direction: 'W',
				heading: 'S',
				id: 6
			},
			{
				code: 'K',
				direction: 'W',
				heading: 'S',
				id: 7
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 8
			},
			{
				code: 'K',
				direction: 'E',
				heading: 'S',
				id: 9
			},
			{
				code: 'K+L2',
				direction: 'E',
				heading: 'S',
				id: 10
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 11
			},
			{
				code: 'K+L',
				direction: 'E',
				heading: 'S',
				id: 12
			},
			{
				code: 'K',
				direction: 'S',
				heading: 'S',
				id: 13
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 14
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 15
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 16
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 17
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 18
			},
			{
				code: 'Z',
				direction: 'S',
				heading: 'S',
				id: 19
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 22
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 23
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 24
			}
		]
	},
	{
		geoPointNorthBound: '-27.455800254038184, 153.10675352811813',
		geoPointSouthBound: '-27.45566696818027, 153.10689568519592',
		id: 21,

		marker: '-27.455719, 153.106815',
		name: 'Lytton Rd',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 24
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 22
			},
			{
				code: 'M',
				direction: 'N',
				heading: 'N',
				id: 23
			}
		]
	},
	{
		geoPointNorthBound: '-27.436633700684485, 153.096126280725',
		geoPointSouthBound: '-27.4347665,153.0957305',
		id: 22,

		marker: '-27.436310, 153.096142',
		name: 'Southern Cross Way',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'M+K+H',
				direction: 'W',
				heading: 'S',
				id: 1
			},
			{
				code: 'M+K+H',
				direction: 'W',
				heading: 'S',
				id: 3
			},
			{
				code: 'M+K+H',
				direction: 'W',
				heading: 'S',
				id: 4
			},
			{
				code: 'M+K+H2',
				direction: 'W',
				heading: 'S',
				id: 5
			},
			{
				code: 'M+K+P2',
				direction: 'W',
				heading: 'S',
				id: 6
			},
			{
				code: 'M+K',
				direction: 'W',
				heading: 'S',
				id: 7
			},
			{
				code: 'M+K',
				direction: 'E',
				heading: 's',
				id: 8
			},
			{
				code: 'M+K',
				direction: 'E',
				heading: 'S',
				id: 9
			},
			{
				code: 'M+K+L2',
				direction: 'E',
				heading: 'S',
				id: 10
			},
			{
				code: 'M+K+L',
				direction: 'E',
				heading: 'S',
				id: 11
			},
			{
				code: 'M+K+L',
				direction: 'E',
				heading: 'S',
				id: 12
			},
			{
				code: 'M+K',
				direction: 'S',
				heading: 'S',
				id: 13
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 14
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 15
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 16
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 17
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 18
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 19
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 20
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 21
			}
		]
	},
	{
		geoPointNorthBound: '-27.43013797209447, 153.09312757104635',
		geoPointSouthBound: '-27.43007786049334, 153.09327978640795',
		id: 23,

		marker: '-27.43010583322269, 153.09320200234652',
		name: 'Kingsford Smith Dr',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'M+K+H',
				direction: 'W',
				heading: 'S',
				id: 1
			},
			{
				code: 'M+K+H',
				direction: 'W',
				heading: 'S',
				id: 3
			},
			{
				code: 'M+K+H',
				direction: 'W',
				heading: 'S',
				id: 4
			},
			{
				code: 'M+K+H2',
				direction: 'W',
				heading: 'S',
				id: 5
			},
			{
				code: 'M+K+P2',
				direction: 'W',
				heading: 'S',
				id: 6
			},
			{
				code: 'M+K',
				direction: 'W',
				heading: 'S',
				id: 7
			},
			{
				code: 'M+K',
				direction: 'E',
				heading: 'S',
				id: 8
			},
			{
				code: 'M+K',
				direction: 'E',
				heading: 'S',
				id: 9
			},
			{
				code: 'M+K+L2',
				direction: 'E',
				heading: 'S',
				id: 10
			},
			{
				code: 'M+K+L',
				direction: 'E',
				heading: 'S',
				id: 11
			},
			{
				code: 'M+K+L',
				direction: 'E',
				heading: 'S',
				id: 12
			},
			{
				code: 'M+K',
				direction: 'S',
				heading: 'S',
				id: 13
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 14
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 15
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 16
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 17
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 18
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 19
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 20
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 21
			}
		]
	},
	{
		geoPointNorthBound: '-27.40809386560062, 153.08889592532068',
		geoPointSouthBound: '-27.40825102020322, 153.0890434468165',
		id: 24,

		marker: '-27.408063, 153.089047',
		name: 'Moreton Dr (Airport)',
		group: 'Gateway Motorway M1',
		validExits: [{
				code: 'M+K+H',
				direction: 'W',
				heading: 'S',
				id: 1
			},
			{
				code: 'M+K+H',
				direction: 'W',
				heading: 'S',
				id: 3
			},
			{
				code: 'M+K+H',
				direction: 'W',
				heading: 'S',
				id: 4
			},
			{
				code: 'M+K+H2',
				direction: 'W',
				heading: 'S',
				id: 5
			},
			{
				code: 'M+K+P2',
				direction: 'W',
				heading: 'S',
				id: 6
			},
			{
				code: 'M+K',
				direction: 'W',
				heading: 'S',
				id: 7
			},
			{
				code: 'M+K',
				direction: 'E',
				heading: 'S',
				id: 8
			},
			{
				code: 'M+K',
				direction: 'E',
				heading: 'S',
				id: 9
			},
			{
				code: 'M+K+L2',
				direction: 'E',
				heading: 'S',
				id: 10
			},
			{
				code: 'M+K+L',
				direction: 'E',
				heading: 'S',
				id: 11
			},
			{
				code: 'M+K+L',
				direction: 'E',
				heading: 'S',
				id: 12
			},
			{
				code: 'M+K',
				direction: 'S',
				heading: 'S',
				id: 13
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 14
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 15
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 16
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 17
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 18
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 19
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 20
			},
			{
				code: 'M',
				direction: 'S',
				heading: 'S',
				id: 21
			}
		]
	},
	{
		geoPointNorthBound: '-27.469827392215496, 153.01215067505836',
		geoPointSouthBound: '-27.469780688624024, 153.01223382353783',
		id: 25,
		marker: '-27.469799, 153.012199',
		name: 'Go Between Bridge',
		group: 'Go Between Bridge',
		peakOffPeak: true,
		validExits: [{
			code: 'GBB',
			id: 25
		}]
	},
	{
		geoPointNorthBound: '-27.491717298179548, 153.03362142294645',
		geoPointSouthBound: '-27.4916938023183, 153.03372904658318',
		id: 26,
		marker: '-27.491697, 153.033656',
		name: 'Pacific Mwy/Ipswich Rd',
		group: 'Clem7',
		peakOffPeak: true,
		validExits: [{
				code: 'C7+A1',
				direction: 'N',
				heading: 'N',
				id: 32
			},
			{
				code: 'C7+A2',
				direction: 'E',
				heading: 'N',
				id: 33
			},
			{
				code: 'C7',
				direction: 'N',
				heading: 'N',
				id: 28
			},
			{
				code: 'C7+LW',
				direction: 'S',
				heading: 'N',
				id: 29
			}
		]
	},
	{
		geoPointNorthBound: '-27.476113752961673, 153.03806215524673',
		geoPointSouthBound: '-27.47608370969236, 153.03807757794857',
		id: 27,

		marker: '-27.476100, 153.038033',
		name: 'Shafston Ave',
		group: 'Clem7',
		peakOffPeak: true,
		validExits: [{
				code: 'C7+A1',
				direction: 'N',
				heading: 'N',
				id: 32
			},
			{
				code: 'C7+A2',
				direction: 'E',
				heading: 'N',
				id: 33
			},
			{
				code: 'C7',
				direction: 'N',
				heading: 'N',
				id: 28
			},
			{
				code: 'C7+LW',
				direction: 'S',
				heading: 'N',
				id: 29
			}
		]
	},
	{
		geoPointNorthBound: '-27.447742120381257, 153.03258508443832',
		geoPointSouthBound: '-27.447790916050423, 153.03275004029274',
		id: 28,

		marker: '-27.447750, 153.032666',
		name: 'Inner City Bypass/Lutwyche Rd',
		group: 'Clem7',
		peakOffPeak: true,
		validExits: [{
				code: 'C7',
				direction: 'S',
				heading: 'S',
				id: 26
			},
			{
				code: 'C7',
				direction: 'S',
				heading: 'S',
				id: 27
			}
		]
	},
	{
		geoPointNorthBound: '-27.478313730893035, 152.97791495919228',
		geoPointSouthBound: '-27.47839344803548, 152.97798335552216',
		id: 29,

		marker: '-27.478347, 152.977951',
		name: 'Western Fwy',
		group: 'Legacy Way',
		validExits: [{
				code: 'LW+A1',
				direction: 'N',
				heading: 'N',
				id: 32
			},
			{
				code: 'LW+A2',
				direction: 'E',
				heading: 'N',
				id: 33
			},
			{
				code: 'LW+C7',
				direction: 'S',
				heading: 'N',
				id: 26
			},
			{
				code: 'LW+C7',
				direction: 'S',
				heading: 'N',
				id: 27
			},
			{
				code: 'LW',
				direction: 'N',
				heading: 'N',
				id: 30
			}
		]
	},
	{
		geoPointNorthBound: '-27.456997439432477, 153.0174708366394',
		geoPointSouthBound: '-27.45710454286016, 153.01746279001236',
		id: 30,
		marker: '-27.457041, 153.017427',
		name: 'Inner City Bypass',
		group: 'Legacy Way',
		validExits: [{
			code: 'LW',
			direction: 'S',
			heading: 'S',
			id: 29
		}]
	},
	{
		geoPointNorthBound: '-27.44165677421649, 153.03161814808846',
		geoPointSouthBound: '-27.441648442786903, 153.03177773952484',
		id: 31,
		marker: '-27.441652, 153.031691',
		name: 'Bowen Hills',
		group: 'AirportlinkM7',
		validExits: [{
				code: 'A1',
				direction: 'N',
				heading: 'N',
				id: 32
			},
			{
				code: 'A2',
				direction: 'E',
				heading: 'N',
				id: 33
			}
		]
	},
	{
		geoPointNorthBound: '-27.412822692627206, 153.0339302122593',
		geoPointSouthBound: '-27.412822692627206, 153.0340039730072',
		id: 32,

		marker: '-27.412822692627206, 153.0338873',
		name: 'Kedron',
		group: 'AirportlinkM7',
		validExits: [{
				code: 'A3',
				direction: 'E',
				heading: 'S',
				id: 33
			},
			{
				code: 'A1+C7',
				direction: 'S',
				heading: 'S',
				id: 26
			},
			{
				code: 'A1+C7',
				direction: 'S',
				heading: 'S',
				id: 27
			},
			{
				code: 'A1+LW',
				direction: 'S',
				heading: 'S',
				id: 29
			},
			{
				code: 'A1',
				direction: 'S',
				heading: 'S',
				id: 31
			}
		]
	},
	{
		geoPointEastBound: '-27.410816656535, 153.05818006396294',
		geoPointWestBound: '-27.410933329067973, 153.05817872285843',
		id: 33,

		marker: '-27.410937, 153.058292',
		name: 'Toombul',
		group: 'AirportlinkM7',
		validExits: [{
				code: 'A3',
				direction: 'N',
				heading: 'W',
				id: 32
			},
			{
				code: 'A2+C7',
				direction: 'S',
				heading: 'W',
				id: 26
			},
			{
				code: 'A2+C7',
				direction: 'S',
				heading: 'W',
				id: 27
			},
			{
				code: 'A2+LW',
				direction: 'S',
				heading: 'W',
				id: 29
			},
			{
				code: 'A2',
				direction: 'S',
				heading: 'W',
				id: 31
			}
		]
	}
]

const brisbanePrices = [{
		id: 'M',
		name: 'Murarrie',
		price: {
			car: 4.62,
			hcv: 14.18,
			lcv: 6.93,
			mot: 2.31
		},
		roadName: 'Gateway Motorway'
	},
	{
		id: 'K',
		name: 'Kuraby',
		price: {
			car: 2.72,
			hcv: 8.37,
			lcv: 4.09,
			mot: 1.37
		},
		roadName: 'Gateway Motorway'
	},
	{
		id: 'L',
		name: 'Loganlea',
		price: {
			car: 1.75,
			hcv: 5.36,
			lcv: 2.63,
			mot: 0.87
		},
		roadName: 'Logan Motorway'
	},
	{
		id: 'H',
		name: 'Heathwood',
		price: {
			car: 2.88,
			hcv: 8.85,
			lcv: 4.31,
			mot: 1.44
		},
		roadName: 'Logan Motorway'
	},
	{
		id: 'P',
		name: 'Paradise Road',
		price: {
			car: 2.88,
			hcv: 7.62,
			lcv: 4.31,
			mot: 1.44
		},
		roadName: 'Logan Motorway'
	},
	{
		id: 'GBB',
		name: 'Go Between Bridge',
		price: {
			car: 3.23,
			hcv: 8.58,
			lcv: 4.85,
			mot: 1.62
		},
		roadName: 'Go Between Bridge',
		hcvOffPeak: 8.58,
		hcvPeak: 9.7
	},
	{
		id: 'C7',
		name: 'Clem7',
		price: {
			car: 5.19,
			hcv: 13.74,
			lcv: 7.78,
			mot: 2.59
		},
		roadName: 'Clem7',
		hcvOffPeak: 13.74,
		hcvPeak: 15.57
	},
	{
		id: 'LW',
		name: 'Legacy Way',
		price: {
			car: 5.19,
			hcv: 13.76,
			lcv: 7.79,
			mot: 2.6
		},
		roadName: 'Legacy Way'
	},
	{
		id: 'A1',
		name: 'Bowen Hills to Kedron',
		price: {
			car: 5.56,
			hcv: 14.73,
			lcv: 8.34,
			mot: 2.78
		},
		roadName: 'AirportLinkM7'
	},
	{
		id: 'A2',
		name: 'Bowen Hills to Toombul',
		price: {
			car: 5.56,
			hcv: 14.73,
			lcv: 8.34,
			mot: 2.78
		},
		roadName: 'AirportLinkM7'
	},
	{
		id: 'A3',
		name: 'Kedron to Toombul',
		price: {
			car: 4.16,
			hcv: 11.05,
			lcv: 6.25,
			mot: 2.09
		},
		roadName: 'AirportLinkM7'
	},
	{
		id: 'CR1',
		name: 'Compton Road',
		price: {
			car: 2.72,
			hcv: 8.37,
			lcv: 4.09,
			mot: 1.37
		},
		roadName: 'Gateway Motorway'
	},
	{
		id: 'CR2',
		name: 'Compton Road',
		price: {
			car: 2.72,
			hcv: 8.37,
			lcv: 4.09,
			mot: 1.37
		},
		roadName: 'Gateway Motorway'
	},
	{
		id: 'Z',
		name: 'No Toll',
		price: {
			car: 0,
			hcv: 0,
			lcv: 0,
			mot: 0
		}
	},
	{
		id: 'VMF',
		name: 'Video Matching Fee',
		price: {
			car: 0.49,
			hcv: 0.49,
			lcv: 0.49,
			mot: 0
		}
	}
];