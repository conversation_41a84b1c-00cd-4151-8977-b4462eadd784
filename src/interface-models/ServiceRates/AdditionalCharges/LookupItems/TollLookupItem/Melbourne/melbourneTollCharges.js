const melbourne = [
  {
  id: 1,
  name: 'Tullamarine Freeway & Bulla Road',
  marker: '-37.735413, 144.904578',
  highlighted: '',
  geoPointSouthBound: '-37.735299, 144.904659',
  geoPointNorthBound: '-37.735486, 144.904772',
  validExits: [
  {
  id: 2,
  direction: 'south',
  code: '0'
  },
  {
  id: 4,
  direction: 'south',
  code: '1A'
  },
  {
  id: 5,
  direction: 'south',
  code: '1A'
  },
  {
  id: 7,
  direction: 'south',
  code: '1A+2A'
  },
  {
  id: 8,
  direction: 'south',
  code: '1A+2A'
  },
  {
  id: 9,
  direction: 'south',
  code: '1A+2A+3A'
  },
  {
  id: 10,
  direction: 'south',
  code: '1A+2A+3A'
  },
  {
  id: 11,
  direction: 'south',
  code: '1A+2A+3A'
  },
  {
  id: 12,
  direction: 'south',
  code: '1A+2A+3A+4A+5A'
  },
  {
  id: 15,
  direction: 'south',
  code: '1A+2A+3A+5B+6A'
  }
  ]
  },
  {
  id: 2,
  name: 'Bell Street & Pascoe Vale Road',
  marker: '-37.735016, 144.927335',
  geoPointSouthBound: '-37.734937, 144.927470',
  geoPointNorthBound: '-37.735134, 144.927311',
   
  validExits: [
  {
  id: 1,
  direction: 'north',
  code: '0'
  },
  {
  id: 4,
  direction: 'south',
  code: '1A'
  },
  {
  id: 5,
  direction: 'south',
  code: '1A'
  },
  {
  id: 7,
  direction: 'south',
  code: '1A+2A'
  },
  {
  id: 8,
  direction: 'south',
  code: '1A+2A'
  },
  {
  id: 9,
  direction: 'south',
  code: '1A+2A+3A'
  },
  {
  id: 10,
  direction: 'south',
  code: '1A+2A+3A'
  },
  {
  id: 11,
  direction: 'south',
  code: '1A+2A+3A'
  },
  {
  id: 12,
  direction: 'south',
  code: '1A+2A+3A+4A+5A'
  },
  {
  id: 15,
  direction: 'south',
  code: '1A+2A+3A+4A+5B+6A'
  }
  ]
  },
  {
  id: 3,
  name: 'Moreland Road',
  marker: '-37.752067, 144.935626',
  geoPointSouthBound: '-37.752237, 144.935825',
  geoPointNorthBound: '-37.752230, 144.935575',
   
  validExits: [
  {
  id: 4,
  direction: 'south',
  code: '1A'
  },
  {
  id: 5,
  direction: 'south',
  code: '1A'
  },
  {
  id: 7,
  direction: 'south',
  code: '1A+2A'
  },
  {
  id: 8,
  direction: 'south',
  code: '1A+2A'
  },
  {
  id: 9,
  direction: 'south',
  code: '1A+2A+3A'
  },
  {
  id: 10,
  direction: 'south',
  code: '1A+2A+3A'
  },
  {
  id: 11,
  direction: 'south',
  code: '1A+2A+3A'
  },
  {
  id: 12,
  direction: 'south',
  code: '1A+2A+3A+5B'
  },
  {
  id: 15,
  direction: 'south',
  code: '1A+2A+3A+5B+6A'
  }
  ]
  },
  {
  id: 4,
  name: 'Brunswick Road',
  marker: '-37.773855, 144.936976',
  geoPointSouthBound: '-37.773788, 144.937081',
  geoPointNorthBound: '-37.773737, 144.936877',
   
  validExits: [
  {
  id: 1,
  direction: 'north',
  code: '1B'
  },
  {
  id: 2,
  direction: 'north',
  code: '1B'
  },
  {
  id: 3,
  direction: 'north',
  code: '1B'
  }
  ]
  },
  {
  id: 5,
  name: 'Flemington Road',
  highlighted: 'Flemington Road',
  marker: '-37.786375, 144.939439',
  geoPointSouthBound: '-37.786367, 144.939525',
  geoPointNorthBound: '-37.786387, 144.939373',
  validExits: [
  {
  id: 1,
  direction: 'north',
  code: '1B'
  },
  {
  id: 2,
  direction: 'north',
  code: '1B'
  },
  {
  id: 3,
  direction: 'north',
  code: '1B'
  }
  ]
  },
  {
  id: 6,
  name: 'Racecourse Road',
  marker: '-37.789040, 144.937877',
  highlighted: 'CityLink M2',
  geoPointSouthBound: '-37.789032, 144.937974',
  geoPointNorthBound: '-37.788947, 144.937835',
  validExits: [
  {
  id: 7,
  direction: 'south',
  code: '2A'
  },
  {
  id: 8,
  direction: 'south',
  code: '2A'
  },
  {
  id: 9,
  direction: 'south',
  code: '2A+3A'
  },
  {
  id: 10,
  direction: 'south',
  code: '2A+3A'
  },
  {
  id: 11,
  direction: 'south',
  code: '2A+3A'
  },
  {
  id: 12,
  direction: 'south',
  code: '2A+3A+5A'
  },
  {
  id: 15,
  direction: 'south',
  code: '2A+3A+5B+6A'
  }
  ]
  },
  {
  id: 7,
  name: 'Dynon Road',
  marker: '-37.804408, 144.934527',
  geoPointSouthBound: '-37.804332, 144.934647',
  geoPointNorthBound: '-37.804311, 144.934524',
  validExits: [
  {
  id: 1,
  direction: 'north',
  code: '2B+1B'
  },
  {
  id: 2,
  direction: 'north',
  code: '2B+1B'
  },
  {
  id: 3,
  direction: 'north',
  code: '2B+1B'
  },
  {
  id: 6,
  direction: 'north',
  code: '2B'
  }
  ]
  },
  {
  id: 8,
  name: 'Footscray Road',
  marker: '-37.809371, 144.933620',
  geoPointSouthBound: '-37.809376, 144.933721',
  geoPointNorthBound: '-37.809359, 144.933528',
  validExits: [
  {
  id: 1,
  direction: 'north',
  code: '2B+1B'
  },
  {
  id: 2,
  direction: 'north',
  code: '2B+1B'
  },
  {
  id: 3,
  direction: 'north',
  code: '2B+1B'
  },
  {
  id: 6,
  direction: 'north',
  code: '2B'
  },
  {
  id: 9,
  direction: 'south',
  code: '3A'
  },
  {
  id: 10,
  direction: 'south',
  code: '3A'
  },
  {
  id: 11,
  direction: 'south',
  code: '3A'
  },
  {
  id: 12,
  direction: 'south',
  code: '3A+5A'
  },
  {
  id: 15,
  direction: 'south',
  code: '3A+5B+6A'
  }
  ]
  },
  {
  id: 9,
  name: 'West Gate Freeway',
  marker: '-37.826495, 144.930728',
  highlighted: 'West Gate Freeway',
  geoPointSouthBound: '-37.826387, 144.930768',
  geoPointNorthBound: '-37.826607, 144.930685',
  geoPointEastBound: '-37.826392, 144.930767',
  validExits: [
  {
  id: 1,
  direction: 'north',
  code: '3B+2B+1B'
  },
  {
  id: 2,
  direction: 'north',
  code: '3B+2B+1B'
  },
  {
  id: 3,
  direction: 'north',
  code: '3B+2B+1B'
  },
  {
  id: 6,
  direction: 'north',
  code: '3B+2B'
  },
  {
  id: 8,
  direction: 'north',
  code: '3B'
  },
  {
  id: 10,
  direction: 'east',
  code: '0'
  },
  {
  id: 11,
  direction: 'east',
  code: '0'
  },
  {
  id: 12,
  direction: 'east',
  code: '5A'
  },
  {
  id: 15,
  direction: 'east',
  code: '5B+6A'
  }
  ]
  },
  {
  id: 10,
  name: 'Lorimer Street & Montague Street',
  marker: '-37.826226, 144.947282',
  geoPointSouthBound: '-37.826150, 144.947110',
  geoPointNorthBound: '-37.826606, 144.946740',
   
  validExits: [
  {
  id: 1,
  direction: 'north',
  code: '3B+2B+1B'
  },
  {
  id: 2,
  direction: 'north',
  code: '3B+2B+1B'
  },
  {
  id: 3,
  direction: 'north',
  code: '3B+2B+1B'
  },
  {
  id: 6,
  direction: 'north',
  code: '3B+2B'
  },
  {
  id: 8,
  direction: 'north',
  code: '3B'
  },
  {
  id: 9,
  direction: 'north',
  code: '0'
  },
  {
  id: 11,
  direction: 'south',
  code: '0'
  },
  {
  id: 12,
  direction: 'south',
  code: '5A'
  },
  {
  id: 15,
  direction: 'south',
  code: '5B+6A'
  }
  ]
  },
  {
  id: 11,
  name: 'Kings Way & Power Street',
  marker: '-37.827534, 144.962140',
  highlighted: 'Kings Way',
  geoPointSouthBound: '-37.827569, 144.961717',
  geoPointNorthBound: '-37.827595, 144.962159',
  validExits: [
  {
  id: 1,
  direction: 'north',
  code: '3B+2B+1B'
  },
  {
  id: 2,
  direction: 'north',
  code: '3B+2B+1B'
  },
  {
  id: 3,
  direction: 'north',
  code: '3B+2B+1B'
  },
  {
  id: 6,
  direction: 'north',
  code: '3B+2B'
  },
  {
  id: 8,
  direction: 'north',
  code: '3B'
  },
  {
  id: 9,
  direction: 'north',
  code: '0'
  },
  {
  id: 10,
  direction: 'north',
  code: '0'
  },
  {
  id: 12,
  direction: 'south',
  code: '5A'
  },
  {
  id: 15,
  direction: 'south',
  code: '5B+6A'
  }
  ]
  },
  {
  id: 12,
  name: 'Burnley Street',
  marker: '-37.830063, 145.006408',
  geoPointSouthBound: '-37.830063, 145.006408',
  geoPointNorthBound: '-37.830063, 145.006408',
  exitOnly: true,
  validExits: [ ]
  },
  {
  id: 13,
  name: 'Gibdon Street',
  marker: '-37.830455, 145.010489',
  geoPointSouthBound: '-37.830455, 145.010383',
  geoPointNorthBound: '-37.830455, 145.010489',
  validExits: [
  {
  id: 1,
  direction: 'north',
  code: '9B+3B+2B+1B'
  },
  {
  id: 2,
  direction: 'north',
  code: '9B+3B+2B+1B'
  },
  {
  id: 3,
  direction: 'north',
  code: '9B+3B+2B+1B'
  },
  {
  id: 6,
  direction: 'north',
  code: '9B+3B+2B'
  },
  {
  id: 8,
  direction: 'north',
  code: '9B+3B'
  },
  {
  id: 9,
  direction: 'north',
  code: '9B'
  },
  {
  id: 10,
  direction: 'north',
  code: '9B'
  },
  {
  id: 11,
  direction: 'north',
  code: '9B'
  },
  {
  id: 16,
  direction: 'north',
  code: '4B+8B'
  },
  {
  id: 17,
  direction: 'north',
  code: '4B'
  },
  {
  id: 18,
  direction: 'north',
  code: '4A'
  }
  ]
  },
  {
  id: 14,
  name: 'Yarra Boulevard',
  marker: '-37.829993, 145.013951',
  highlighted: 'CityLink M1',
  geoPointSouthBound: '-37.829904, 145.013922',
  geoPointNorthBound: '-37.830066, 145.014123',
  validExits: [
  {
  id: 15,
  direction: 'south',
  code: '6A'
  }
  ]
  },
  {
  id: 15,
  name: 'Monash Freeway & Toorak Road',
  marker: '-37.844720, 145.039597',
  geoPointSouthBound: '-37.844470, 145.039639',
  geoPointNorthBound: '-37.844449, 145.039478',
  highlighted: 'Monash Freeway',
  validExits: [
  {
  id: 1,
  direction: 'north',
  code: '6B+9B+3B+2B+1B'
  },
  {
  id: 2,
  direction: 'north',
  code: '6B+9B+3B+2B+1B'
  },
  {
  id: 3,
  direction: 'north',
  code: '6B+9B+3B+2B+1B'
  },
  {
  id: 6,
  direction: 'north',
  code: '6B+9B+3B+2B'
  },
  {
  id: 8,
  direction: 'north',
  code: '6B+9B+3B'
  },
  {
  id: 9,
  direction: 'north',
  code: '6B+9B'
  },
  {
  id: 10,
  direction: 'north',
  code: '6B+9B'
  },
  {
  id: 11,
  direction: 'north',
  code: '6B+9B'
  },
  {
  id: 14,
  direction: 'north',
  code: '6B'
  },
  {
  id: 16,
  direction: 'north',
  code: '6B+4B+8B'
  },
  {
  id: 17,
  direction: 'north',
  code: '6B+4B'
  },
  {
  id: 18,
  direction: 'north',
  code: '6B+4A'
  }
  ]
  },
  {
  id: 16,
  name: 'Exhibition Street & Batman Avenue',
  marker: '-37.816124, 144.972465',
  geoPointSouthBound: '-37.816113, 144.972562',
  geoPointNorthBound: '-37.816194, 144.972396',
   
  validExits: [
  {
  id: 12,
  direction: 'south',
  code: '8A+9A+7A'
  },
  {
  id: 15,
  direction: 'south',
  code: '8A+9A+7A+6A'
  },
  {
  id: 17,
  direction: 'south',
  code: '8A'
  },
  {
  id: 19,
  direction: 'south',
  code: '8A+9A'
  }
  ]
  },
  {
  id: 17,
  name: 'Swan Street',
  marker: '-37.823317, 144.978452',
  geoPointSouthBound: '-37.823434, 144.978624',
  geoPointNorthBound: '-37.823440, 144.978493',
   
  validExits: [
  {
  id: 12,
  direction: 'south',
  code: '9A+7A'
  },
  {
  id: 15,
  direction: 'south',
  code: '9A+7A+6A'
  },
  {
  id: 16,
  direction: 'north',
  code: '8B'
  },
  {
  id: 19,
  direction: 'south',
  code: '9A'
  }
  ]
  },
  {
  id: 18,
  name: 'Punt Road',
  marker: '-37.827350, 144.988342',
  geoPointSouthBound: '-37.827418, 144.988401',
  geoPointNorthBound: '-37.827185, 144.988262',
  validExits: [
  {
  id: 12,
  direction: 'south',
  code: '7A'
  },
  {
  id: 15,
  direction: 'south',
  code: '7A+6A'
  },
  {
  id: 19,
  direction: 'south',
  code: '0'
  }
  ]
  },
  {
  id: 19,
  name: 'Church Street',
  marker: '-37.833840, 144.996621',
  geoPointSouthBound: '-37.833785, 144.996542',
  geoPointNorthBound: '-37.833878, 144.996531',
  exitOnly: true,
  validExits: [ ]
  },
  {
  id: 20,
  name: 'Eastern Fwy/Springvale Road',
  groupName: 'EastLink',
  marker: '-37.803889, 145.178023',
  highlighted: 'Eastern Fwy Springvale Rd',
  geoPointSouthBound: '-37.803817, 145.178022',
  geoPointNorthBound: '-37.803953, 145.178021',
  validExits: [
  {
  id: 21,
  direction: 'south',
  code: 'E1'
  },
  {
  id: 22,
  direction: 'south',
  code: 'E1'
  },
  {
  id: 23,
  direction: 'south',
  code: 'E1+E2'
  },
  {
  id: 24,
  direction: 'south',
  code: 'E1+E2+E3'
  },
  {
  id: 25,
  direction: 'south',
  code: 'E1+E2+E3+E4'
  },
  {
  id: 26,
  direction: 'south',
  code: 'E1+E2+E3+E4+E5'
  },
  {
  id: 27,
  direction: 'south',
  code: 'E1+E2+E3+E4+E5+E6'
  },
  {
  id: 28,
  direction: 'south',
  code: 'E1+E2+E3+E4+E5+E6+E7'
  },
  {
  id: 29,
  direction: 'south',
  code: 'E1+E2+E3+E4+E5+E6+E7+E8'
  },
  {
  id: 30,
  direction: 'south',
  code: 'E1+E2+E3+E4+E5+E6+E7+E8'
  },
  {
  id: 31,
  direction: 'south',
  code: 'E1+E2+E3+E4+E5+E6+E7+E8+E9'
  },
  {
  id: 32,
  direction: 'south',
  code: 'E1+E2+E3+E4+E5+E6+E7+E8+E9+E10'
  },
  {
  id: 33,
  direction: 'south',
  code: 'E1+E2+E3+E4+E5+E6+E7+E8+E9+E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E1+E2+E3+E4+E5+E6+E7+E8+E9+E10+E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E1+E2+E3+E4+E5+E6+E7+E8+E9+E10+E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E1+E2+E3+E4+E5+E6+E7+E8+E9+E10+E11+E12+E13'
  }
  ]
  },
  {
  id: 21,
  name: 'Ringwood Bypass',
  groupName: 'EastLink',
  marker: '-37.812825, 145.215380',
  highlighted: '',
  geoPointSouthBound: '-37.812775, 145.215344',
  geoPointNorthBound: '-37.812936, 145.215269',
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E1'
  },
  {
  id: 22,
  direction: 'south',
  code: '0'
  },
  {
  id: 23,
  direction: 'south',
  code: 'E2'
  },
  {
  id: 24,
  direction: 'south',
  code: 'E2+E3'
  },
  {
  id: 25,
  direction: 'south',
  code: 'E2+E3+E4'
  },
  {
  id: 26,
  direction: 'south',
  code: 'E2+E3+E4+E5'
  },
  {
  id: 27,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6'
  },
  {
  id: 28,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7'
  },
  {
  id: 29,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8'
  },
  {
  id: 30,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8'
  },
  {
  id: 31,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9'
  },
  {
  id: 32,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9+E10'
  },
  {
  id: 33,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9+E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9+E10+E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9+E10+E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9+E10+E11+E12+E13'
  }
  ]
  },
  {
  id: 22,
  name: 'Maroondah Hwy',
  groupName: 'EastLink',
  marker: '-37.817253, 145.217014',
  highlighted: '',
  geoPointSouthBound: '-37.817274, 145.216888',
  geoPointNorthBound: '-37.817272, 145.216643',
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E1'
  },
  {
  id: 21,
  direction: 'north',
  code: '0'
  },
  {
  id: 23,
  direction: 'south',
  code: 'E2'
  },
  {
  id: 24,
  direction: 'south',
  code: 'E2+E3'
  },
  {
  id: 25,
  direction: 'south',
  code: 'E2+E3+E4'
  },
  {
  id: 26,
  direction: 'south',
  code: 'E2+E3+E4+E5'
  },
  {
  id: 27,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6'
  },
  {
  id: 28,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7'
  },
  {
  id: 29,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8'
  },
  {
  id: 30,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8'
  },
  {
  id: 31,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9'
  },
  {
  id: 32,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9+E10'
  },
  {
  id: 33,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9+E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9+E10+E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9+E10+E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E2+E3+E4+E5+E6+E7+E8+E9+E10+E11+E12+E13'
  }
  ]
  },
  {
  id: 23,
  name: 'Canterbury Road',
  groupName: 'EastLink',
  marker: '-37.832411, 145.217622',
  highlighted: '',
  geoPointSouthBound: '-37.832408, 145.217758',
  geoPointNorthBound: '-37.832412, 145.217477',
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E1+E2'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E2'
  },
  {
  id: 24,
  direction: 'south',
  code: 'E3'
  },
  {
  id: 25,
  direction: 'south',
  code: 'E3+E4'
  },
  {
  id: 26,
  direction: 'south',
  code: 'E3+E4+E5'
  },
  {
  id: 27,
  direction: 'south',
  code: 'E3+E4+E5+E6'
  },
  {
  id: 28,
  direction: 'south',
  code: 'E3+E4+E5+E6+E7'
  },
  {
  id: 29,
  direction: 'south',
  code: 'E3+E4+E5+E6+E7+E8'
  },
  {
  id: 30,
  direction: 'south',
  code: 'E3+E4+E5+E6+E7+E8'
  },
  {
  id: 31,
  direction: 'south',
  code: 'E3+E4+E5+E6+E7+E8+E9'
  },
  {
  id: 32,
  direction: 'south',
  code: 'E3+E4+E5+E6+E7+E8+E9+E10'
  },
  {
  id: 33,
  direction: 'south',
  code: 'E3+E4+E5+E6+E7+E8+E9+E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E3+E4+E5+E6+E7+E8+E9+E10+E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E3+E4+E5+E6+E7+E8+E9+E10+E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E3+E4+E5+E6+E7+E8+E9+E10+E11+E12+E13'
  }
  ]
  },
  {
  id: 24,
  name: 'Boronia Road',
  groupName: 'EastLink',
  marker: '-37.846251, 145.217060',
  highlighted: '',
  geoPointSouthBound: '-37.846270, 145.217196',
  geoPointNorthBound: '-37.846220, 145.216915',
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E3'
  },
  {
  id: 25,
  direction: 'south',
  code: 'E4'
  },
  {
  id: 26,
  direction: 'south',
  code: 'E4+E5'
  },
  {
  id: 27,
  direction: 'south',
  code: 'E4+E5+E6'
  },
  {
  id: 28,
  direction: 'south',
  code: 'E4+E5+E6+E7'
  },
  {
  id: 29,
  direction: 'south',
  code: 'E4+E5+E6+E7+E8'
  },
  {
  id: 30,
  direction: 'south',
  code: 'E4+E5+E6+E7+E8'
  },
  {
  id: 31,
  direction: 'south',
  code: 'E4+E5+E6+E7+E8+E9'
  },
  {
  id: 32,
  direction: 'south',
  code: 'E4+E5+E6+E7+E8+E9+E10'
  },
  {
  id: 33,
  direction: 'south',
  code: 'E4+E5+E6+E7+E8+E9+E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E4+E5+E6+E7+E8+E9+E10+E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E4+E5+E6+E7+E8+E9+E10+E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E4+E5+E6+E7+E8+E9+E10+E11+E12+E13'
  }
  ]
  },
  {
  id: 25,
  name: 'Burwood Hwy',
  groupName: 'EastLink',
  marker: '-37.861243, 145.211876',
  highlighted: '',
  geoPointSouthBound: '-37.861279, 145.212013',
  geoPointNorthBound: '-37.861195, 145.211748',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E4'
  },
  {
  id: 26,
  direction: 'south',
  code: 'E5'
  },
  {
  id: 27,
  direction: 'south',
  code: 'E5+E6'
  },
  {
  id: 28,
  direction: 'south',
  code: 'E5+E6+E7'
  },
  {
  id: 29,
  direction: 'south',
  code: 'E5+E6+E7+E8'
  },
  {
  id: 30,
  direction: 'south',
  code: 'E5+E6+E7+E8'
  },
  {
  id: 31,
  direction: 'south',
  code: 'E5+E6+E7+E8+E9'
  },
  {
  id: 32,
  direction: 'south',
  code: 'E5+E6+E7+E8+E9+E10'
  },
  {
  id: 33,
  direction: 'south',
  code: 'E5+E6+E7+E8+E9+E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E5+E6+E7+E8+E9+E10+E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E5+E6+E7+E8+E9+E10+E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E5+E6+E7+E8+E9+E10+E11+E12+E13'
  }
  ]
  },
  {
  id: 26,
  name: 'High St Road',
  groupName: 'EastLink',
  marker: '-37.876816, 145.212653',
  highlighted: '',
  geoPointSouthBound: '-37.876817, 145.212804',
  geoPointNorthBound: '-37.876809, 145.212512',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E5+E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E5+E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E5+E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E5+E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E5+E4'
  },
  {
  id: 25,
  direction: 'north',
  code: 'E5'
  },
  {
  id: 27,
  direction: 'south',
  code: 'E6'
  },
  {
  id: 28,
  direction: 'south',
  code: 'E6+E7'
  },
  {
  id: 29,
  direction: 'south',
  code: 'E6+E7+E8'
  },
  {
  id: 30,
  direction: 'south',
  code: 'E6+E7+E8'
  },
  {
  id: 31,
  direction: 'south',
  code: 'E6+E7+E8+E9'
  },
  {
  id: 32,
  direction: 'south',
  code: 'E6+E7+E8+E9+E10'
  },
  {
  id: 33,
  direction: 'south',
  code: 'E6+E7+E8+E9+E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E6+E7+E8+E9+E10+E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E6+E7+E8+E9+E10+E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E6+E7+E8+E9+E10+E11+E12+E13'
  }
  ]
  },
  {
  id: 27,
  name: 'Ferntree Gully Road',
  groupName: 'EastLink',
  marker: '-37.902998, 145.215315',
  highlighted: '',
  geoPointSouthBound: '-37.902926, 145.215442',
  geoPointNorthBound: '-37.903076, 145.215189',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E6+E5+E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E6+E5+E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E6+E5+E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E6+E5+E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E6+E5+E4'
  },
  {
  id: 25,
  direction: 'north',
  code: 'E6+E5'
  },
  {
  id: 26,
  direction: 'north',
  code: 'E6'
  },
  {
  id: 28,
  direction: 'south',
  code: 'E7'
  },
  {
  id: 29,
  direction: 'south',
  code: 'E7+E8'
  },
  {
  id: 30,
  direction: 'south',
  code: 'E7+E8'
  },
  {
  id: 31,
  direction: 'south',
  code: 'E7+E8+E9'
  },
  {
  id: 32,
  direction: 'south',
  code: 'E7+E8+E9+E10'
  },
  {
  id: 33,
  direction: 'south',
  code: 'E7+E8+E9+E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E7+E8+E9+E10+E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E7+E8+E9+E10+E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E7+E8+E9+E10+E11+E12+E13'
  }
  ]
  },
  {
  id: 28,
  name: 'Wellington Road',
  groupName: 'EastLink',
  marker: '-37.925431, 145.213137',
  highlighted: '',
  geoPointSouthBound: '-37.925457, 145.213276',
  geoPointNorthBound: '-37.925416, 145.212997',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E7+E6+E5+E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E7+E6+E5+E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E7+E6+E5+E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E7+E6+E5+E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E7+E6+E5+E4'
  },
  {
  id: 25,
  direction: 'north',
  code: 'E7+E6+E5'
  },
  {
  id: 26,
  direction: 'north',
  code: 'E7+E6'
  },
  {
  id: 27,
  direction: 'north',
  code: 'E7'
  },
  {
  id: 29,
  direction: 'south',
  code: 'E8'
  },
  {
  id: 30,
  direction: 'south',
  code: 'E8'
  },
  {
  id: 31,
  direction: 'south',
  code: 'E8+E9'
  },
  {
  id: 32,
  direction: 'south',
  code: 'E8+E9+E10'
  },
  {
  id: 33,
  direction: 'south',
  code: 'E8+E9+E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E8+E9+E10+E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E8+E9+E10+E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E8+E9+E10+E11+E12+E13'
  }
  ]
  },
  {
  id: 29,
  name: 'Police Road',
  groupName: 'EastLink',
  marker: '-37.938833, 145.207162',
  highlighted: 'EastLink M3',
  geoPointSouthBound: '-37.938660, 145.207353',
  geoPointNorthBound: '-37.938594, 145.207072',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E8+E7+E6+E5+E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E8+E7+E6+E5+E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E8+E7+E6+E5+E4'
  },
  {
  id: 25,
  direction: 'north',
  code: 'E8+E7+E6+E5'
  },
  {
  id: 26,
  direction: 'north',
  code: 'E8+E7+E6'
  },
  {
  id: 27,
  direction: 'north',
  code: 'E8+E7'
  },
  {
  id: 28,
  direction: 'north',
  code: 'E8'
  }
  ]
  },
  {
  id: 30,
  name: 'Monash Fwy',
  groupName: 'EastLink',
  marker: '-37.945530, 145.205218',
  highlighted: 'Monash Freeway',
  geoPointSouthBound: '-37.945564, 145.205341',
  geoPointNorthBound: '-37.945445, 145.205095',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E8+E7+E6+E5+E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E8+E7+E6+E5+E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E8+E7+E6+E5+E4'
  },
  {
  id: 25,
  direction: 'north',
  code: 'E8+E7+E6+E5'
  },
  {
  id: 26,
  direction: 'north',
  code: 'E8+E7+E6'
  },
  {
  id: 27,
  direction: 'north',
  code: 'E8+E7'
  },
  {
  id: 28,
  direction: 'north',
  code: 'E8'
  },
  {
  id: 31,
  direction: 'south',
  code: 'E9'
  },
  {
  id: 32,
  direction: 'south',
  code: 'E9+E10'
  },
  {
  id: 33,
  direction: 'south',
  code: 'E9+E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E9+E10+E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E9+E10+E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E9+E10+E11+E12+E13'
  }
  ]
  },
  {
  id: 31,
  name: 'Princes Hwy',
  groupName: 'EastLink',
  marker: '-37.967395, 145.194892',
  highlighted: '',
  geoPointSouthBound: '-37.967512, 145.195002',
  geoPointNorthBound: '-37.967288, 145.194763',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E9+E8+E7+E6+E5+E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E9+E8+E7+E6+E5+E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E9+E8+E7+E6+E5+E4'
  },
  {
  id: 25,
  direction: 'north',
  code: 'E9+E8+E7+E6+E5'
  },
  {
  id: 26,
  direction: 'north',
  code: 'E9+E8+E7+E6'
  },
  {
  id: 27,
  direction: 'north',
  code: 'E9+E8+E7'
  },
  {
  id: 28,
  direction: 'north',
  code: 'E9+E8'
  },
  {
  id: 30,
  direction: 'north',
  code: 'E9'
  },
  {
  id: 32,
  direction: 'south',
  code: 'E10'
  },
  {
  id: 33,
  direction: 'south',
  code: 'E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E10+E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E10+E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E10+E11+E12+E13'
  }
  ]
  },
  {
  id: 32,
  name: 'Cheltenham Road',
  groupName: 'EastLink',
  marker: '-37.992607, 145.191783',
  highlighted: '',
  geoPointSouthBound: '-37.992365, 145.191954',
  geoPointNorthBound: '-37.992426, 145.191645',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5+E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5+E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5+E4'
  },
  {
  id: 25,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5'
  },
  {
  id: 26,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6'
  },
  {
  id: 27,
  direction: 'north',
  code: 'E10+E9+E8+E7'
  },
  {
  id: 28,
  direction: 'north',
  code: 'E10+E9+E8'
  },
  {
  id: 30,
  direction: 'north',
  code: 'E10+E9'
  },
  {
  id: 31,
  direction: 'north',
  code: 'E10'
  }
  ]
  },
  {
  id: 33,
  name: 'Dandenong Bypass',
  groupName: 'EastLink',
  marker: '-38.001231, 145.191434',
  highlighted: '',
  geoPointSouthBound: '-38.001247, 145.191588',
  geoPointNorthBound: '-38.001205, 145.191285',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5+E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5+E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5+E4'
  },
  {
  id: 25,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6+E5'
  },
  {
  id: 26,
  direction: 'north',
  code: 'E10+E9+E8+E7+E6'
  },
  {
  id: 27,
  direction: 'north',
  code: 'E10+E9+E8+E7'
  },
  {
  id: 28,
  direction: 'north',
  code: 'E10+E9+E8'
  },
  {
  id: 30,
  direction: 'north',
  code: 'E10+E9'
  },
  {
  id: 31,
  direction: 'north',
  code: 'E10'
  },
  {
  id: 34,
  direction: 'south',
  code: 'E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E11+E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E11+E12+E13'
  }
  ]
  },
  {
  id: 34,
  name: 'Greens Road',
  groupName: 'EastLink',
  marker: '-38.011348, 145.188485',
  highlighted: '',
  geoPointSouthBound: '-38.011376, 145.188661',
  geoPointNorthBound: '-38.011333, 145.188325',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E11+E10+E9+E8+E7+E6+E5+E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E11+E10+E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E11+E10+E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E11+E10+E9+E8+E7+E6+E5+E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E11+E10+E9+E8+E7+E6+E5+E4'
  },
  {
  id: 25,
  direction: 'north',
  code: 'E11+E10+E9+E8+E7+E6+E5'
  },
  {
  id: 26,
  direction: 'north',
  code: 'E11+E10+E9+E8+E7+E6'
  },
  {
  id: 27,
  direction: 'north',
  code: 'E11+E10+E9+E8+E7'
  },
  {
  id: 28,
  direction: 'north',
  code: 'E11+E10+E9+E8'
  },
  {
  id: 30,
  direction: 'north',
  code: 'E11+E10+E9'
  },
  {
  id: 31,
  direction: 'north',
  code: 'E11+E10'
  },
  {
  id: 33,
  direction: 'north',
  code: 'E11'
  },
  {
  id: 35,
  direction: 'south',
  code: 'E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E12+E13'
  }
  ]
  },
  {
  id: 35,
  name: 'Thompson Road',
  groupName: 'EastLink',
  marker: '-38.070496, 145.181935',
  highlighted: '',
  geoPointSouthBound: '-38.070516, 145.182059',
  geoPointNorthBound: '-38.070480, 145.181810',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E12+E11+E10+E9+E8+E7+E6+E5+E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E12+E11+E10+E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E12+E11+E10+E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E12+E11+E10+E9+E8+E7+E6+E5+E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E12+E11+E10+E9+E8+E7+E6+E5+E4'
  },
  {
  id: 25,
  direction: 'north',
  code: 'E12+E11+E10+E9+E8+E7+E6+E5'
  },
  {
  id: 26,
  direction: 'north',
  code: 'E12+E11+E10+E9+E8+E7+E6'
  },
  {
  id: 27,
  direction: 'north',
  code: 'E12+E11+E10+E9+E8+E7'
  },
  {
  id: 28,
  direction: 'north',
  code: 'E12+E11+E10+E9+E8'
  },
  {
  id: 30,
  direction: 'north',
  code: 'E12+E11+E10+E9'
  },
  {
  id: 31,
  direction: 'north',
  code: 'E12+E11+E10'
  },
  {
  id: 33,
  direction: 'north',
  code: 'E12+E11'
  },
  {
  id: 34,
  direction: 'north',
  code: 'E12'
  },
  {
  id: 36,
  direction: 'south',
  code: 'E13'
  }
  ]
  },
  {
  id: 36,
  name: 'Frankston Fwy/Peninsula Link',
  groupName: 'EastLink',
  marker: '-38.092333, 145.152597',
  highlighted: '',
  geoPointSouthBound: '-38.092449, 145.152642',
  geoPointNorthBound: '-38.092267, 145.152564',
   
  validExits: [
  {
  id: 20,
  direction: 'north',
  code: 'E13+E12+E11+E10+E9+E8+E7+E6+E5+E4+E3+E2+E1'
  },
  {
  id: 21,
  direction: 'north',
  code: 'E13+E12+E11+E10+E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 22,
  direction: 'north',
  code: 'E13+E12+E11+E10+E9+E8+E7+E6+E5+E4+E3+E2'
  },
  {
  id: 23,
  direction: 'north',
  code: 'E13+E12+E11+E10+E9+E8+E7+E6+E5+E4+E3'
  },
  {
  id: 24,
  direction: 'north',
  code: 'E13+E12+E11+E10+E9+E8+E7+E6+E5+E4'
  },
  {
  id: 25,
  direction: 'north',
  code: 'E13+E12+E11+E10+E9+E8+E7+E6+E5'
  },
  {
  id: 26,
  direction: 'north',
  code: 'E13+E12+E11+E10+E9+E8+E7+E6'
  },
  {
  id: 27,
  direction: 'north',
  code: 'E13+E12+E11+E10+E9+E8+E7'
  },
  {
  id: 28,
  direction: 'north',
  code: 'E13+E12+E11+E10+E9+E8'
  },
  {
  id: 30,
  direction: 'north',
  code: 'E13+E12+E11+E10+E9'
  },
  {
  id: 31,
  direction: 'north',
  code: 'E13+E12+E11+E10'
  },
  {
  id: 33,
  direction: 'north',
  code: 'E13+E12+E11'
  },
  {
  id: 34,
  direction: 'north',
  code: 'E13+E12'
  },
  {
  id: 35,
  direction: 'north',
  code: 'E13'
  }
  ]
  }
  ]
  const melbournePrices = [
    {
      id: '0',
      price: {
        car: '0',
        lcv: '0',
        hcv: '0',
        hcvOp: '0',
        mot: '0'
      }
    },
    {
      id: '1A',
      price: {
        car: '2.52',
        lcv: '4.03',
        hcv: '7.55',
        hcvOp: '5.04',
        mot: '1.26'
      }
    },
    {
      id: '1B',
      price: {
        car: '2.52',
        lcv: '4.03',
        hcv: '7.55',
        hcvOp: '5.04',
        mot: '1.26'
      }
    },
    {
      id: '2A',
      price: {
        car: '2.52',
        lcv: '4.03',
        hcv: '7.55',
        hcvOp: '5.04',
        mot: '1.26'
      }
    },
    {
      id: '2B',
      price: {
        car: '2.52',
        lcv: '4.03',
        hcv: '7.55',
        hcvOp: '5.04',
        mot: '1.26'
      }
    },
    {
      id: '3A',
      price: {
        car: '3.15',
        lcv: '5.04',
        hcv: '9.45',
        hcvOp: '6.30',
        mot: '1.57'
      }
    },
    {
      id: '3B',
      price: {
        car: '3.15',
        lcv: '5.04',
        hcv: '9.45',
        hcvOp: '6.30',
        mot: '1.57'
      }
    },
    {
      id: '4A',
      price: {
        car: '2.52',
        lcv: '4.03',
        hcv: '7.55',
        hcvOp: '5.04',
        mot: '1.26'
      }
    },
    {
      id: '4B',
      price: {
        car: '4.09',
        lcv: '6.55',
        hcv: '12.27',
        hcvOp: '8.19',
        mot: '2.05'
      }
    },
    {
      id: '5A',
      price: {
        car: '5.67',
        lcv: '9.07',
        hcv: '17.00',
        hcvOp: '11.33',
        mot: '2.83'
      }
    },
    {
      id: '5B',
      price: {
        car: '5.67',
        lcv: '9.07',
        hcv: '17.00',
        hcvOp: '11.33',
        mot: '2.83'
      }
    },
    {
      id: '6A',
      price: {
        car: '2.52',
        lcv: '4.03',
        hcv: '7.55',
        hcvOp: '5.04',
        mot: '1.26'
      }
    },
    {
      id: '6B',
      price: {
        car: '2.52',
        lcv: '4.03',
        hcv: '7.55',
        hcvOp: '5.04',
        mot: '1.26'
      }
    },
    {
      id: '7A',
      price: {
        car: '2.52',
        lcv: '4.03',
        hcv: '7.55',
        hcvOp: '5.04',
        mot: '1.26'
      }
    },
    {
      id: '8A',
      price: {
        car: '1.57',
        lcv: '2.52',
        hcv: '4.72',
        hcvOp: '3.15',
        mot: '0.79'
      }
    },
    {
      id: '8B',
      price: {
        car: '1.57',
        lcv: '2.52',
        hcv: '4.72',
        hcvOp: '3.15',
        mot: '0.79'
      }
    },
    {
      id: '9A',
      price: {
        car: '1.57',
        lcv: '2.52',
        hcv: '4.72',
        hcvOp: '3.15',
        mot: '0.79'
      }
    },
    {
      id: '9B',
      price: {
        car: '5.67',
        lcv: '9.07',
        hcv: '17.00',
        hcvOp: '11.34',
        mot: '2.83'
      }
    },
    {
      id: 'TRIPCAP',
      price: {
        car: '9.45',
        lcv: '15.11',
        hcv: '28.34',
        hcvOp: '18.89',
        mot: '4.72'
      }
    },
    {
      id: 'FEEVMF',
      price: {
        car: '0.55',
        lcv: '0.55',
        hcv: '0.55',
        mot: '0'
      }
    },
    {
      id: 'CAP24',
      price: {
        car: '18.12',
        lcv: '39.30',
        hcv: '73.21',
        mot: '9.06'
      }
    },
    {
      id: 'CAPWEEKEND',
      price: {
        car: '18.12',
        lcv: '39.30',
        mot: '9.06'
      }
    },
    {
      id: 'CAPTULLA',
      price: {
        car: '6.45',
        lcv: '10.32',
        mot: '3.20'
      }
    },
    {
      id: 'E1',
      price: {
        car: '2.93',
        carWeekend: '2.34',
        carSingle: '2.93',
        lcv: '4.68',
        hcv: '7.74',
        mot: '1.46'
      }
    },
    {
      id: 'E2',
      price: {
        car: '0.43',
        carWeekend: '0.34',
        carSingle: '0.34',
        lcv: '0.68',
        hcv: '1.12',
        mot: '0.21'
      }
    },
    {
      id: 'E3',
      price: {
        car: '0.43',
        carWeekend: '0.34',
        carSingle: '0.34',
        lcv: '0.68',
        hcv: '1.12',
        mot: '0.21'
      }
    },
    {
      id: 'E4',
      price: {
        car: '0.43',
        carWeekend: '0.34',
        carSingle: '0.34',
        lcv: '0.68',
        hcv: '1.12',
        mot: '0.21'
      }
    },
    {
      id: 'E5',
      price: {
        car: '0.43',
        carWeekend: '0.34',
        carSingle: '0.34',
        lcv: '0.68',
        hcv: '1.12',
        mot: '0.21'
      }
    },
    {
      id: 'E6',
      price: {
        car: '0.64',
        carWeekend: '0.51',
        carSingle: '0.51',
        lcv: '1.01',
        hcv: '1.68',
        mot: '0.32'
      }
    },
    {
      id: 'E7',
      price: {
        car: '0.64',
        carWeekend: '0.51',
        carSingle: '0.51',
        lcv: '1.01',
        hcv: '1.68',
        mot: '0.32'
      }
    },
    {
      id: 'E8',
      price: {
        car: '0.64',
        carWeekend: '0.51',
        carSingle: '0.51',
        lcv: '1.01',
        hcv: '1.68',
        mot: '0.32'
      }
    },
    {
      id: 'E9',
      price: {
        car: '0.64',
        carWeekend: '0.51',
        carSingle: '0.51',
        lcv: '1.01',
        hcv: '1.68',
        mot: '0.32'
      }
    },
    {
      id: 'E10',
      price: {
        car: '0.64',
        carWeekend: '0.51',
        carSingle: '0.51',
        lcv: '1.01',
        hcv: '1.68',
        mot: '0.32'
      }
    },
    {
      id: 'E11',
      price: {
        car: '0.64',
        carWeekend: '0.51',
        carSingle: '0.51',
        lcv: '1.01',
        hcv: '1.68',
        mot: '0.32'
      }
    },
    {
      id: 'E12',
      price: {
        car: '1.49',
        carWeekend: '1.19',
        carSingle: '1.19',
        lcv: '2.38',
        hcv: '3.93',
        mot: '0.74'
      }
    },
    {
      id: 'E13',
      price: {
        car: '1.49',
        carWeekend: '1.19',
        carSingle: '1.19',
        lcv: '2.38',
        hcv: '3.93',
        mot: '0.74'
      }
    },
    {
      id: 'CAPEASTLINK',
      price: {
        car: '6.36',
        carWeekend: '5.08',
        lcv: '10.18',
        hcv: '16.85',
        mot: '3.18'
      }
    },
    {
      id: 'IMAGEPROCESSINGEASTLINK',
      price: {
        car: '0.30',
        carWeekend: '0.30',
        lcv: '0.30',
        hcv: '0.30',
        mot: '0'
      }
    }
  ];
  