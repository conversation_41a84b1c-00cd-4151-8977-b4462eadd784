import { EntityType } from '@/interface-models/Generic/EntityType';

export interface RateExpirationSummary {
  attentionRequired: boolean;
  name: string; // The name of the serviceRate table, or the % value of fuel surcharge (as a string)
  validFromDate?: number; // validFromDate of serviceRate, defaultConfig or fuelSurcharge
  validToDate?: number; // validToDate of serviceRate, defaultConfig or fuelSurcharge
  priority?: number; // The higher the number the lower the priority
  // entityType?: EntityType; // FLEET_ASSET, DRIVER etc.
  entityId?: string; // fleetAssetId, driverId etc.
  expirationType?: ExpiringDocumentType;
}

export enum ExpiringDocumentType {
  SERVICE_RATE = 'SERVICE_RATE',
  SERVICE_RATE_VARIATIONS = 'SERVICE_RATE_VARIATIONS',
  FUEL_SURCHARGE = 'FUEL_SURCHARGE',
  DEFAULTS_CONFIGURATION = 'DEFAULTS_CONFIGURATION',
  STATUS = 'STATUS',
  LICENCE = 'LICENCE',
  INDUCTION = 'INDUCTION',
  INSURANCE = 'INSURANCE',
  ADDITIONAL_EQUIPMENT = 'ADDITIONAL_EQUIPMENT',
  HIRE_CONTRACT = 'HIRE_CONTRACT',
  REGISTRATION = 'REGISTRATION',
}

export interface RateExpirationSummaryGroup {
  id: string;
  name: string;
  summaries: RateExpirationSummary[];
  entityType: EntityType; // FLEET_ASSET, DRIVER etc.
  entityId: string; // fleetAssetId, driverId etc.
  relatedGroups?: RateExpirationSummaryGroup[];
}
