import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { FuelLevyChargeBasis } from '@/interface-models/ServiceRates/FuelSurcharge/FuelLevyChargeBasis';
import { FuelSurchargeType } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeType';
import { RangeDeterminant } from '@/interface-models/ServiceRates/FuelSurcharge/RangeDeterminant';
import { RangedFlexRate } from '@/interface-models/ServiceRates/FuelSurcharge/RangedFlexRate';
import { RateBracketType } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';
import { sessionManager } from '@/store/session/SessionState';

export interface IFuelSurchargeRate {
  id?: string;
  company: string;
  division: string;
  uuid: string;
  tableId: number;
  name: string;
  serviceTypes?: number[] | null;
  validFromDate: number | null;
  validToDate: number | null;
  fuelSurchargeApplicationType: FuelSurchargeType;
  rateBracketType?: RateBracketType | null;
  rangeDeterminant?: RangeDeterminant | null;
  rateBrackets: RangedFlexRate[];
  appliedRateBracketId?: string | null;
}

export class FuelSurchargeRate implements IFuelSurchargeRate {
  public id?: string;
  public company: string = sessionManager.getCompanyId();
  public division: string = sessionManager.getDivisionId();
  public uuid: string;
  public tableId: number;
  public name: string = '';
  public serviceTypes?: number[] | null;
  public validFromDate: number | null = null;
  public validToDate: number | null = null;
  public fuelSurchargeApplicationType: FuelSurchargeType =
    FuelSurchargeType.CONSTANT;
  public rateBracketType?: RateBracketType | null = null;
  public rangeDeterminant?: RangeDeterminant | null = null;
  public rateBrackets: RangedFlexRate[] = [];
  public appliedRateBracketId?: string | null = null;

  constructor(init?: Partial<IFuelSurchargeRate>) {
    if (init) {
      Object.assign(this, init);
      this.serviceTypes = Array.isArray(init.serviceTypes)
        ? [...init.serviceTypes]
        : null;
      this.rateBrackets = Array.isArray(init.rateBrackets)
        ? init.rateBrackets.map((rb) => ({ ...rb }))
        : [];
    }
  }

  /**
   * Returns a string description of the rate for display purposes.
   * If the fuel surcharge is a ranged rate, returns 'Various'.
   * Otherwise, returns the formatted rate of the first bracket, or 'N/A' if none exist.
   */
  get rateDescription(): string {
    if (this.fuelSurchargeApplicationType === FuelSurchargeType.RANGED_RATE) {
      return 'Various';
    } else {
      if (this.rateBrackets.length > 0) {
        const bracket = this.rateBrackets[0];
        return [
          bracket.chargeType === FuelLevyChargeBasis.FIXED_CHARGE ? '$' : '',
          DisplayCurrencyValue(bracket.rate),
          bracket.chargeType === FuelLevyChargeBasis.PERCENTAGE ? '%' : '',
        ].join('');
      } else {
        return 'N/A';
      }
    }
  }

  /**
   * Gets the applied fuel surcharge rate (from the first bracket).
   * If no brackets exist, logs an error and returns 0.
   */
  get appliedFuelSurchargeRate(): number {
    if (this.rateBrackets.length > 0) {
      return this.rateBrackets[0].rate;
    }
    console.error('Fuel surcharge rate is not defined.');
    return 0;
  }
  /**
   * Sets the applied fuel surcharge rate (on the first bracket).
   * If no brackets exist, logs an error.
   */
  set appliedFuelSurchargeRate(value: number) {
    if (this.rateBrackets.length > 0) {
      this.rateBrackets[0].rate = value;
    } else {
      console.error(
        'Cannot set appliedFuelSurchargeRate when rateBrackets is empty.',
      );
    }
  }

  /**
   * Checks if a value falls within any of the defined rate brackets.
   * For ranged rates, checks if value is within any bracket's min/max.
   * For constant rates, returns true if any brackets exist.
   * @param value - The value to check (e.g., distance, litres).
   * @returns True if value is within a bracket, false otherwise.
   */
  public isValueWithinBrackets(value: number | undefined | null): boolean {
    if (value === undefined || value === null || isNaN(value) || value < 0) {
      return false;
    }
    if (this.fuelSurchargeApplicationType === FuelSurchargeType.RANGED_RATE) {
      return this.rateBrackets.some(
        (bracket) =>
          value >= bracket.bracketMin &&
          (bracket.bracketMax === -1 || value <= bracket.bracketMax),
      );
    } else {
      return this.rateBrackets.length > 0;
    }
  }
}

export default FuelSurchargeRate;
