/**
 * Used to determine how the rate for fuel surcharge is defined for client, either CONSTANT or RANGED_RATE.
 */
export enum FuelSurchargeType {
  /**
   * Indicates that the rate for fuel surcharge is constant, which means rate
   * can be extracted from the first item.
   */
  CONSTANT = 'CONSTANT',

  /**
   * Indicates that the rate for fuel surcharge is ranged, which means rate
   * needs to be extracted from appropriate rate bracket.
   */
  RANGED_RATE = 'RANGED_RATE',
}

export function returnReadableFuelSurchargeType(
  fuelSurchargeType: FuelSurchargeType,
): string {
  switch (fuelSurchargeType) {
    case FuelSurchargeType.CONSTANT:
      return 'Fixed Rate';
    case FuelSurchargeType.RANGED_RATE:
      return 'Bracketed';
    default:
      return 'Unknown Fuel Surcharge Type';
  }
}
