import {
  FuelSurchargeRate,
  IFuelSurchargeRate,
} from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeRate';
import { FuelSurchargeType } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeType';

export interface FleetAssetFuelSurchargeRateInterface
  extends IFuelSurchargeRate {
  /**
   * Fleet ids for which fuel surcharge is defined. If a fleetAssetId is
   * referenced in this field, then it will be returned by the backend when
   * requesting their applicable fuel surcharges.
   *
   *  - Division: fleetAssetIds = ["0"]; no additional IDs allowed.
   *  - Fleet Asset: Can include one or more fleetAssetIds
   *    FleetAsset.fleetAssetId; must not reference "0" or "CS".
   */
  fleetAssetIds: string[];
}

export class FleetAssetFuelSurchargeRate
  extends FuelSurchargeRate
  implements FleetAssetFuelSurchargeRateInterface
{
  public fleetAssetIds: string[] = [];

  constructor(init?: Partial<FleetAssetFuelSurchargeRateInterface>) {
    super(init);
    this.fleetAssetIds = Array.isArray(init?.fleetAssetIds)
      ? [...init!.fleetAssetIds]
      : [];
  }

  // Returns true if the fuel surcharge fulfils the criteria to serve as a base
  // division-level rate. A base division-level rate is one that has full
  // coverage for all service types + distances.
  get isValidBaseDivisionRate(): boolean {
    const isDivisionRate =
      this.fleetAssetIds.length === 1 && this.fleetAssetIds[0] === '0';
    if (!isDivisionRate) {
      return false;
    }
    if (this.fuelSurchargeApplicationType === FuelSurchargeType.RANGED_RATE) {
      return (
        this.rateBrackets.length > 0 &&
        // Confirm that the first bracket is zero, and max bracket is -1 (for
        // infinity)
        this.rateBrackets[0].bracketMin === 0 &&
        this.rateBrackets[this.rateBrackets.length - 1].bracketMax === -1
      );
    } else {
      // For constant rates, we only need to check if there's at least one rate
      return this.rateBrackets.length === 1;
    }
  }
}

export default FleetAssetFuelSurchargeRate;
