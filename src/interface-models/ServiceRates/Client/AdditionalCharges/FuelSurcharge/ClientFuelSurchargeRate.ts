import {
  FuelSurchargeRate,
  IFuelSurchargeRate,
} from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeRate';
import { FuelSurchargeType } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeType';

export interface ClientFuelSurchargeRateInterface extends IFuelSurchargeRate {
  /**
   * Client ids for which fuel surcharge is defined. If a clientId is referenced
   * in this field, then it will be returned by the backend when requesting
   * their applicable fuel surcharges.
   *
   *  - Division: clientIds = ["0"]; no additional IDs allowed.
   *  - Cash Sale: clientIds = ["CS"]; no additional IDs allowed.
   *  - Client: Can include one or more clientIds  ClientDetails.clientId; must
   *    not reference "0" or "CS".
   */
  clientIds: string[];
}

export class ClientFuelSurchargeRate
  extends FuelSurchargeRate
  implements ClientFuelSurchargeRateInterface
{
  public clientIds: string[] = [];

  constructor(init?: Partial<ClientFuelSurchargeRateInterface>) {
    super(init);
    this.clientIds = Array.isArray(init?.clientIds) ? [...init!.clientIds] : [];
  }

  // Returns true if the fuel surcharge fulfils the criteria to serve as a base
  // division-level rate. A base division-level rate is one that has full
  // coverage for all service types + distances.
  get isValidBaseDivisionRate(): boolean {
    const isDivisionRate =
      this.clientIds.length === 1 && this.clientIds[0] === '0';
    if (!isDivisionRate) {
      return false;
    }
    if (this.fuelSurchargeApplicationType === FuelSurchargeType.RANGED_RATE) {
      return (
        this.rateBrackets.length > 0 &&
        // Confirm that the first bracket is zero, and max bracket is -1 (for
        // infinity)
        this.rateBrackets[0].bracketMin === 0 &&
        this.rateBrackets[this.rateBrackets.length - 1].bracketMax === -1
      );
    } else {
      // For constant rates, we only need to check if there's at least one rate
      return this.rateBrackets.length === 1;
    }
  }
}

export default ClientFuelSurchargeRate;
