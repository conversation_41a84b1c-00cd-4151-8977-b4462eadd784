import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  returnRateTypeLongNameFromId,
  returnServiceTypeLongNameFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { initialiseRateTableItems } from '@/helpers/classInitialisers/InitialiseRateTableItems';
import { Portal } from '@/interface-models/Generic/Portal';
import {
  JobRateType,
  serviceTypeRates,
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import RateTableSettings from '@/interface-models/ServiceRates/RateTableSettings';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import TimeRateType from '../../ServiceTypes/TimeServiceRate/TimeRateType';

export interface ClientServiceRateInterface {
  _id?: string;
  tableId?: number;
  name: string;
  company: string;
  division: string;
  clientId?: string;
  validFromDate: number | null;
  validToDate: number | null;
  outsideMetroRate: number | null; // Float/percentage
  rateTableSettings: RateTableSettings[];
  rateTableItems: RateTableItems[];
  statusList: number[];
}

export class ClientServiceRate implements ClientServiceRateInterface {
  constructor(
    public _id?: string,
    public tableId?: number,
    public name: string = '',
    public company: string = sessionManager.getCompanyId(),
    public division: string = sessionManager.getDivisionId(),
    public clientId?: string,
    public validFromDate: number | null = null,
    public validToDate: number | null = null,
    public outsideMetroRate: number | null = 0, // Float/percentage
    public rateTableSettings: RateTableSettings[] = [],
    public rateTableItems: RateTableItems[] = [],
    public statusList: number[] = [],
  ) {}

  get availableServiceTypes() {
    const sl = useCompanyDetailsStore().getServiceTypesList;

    return sl.filter((serviceType: ServiceTypes) => {
      // If the user is on the client portal we should not add services that have no rates
      // associated with them or if a time rate exists but is $0
      if (
        sessionManager.getPortalType() === Portal.CLIENT &&
        !this.getServiceTypeActive(serviceType.serviceTypeId)
      ) {
        return false;
      }

      if (serviceType.divisionService && serviceType.serviceTypeId !== 4) {
        return true;
      } else {
        const rateTableItemExists = this.rateTableItems.find(
          (rate: RateTableItems) =>
            rate.serviceTypeId === serviceType.serviceTypeId &&
            rate.serviceTypeId !== 4,
        );

        return rateTableItemExists ? true : false;
      }
    });
  }
  // returns boolean value on whether there are valid rates for this service type.
  public getServiceTypeActive(serviceTypeId: number): boolean {
    // check if there are any rates for this serviceTypeId that are not time.
    const noneTimeRate = this.rateTableItems.find(
      (rate: RateTableItems) =>
        rate.serviceTypeId === serviceTypeId && rate.rateTypeId !== 1,
    );
    // if a none time rate is found it means that a none time rate is active.
    const noneTimeRateActive = noneTimeRate ? true : false;

    // find the time rate associated with this serviceTypeId.
    const timeRate: RateTableItems | undefined = this.rateTableItems.find(
      (rate: RateTableItems) =>
        rate.serviceTypeId === serviceTypeId && rate.rateTypeId === 1,
    );
    // If there is no time rate the time rate is empty || if the time rate is defined as $0 the time rate is also empty.
    const timeRateIsActive: boolean = timeRate
      ? !(timeRate.rateTypeObject as TimeRateType).rate ||
        (timeRate.rateTypeObject as TimeRateType).rate <= 0
        ? false
        : true
      : false;
    // return If the time rate is empty and there are no other rate times for this service.
    return timeRateIsActive || noneTimeRateActive;
  }

  public availableRatesByServiceTypeId(serviceTypeId: number) {
    if (!serviceTypeId) {
      return [];
    }

    const adhocRates = serviceTypeRates.filter(
      (rate: ServiceTypeRates) => rate.adhoc,
    );

    const filteredByServiceType = this.rateTableItems.filter(
      (rate: RateTableItems) => {
        return (
          rate.serviceTypeId === serviceTypeId ||
          (rate.serviceTypeId === 4 && rate.rateTypeId === 5)
        );
      },
    );
    const items: ServiceTypeRates[] = [];
    for (const item of filteredByServiceType) {
      for (const rate of serviceTypeRates) {
        if (item.rateTypeId === rate.rateTypeId) {
          // if we are on the client portal and the time rate is <= 0 we should not
          // include it as a selectable option.
          if (
            sessionManager.getPortalType() === Portal.CLIENT &&
            item.rateTypeId === 1 &&
            (!(item.rateTypeObject as TimeRateType).rate ||
              (item.rateTypeObject as TimeRateType).rate <= 0)
          ) {
            continue;
          }
          items.push(rate);
        }
      }
    }

    return sessionManager.getPortalType() !== Portal.CLIENT
      ? items.concat(adhocRates)
      : items;
  }

  // appliedSubsetRates is referring to zone and unit rates. eg. If allSubRates
  // is true it will return all unit rate types. If false it will just return
  // unit rates that should be applied to the job.
  public rateToApplyToJob(
    serviceTypeId: number,
    rateTypeId: number,
  ): RateTableItems | null {
    if (!serviceTypeId || !rateTypeId || !this.rateTableItems.length) {
      return null;
    }
    // If the rate type is UNIT, we should use serviceTypeId 4 (Unit Rate)
    if (rateTypeId === JobRateType.UNIT) {
      serviceTypeId = 4;
    }
    // Look for matching rateTableItem from serviceTypeId and rateTypeId, then
    // copy to local variable so we're not mutating original
    const foundRateTableItem = this.rateTableItems.find(
      (rate) =>
        rate.serviceTypeId === serviceTypeId && rate.rateTypeId === rateTypeId,
    );
    const foundRate: RateTableItems | null = foundRateTableItem
      ? initialiseRateTableItems(foundRateTableItem)
      : null;
    if (!foundRate) {
      const serviceTypeName: string = returnServiceTypeLongNameFromId(
        serviceTypeId,
        'service',
      );
      const rateTypeName: string = returnRateTypeLongNameFromId(
        rateTypeId,
        'rate',
      );
      if (rateTypeId !== JobRateType.TRIP) {
        showNotification(
          `Client does not have ${serviceTypeName} ${rateTypeName} rate available.`,
        );
      } else {
        showNotification(`Please fill in required pricing for ${rateTypeName}`);
      }
    }
    return foundRate ?? null;
  }
}

export default ClientServiceRate;
