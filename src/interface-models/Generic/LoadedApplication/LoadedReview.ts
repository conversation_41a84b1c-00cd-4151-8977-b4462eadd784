interface ILoadedReview {
  fleetAssetFuelSurcharge: boolean;
  fleetAssetServiceRate: boolean;
  clientFuelSurcharge: boolean;
  clientServiceRate: boolean;
  clientDetails: boolean;
  breakDurations: boolean;
}

export default class LoadedReview implements ILoadedReview {
  constructor(
    public fleetAssetFuelSurcharge: boolean = false,
    public fleetAssetServiceRate: boolean = false,
    public clientFuelSurcharge: boolean = false,
    public clientServiceRate: boolean = false,
    public clientDetails: boolean = false,
    public breakDurations: boolean = false,
  ) {}

  get finishedLoading() {
    return Object.keys(this).every((k) => this[k]);
  }
}
