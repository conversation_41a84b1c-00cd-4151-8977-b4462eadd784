import {
  GST_RATE,
  returnRoundedChargeTypeSubtotal,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  returnFormattedDate,
  returnFormattedTime,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnCorrectDuration } from '@/helpers/DateTimeHelpers/DurationHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  computeAdditionalChargeSubtotals,
  computeAdditionalChargeSubtotalValueForRateBasis,
} from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import { generateDemurrages } from '@/helpers/RateHelpers/DemurrageHelpers';
import {
  generateDistanceRateData,
  returnOriginalDistanceForChargeBasis,
} from '@/helpers/RateHelpers/DistanceRateHelpers';
import { generateFuelSurcharges } from '@/helpers/RateHelpers/FuelSurchargeHelpers';
import { isDistanceRateData } from '@/helpers/RateHelpers/RateDataHelpers';
import {
  generateAccountingFinishedJobData,
  requiredBreakFromDuration,
  standbyFuelSurchargeApplies,
} from '@/helpers/RateHelpers/RateHelpers';
import {
  isDistanceRateTypeObject,
  isZoneToZoneRateTypeObject,
} from '@/helpers/RateHelpers/RateTableItemHelpers';
import { generateStandbyRateTotals } from '@/helpers/RateHelpers/StandbyHelpers';
import {
  firstAndLastLegDuration,
  timeRateCalculation,
} from '@/helpers/RateHelpers/TimeRateHelpers';
import { unitRateCalculation } from '@/helpers/RateHelpers/UnitRateHelpers';
import {
  fleetAssetZoneBreakDown,
  generateZoneBreakdowns,
} from '@/helpers/RateHelpers/ZoneRateHelpers';
import { generateZoneToZoneRateData } from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import { AdditionalChargeApplicationType } from '@/interface-models/AdditionalCharges/AdditionalChargeApplicationType';
import { AdditionalChargeRateBasis } from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import HireContract from '@/interface-models/FleetAsset/EquipmentHire/HireContract';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { AdditionalAccountingData } from '@/interface-models/Generic/Accounting/AdditionalAccountingData';
import FinishedJobData from '@/interface-models/Generic/Accounting/FinishedJobDetails/FinishedJobData';
import StandbyCharge from '@/interface-models/Generic/Accounting/Standby/StandbyCharge';
import StandbyChargeBreakdown from '@/interface-models/Generic/Accounting/Standby/StandbyChargeBreakdown';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import AdditionalAsset from '@/interface-models/Jobs/AdditionalAsset';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import FinishedJobDetails from '@/interface-models/Jobs/FinishedJobDetails/FinishedJobDetails';
import { ZoneToZoneRateData } from '@/interface-models/Jobs/FinishedJobDetails/ZoneToZoneRate/ZoneToZoneRateData';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import { FuelSurchargeCalculations } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeCalculations';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { ChargeBasis } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import EquipmentHireTimeRate from '@/interface-models/ServiceRates/ServiceTypes/EquipmentHireTimeRate/EquipmentHireTimeRate';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import TripRate from '@/interface-models/ServiceRates/ServiceTypes/TripRate/TripRate';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';
import { AdditionalChargeList } from './AdditionalChargeList';
import EquipmentHireRate from './EquipmentHireRate';
import { AdditionalChargeSubtotal } from './JobAccountingTotals/AdditionalChargeSubtotal';
import ChargeTypeSubtotal from './JobAccountingTotals/ChargeTypeSubtotal';
import JobAccountingMargin from './JobAccountingTotals/JobAccountingMargin';
import { JobAccountingTotals } from './JobAccountingTotals/JobAccountingTotals';
import { TollChargeBreakdown } from './JobAccountingTotals/TollChargeBreakdown';
import JobPrimaryRate from './JobPrimaryRate';
import { TimeTypeJobRateData } from './JobRateData/TimeTypeJobRateData';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';

export interface JobAccountingDetailsInterface {
  invoiceId: string;
  rctiId: string;
  clientRates: JobPrimaryRate[];
  fleetAssetRates: JobPrimaryRate[];
  clientServiceRateVariations?: ClientServiceRateVariations | null;
  additionalCharges: AdditionalChargeList;
  totals: JobAccountingTotals;
  finishedJobData: FinishedJobData;
  // Frontend only field
  additionalData?: AdditionalAccountingData | null;
}

export class JobAccountingDetails implements JobAccountingDetailsInterface {
  public additionalData?: AdditionalAccountingData | null;
  public clientServiceRateVariations?: ClientServiceRateVariations | null;
  constructor(
    public finishedJobData: FinishedJobData = new FinishedJobData(),
    public invoiceId: string = '',
    public rctiId: string = '',
    public clientRates: JobPrimaryRate[] = [],
    public fleetAssetRates: JobPrimaryRate[] = [],
    public additionalCharges: AdditionalChargeList = new AdditionalChargeList(),
    public totals: JobAccountingTotals = new JobAccountingTotals(),
  ) {}

  public returnItemizedTotals(
    jobDetails: JobDetails,
    fleetAssetOwner: FleetAssetOwnerSummary | null,
    additionalChargeTypeList: AdditionalChargeType[],
    equipmentHireContracts: HireContract[],
    clientOutsideMetroRate: number | null,
    fleetAssetOutsideMetroRate: number | null,
    commonAddresses: ClientCommonAddress[],
  ): {
    totals: JobAccountingTotals;
    jobDurationData: FinishedJobDetails;
  } | null {
    try {
      const clientGstRegistered = true;
      const fleetAssetGstRegistered = fleetAssetOwner
        ? fleetAssetOwner.gstRegistered
        : false;
      const jobData: FinishedJobDetails = this.generateFinishedJobDetails(
        jobDetails,
        fleetAssetGstRegistered,
      );

      const baseFreightCharge = returnRoundedChargeTypeSubtotal(
        jobData.clientRateAmount,
        jobData.fleetAssetRateAmount,
      );
      const baseFreightGstCharge = returnRoundedChargeTypeSubtotal(
        clientGstRegistered ? baseFreightCharge.client * GST_RATE : 0,
        fleetAssetGstRegistered ? baseFreightCharge.fleetAsset * GST_RATE : 0,
      );

      // =========================================================================
      //  FREIGHT AND OVERALL ADJUSTMENTS
      // =========================================================================

      const clientVariancePct =
        this.clientServiceRateVariations?.clientAdjustmentPercentage;
      const fleetAssetVariancePct =
        this.clientServiceRateVariations?.fleetAssetAdjustmentPercentage;

      // Use the division customConfig to determine if the rate variations apply
      // to demurrage calculations. If applyRateVariationsToDemurrage is true,
      // use the regular percent for demurrage. If false, use 0
      const clientDemurrageVariancePct = useCompanyDetailsStore()
        .divisionCustomConfig?.accounting?.applyRateVariationsToDemurrage
        ? clientVariancePct
        : 0;
      const fleetAssetDemurrageVariancePct = useCompanyDetailsStore()
        .divisionCustomConfig?.accounting?.applyRateVariationsToDemurrage
        ? fleetAssetVariancePct
        : 0;

      // Calculate the subtotals for all freight-type additional charges. These
      // are additional charges that adjust the freight charge, and will have fuel
      // levy and other charges applies to them.
      const freightAdditionalChargeSubtotals: AdditionalChargeSubtotal[] =
        computeAdditionalChargeSubtotals({
          type: AdditionalChargeApplicationType.FREIGHT,
          additionalChargeTypes: additionalChargeTypeList,
          additionalChargeItems: this.additionalCharges.chargeList,
          clientApplicableCharge: baseFreightCharge.client,
          fleetAssetApplicableCharge: baseFreightCharge.fleetAsset,
          clientGstRegistered: clientGstRegistered,
          fleetAssetGstRegistered: fleetAssetGstRegistered,
        });

      const freightChargeSummedSubtotal: ChargeTypeSubtotal =
        returnRoundedChargeTypeSubtotal(
          freightAdditionalChargeSubtotals.reduce(
            (acc, charge) => acc + charge.total.client,
            0,
          ),
          freightAdditionalChargeSubtotals.reduce(
            (acc, charge) => acc + charge.total.fleetAsset,
            0,
          ),
        );
      const freightChargeSummedGstSubtotal: ChargeTypeSubtotal =
        returnRoundedChargeTypeSubtotal(
          clientGstRegistered
            ? freightChargeSummedSubtotal.client * GST_RATE
            : 0,
          fleetAssetGstRegistered
            ? freightChargeSummedSubtotal.fleetAsset * GST_RATE
            : 0,
        );

      // Get the fixed and percentage portions of the summed subtotal
      const fixedFreightAdditionalChargeSubtotal =
        computeAdditionalChargeSubtotalValueForRateBasis(
          this.additionalCharges.chargeList,
          freightAdditionalChargeSubtotals,
          AdditionalChargeRateBasis.FIXED,
        );

      // =========================================================================
      // FREIGHT CHARGE
      // =========================================================================
      const freightCharge: ChargeTypeSubtotal = returnRoundedChargeTypeSubtotal(
        baseFreightCharge.client + freightChargeSummedSubtotal.client,
        baseFreightCharge.fleetAsset + freightChargeSummedSubtotal.fleetAsset,
      );
      const freightGstCharge: ChargeTypeSubtotal =
        returnRoundedChargeTypeSubtotal(
          clientGstRegistered ? freightCharge.client * GST_RATE : 0,
          fleetAssetGstRegistered ? freightCharge.fleetAsset * GST_RATE : 0,
        );

      // =========================================================================
      // OUTSIDE METRO CHARGE
      // =========================================================================
      const clientOutsideMetroChargeHolder = clientOutsideMetroRate
        ? RoundCurrencyValue(
            (baseFreightCharge.client +
              fixedFreightAdditionalChargeSubtotal.client) *
              (clientOutsideMetroRate / 100),
          )
        : 0;
      const fleetAssetOutsideMetroChargeHolder = fleetAssetOutsideMetroRate
        ? RoundCurrencyValue(
            (baseFreightCharge.fleetAsset +
              fixedFreightAdditionalChargeSubtotal.fleetAsset) *
              (fleetAssetOutsideMetroRate / 100),
          )
        : 0;

      const outsideMetroCharge = returnRoundedChargeTypeSubtotal(
        clientOutsideMetroChargeHolder,
        fleetAssetOutsideMetroChargeHolder,
      );
      const outsideMetroGstCharge = returnRoundedChargeTypeSubtotal(
        clientGstRegistered ? outsideMetroCharge.client * GST_RATE : 0,
        fleetAssetGstRegistered ? outsideMetroCharge.fleetAsset * GST_RATE : 0,
      );
      // =========================================================================
      // Standby CHARGE
      // =========================================================================

      const standbyCharge = returnRoundedChargeTypeSubtotal(
        jobData.clientStandbyRateAmount,
        jobData.fleetAssetStandbyRateAmount,
      );
      const standbyGstCharge = returnRoundedChargeTypeSubtotal(
        clientGstRegistered ? standbyCharge.client * GST_RATE : 0,
        fleetAssetGstRegistered ? standbyCharge.fleetAsset * GST_RATE : 0,
      );

      // Generate standby breakdown totals for client and fleet asset
      const clientStandbyChargeBreakdown: StandbyChargeBreakdown[] =
        this.clientRates[0].rate.rateTypeId === JobRateType.TIME
          ? generateStandbyRateTotals(
              (this.clientRates[0].rate.rateTypeObject as TimeRateType)
                .standbyRate,
              (this.clientRates[0].rate.rateTypeObject as TimeRateType)
                .standbyMultiplier,
              this.clientRates[0].standbyDuration.durations
                ? this.clientRates[0].standbyDuration.durations
                : [],
              clientGstRegistered,
            )
          : [];

      const fleetAssetStandbyChargeBreakdown: StandbyChargeBreakdown[] =
        this.fleetAssetRates[0].rate.rateTypeId === JobRateType.TIME
          ? generateStandbyRateTotals(
              (this.fleetAssetRates[0].rate.rateTypeObject as TimeRateType)
                .standbyRate,
              (this.fleetAssetRates[0].rate.rateTypeObject as TimeRateType)
                .standbyMultiplier,
              this.fleetAssetRates[0].standbyDuration.durations
                ? this.fleetAssetRates[0].standbyDuration.durations
                : [],
              fleetAssetGstRegistered,
            )
          : [];

      // =========================================================================
      // FUEL SURCHARGE
      // =========================================================================
      if (
        this.additionalCharges.clientFuelSurcharge?.rateBrackets &&
        this.additionalCharges.clientFuelSurcharge.rateBrackets.length > 1
      ) {
        throw new Error(
          'Fuel surcharge rateBrackets should have a maximum of one element when used in JobDetails',
        );
      }
      // const isFuelSurcharge =
      //   this.additionalCharges.clientFuelSurcharge instanceof
      //   ClientFuelSurchargeRate;
      // const addit = this.additionalCharges;
      const clientFuelSurchargeRate =
        this.additionalCharges.clientFuelSurcharge?.appliedFuelSurchargeRate ||
        0;
      const driverFuelSurchargeRate =
        this.additionalCharges.fleetAssetFuelSurcharge
          ?.appliedFuelSurchargeRate || 0;

      // =========================================================================
      // DEMURRAGE CHARGE
      // =========================================================================
      this.finishedJobData.clientDemurrageBreakdown = generateDemurrages({
        rate: this.clientRates[0].rate,
        jobDetails,
        commonAddresses,
        gstRegistered: clientGstRegistered,
        existingDemurrageRateData:
          this.finishedJobData.clientDemurrageBreakdown,
        isClient: true,
        workDiaryList: this.clientRates[0].breakDuration?.breakSummaryList
          ? this.clientRates[0].breakDuration.breakSummaryList
          : [],
        clientRateInfo: null,
        variancePct: clientDemurrageVariancePct,
      });

      this.finishedJobData.fleetAssetDemurrageBreakdown = generateDemurrages({
        rate: this.fleetAssetRates[0].rate,
        jobDetails,
        commonAddresses,
        gstRegistered: clientGstRegistered,
        existingDemurrageRateData:
          this.finishedJobData.fleetAssetDemurrageBreakdown,
        isClient: false,
        workDiaryList: this.fleetAssetRates[0].breakDuration?.breakSummaryList
          ? this.fleetAssetRates[0].breakDuration.breakSummaryList
          : [],
        clientRateInfo: {
          clientFuelSurchargeRate,
          clientRate: this.clientRates[0].rate,
          clientDemurrages: this.finishedJobData.clientDemurrageBreakdown,
        },
        variancePct: fleetAssetDemurrageVariancePct,
      });
      let clientDemurrageChargeExclGst = 0;
      let clientDemurrageChargeGst = 0;

      for (const clientDemurrage of this.finishedJobData
        .clientDemurrageBreakdown) {
        clientDemurrageChargeExclGst += clientDemurrage.demurrageChargeExclGst;
        clientDemurrageChargeGst += clientDemurrage.demurrageChargeGst;
      }
      let fleetAssetDemurrageChargeExclGst = 0;
      let fleetAssetDemurrageChargeGst = 0;

      for (const fleetAssetDemurrage of this.finishedJobData
        .fleetAssetDemurrageBreakdown) {
        fleetAssetDemurrageChargeExclGst +=
          fleetAssetDemurrage.demurrageChargeExclGst;
        fleetAssetDemurrageChargeGst += fleetAssetDemurrage.demurrageChargeGst;
      }

      const demurrageChargeTotals = returnRoundedChargeTypeSubtotal(
        clientDemurrageChargeExclGst,
        fleetAssetDemurrageChargeExclGst,
      );
      const demurrageChargeGstTotals = returnRoundedChargeTypeSubtotal(
        clientDemurrageChargeGst,
        fleetAssetDemurrageChargeGst,
      );

      // =========================================================================
      // FUEL SURCHARGE
      // =========================================================================

      // client and fleet asset fuel surcharge rate variables are above in our demurrage section.
      // Find if fuel surcharge should be applied to our fuel surcharge rate calculation
      const clientStandbyFuelSurchargeApplies: boolean =
        standbyFuelSurchargeApplies(this.clientRates[0].rate);
      const driverStandbyFuelSurchargeApplies: boolean =
        standbyFuelSurchargeApplies(this.fleetAssetRates[0].rate);
      // if standby fuel surcharge rate applies we set our standby rate else set value to 0;
      const clientFuelStandbyCharge = clientStandbyFuelSurchargeApplies
        ? standbyCharge.client
        : 0;
      const driverFuelStandbyCharge = driverStandbyFuelSurchargeApplies
        ? standbyCharge.fleetAsset
        : 0;

      const fuelSurcharge = new ChargeTypeSubtotal();
      const fuelGst = new ChargeTypeSubtotal();

      // TODO: Need a better solution to generateAccountingFinishedJobData. Gets called too many times.
      generateAccountingFinishedJobData(
        jobDetails,
        this,
        jobData,
        fleetAssetGstRegistered,
      );

      const clientFuelSurchargeCalculations: FuelSurchargeCalculations =
        generateFuelSurcharges({
          type: RateEntityType.CLIENT,
          rate: jobDetails.accounting.clientRates[0].rate,
          fuelSurchargeRate: clientFuelSurchargeRate,
          clientFuelSurchargeRate: null,
          freightCharge: freightCharge.client,
          standbyCharge: clientFuelStandbyCharge,
          outsideMetroCharge: clientOutsideMetroChargeHolder,
          gstRegistered: clientGstRegistered,
          rateData: this.finishedJobData.clientRateData,
          demurrageBreakdown: this.finishedJobData.clientDemurrageBreakdown,
          standbyBreakdown: clientStandbyChargeBreakdown,
          freightAdjustmentCharge: freightChargeSummedSubtotal.client || 0,
        });

      clientFuelSurchargeCalculations.fuelSurchargeExclGst = RoundCurrencyValue(
        clientFuelSurchargeCalculations.fuelSurchargeExclGst,
      );
      clientFuelSurchargeCalculations.fuelSurchargeGst = RoundCurrencyValue(
        clientFuelSurchargeCalculations.fuelSurchargeGst,
      );
      const driverFuelSurchargeCalculations: FuelSurchargeCalculations =
        generateFuelSurcharges({
          type: RateEntityType.FLEET_ASSET,
          rate: jobDetails.accounting.fleetAssetRates[0].rate,
          fuelSurchargeRate: driverFuelSurchargeRate,
          clientFuelSurchargeRate,
          freightCharge: freightCharge.fleetAsset,
          standbyCharge: driverFuelStandbyCharge,
          outsideMetroCharge: fleetAssetOutsideMetroChargeHolder,
          gstRegistered: fleetAssetGstRegistered,
          rateData: this.finishedJobData.fleetAssetRateData,
          demurrageBreakdown: this.finishedJobData.fleetAssetDemurrageBreakdown,
          standbyBreakdown: fleetAssetStandbyChargeBreakdown,
          freightAdjustmentCharge: freightChargeSummedSubtotal.fleetAsset || 0,
        });

      driverFuelSurchargeCalculations.fuelSurchargeExclGst = RoundCurrencyValue(
        driverFuelSurchargeCalculations.fuelSurchargeExclGst,
      );
      driverFuelSurchargeCalculations.fuelSurchargeGst = RoundCurrencyValue(
        driverFuelSurchargeCalculations.fuelSurchargeGst,
      );

      // apply calculated fuel surcharge totals
      fuelSurcharge.client =
        clientFuelSurchargeCalculations.fuelSurchargeExclGst;
      fuelGst.client = clientFuelSurchargeCalculations.fuelSurchargeGst;
      this.finishedJobData.clientDemurrageFuelSurchargeBreakdown =
        clientFuelSurchargeCalculations.demurrageFuelSurchargeBreakdown;
      this.finishedJobData.clientStandbyFuelSurchargeBreakdown =
        clientFuelSurchargeCalculations.standbyFuelSurchargeBreakdown;
      fuelSurcharge.fleetAsset =
        driverFuelSurchargeCalculations.fuelSurchargeExclGst;
      fuelGst.fleetAsset = driverFuelSurchargeCalculations.fuelSurchargeGst;
      this.finishedJobData.fleetAssetDemurrageFuelSurchargeBreakdown =
        driverFuelSurchargeCalculations.demurrageFuelSurchargeBreakdown;
      this.finishedJobData.fleetAssetStandbyFuelSurchargeBreakdown =
        driverFuelSurchargeCalculations.standbyFuelSurchargeBreakdown;

      // =========================================================================
      // Additional Charges
      // =========================================================================
      // Sum together the totals thus far. This will be used in the additional
      // charge calculations
      const nonFreightAdditionalChargeApplicableTotal = new ChargeTypeSubtotal(
        freightCharge.client +
          standbyCharge.client +
          demurrageChargeTotals.client +
          fuelSurcharge.client,
        freightCharge.fleetAsset +
          standbyCharge.fleetAsset +
          demurrageChargeTotals.fleetAsset +
          fuelSurcharge.fleetAsset,
      );

      const nonFreightAdditionalChargeSubtotals: AdditionalChargeSubtotal[] =
        computeAdditionalChargeSubtotals({
          type: AdditionalChargeApplicationType.NON_FREIGHT,
          additionalChargeTypes: additionalChargeTypeList,
          additionalChargeItems: this.additionalCharges.chargeList,
          clientApplicableCharge:
            nonFreightAdditionalChargeApplicableTotal.client,
          fleetAssetApplicableCharge:
            nonFreightAdditionalChargeApplicableTotal.fleetAsset,
          clientGstRegistered: clientGstRegistered,
          fleetAssetGstRegistered: fleetAssetGstRegistered,
        });

      const additionalChargeTotal = new ChargeTypeSubtotal();
      const additionalChargeGstTotal = new ChargeTypeSubtotal();
      nonFreightAdditionalChargeSubtotals.forEach((charge) => {
        additionalChargeTotal.client += charge.total.client;
        additionalChargeTotal.fleetAsset += charge.total.fleetAsset;
        additionalChargeGstTotal.client += charge.gstCharge.client;
        additionalChargeGstTotal.fleetAsset += charge.gstCharge.fleetAsset;
      });

      additionalChargeTotal.client = RoundCurrencyValue(
        additionalChargeTotal.client,
      );
      additionalChargeTotal.fleetAsset = RoundCurrencyValue(
        additionalChargeTotal.fleetAsset,
      );

      additionalChargeGstTotal.client = RoundCurrencyValue(
        additionalChargeGstTotal.client,
      );
      additionalChargeGstTotal.fleetAsset = RoundCurrencyValue(
        additionalChargeGstTotal.fleetAsset,
      );

      const allAdditionalChargeSubtotals = [
        ...nonFreightAdditionalChargeSubtotals,
        ...freightAdditionalChargeSubtotals,
      ];

      // =========================================================================
      //  EQUIPMENT HIRE
      // =========================================================================
      const equipmentHireCharges: EquipmentHireRate[] = [];
      let equipmentHireTotal: number = 0;
      const hiredAssets = jobDetails.additionalAssets
        ? jobDetails.additionalAssets.filter(
            (x: AdditionalAsset) => x.contractId !== '',
          )
        : [];
      for (const asset of hiredAssets) {
        const contract: HireContract | undefined = equipmentHireContracts.find(
          (x: HireContract) => x._id === asset.contractId,
        );

        let contractJobRate: EquipmentHireTimeRate | undefined;

        if (contract) {
          contractJobRate = new EquipmentHireTimeRate(
            contract.jobRate,
            contract.jobRateMultiplier,
          );

          const chargeTotal = timeRateCalculation({
            rate: contractJobRate.jobRate,
            multiplier: contract.jobRateMultiplier,
            durationInMs: jobData.fleetAssetDurations.actualBilledDuration,
          });

          equipmentHireTotal = RoundCurrencyValue(
            equipmentHireTotal + chargeTotal,
          );

          if (contractJobRate) {
            const rate: EquipmentHireRate = new EquipmentHireRate(
              asset.assetId,
              asset.contractId,
              contractJobRate,
              chargeTotal,
            );

            equipmentHireCharges.push(rate);
          }
        }
      }
      // =========================================================================
      // GST Total
      // =========================================================================

      const gstTotal = new ChargeTypeSubtotal();
      gstTotal.client = RoundCurrencyValue(
        freightGstCharge.client +
          additionalChargeGstTotal.client +
          fuelGst.client +
          standbyGstCharge.client +
          outsideMetroGstCharge.client +
          demurrageChargeGstTotals.client,
      );
      gstTotal.fleetAsset = RoundCurrencyValue(
        freightGstCharge.fleetAsset +
          additionalChargeGstTotal.fleetAsset +
          fuelGst.fleetAsset +
          standbyGstCharge.fleetAsset +
          outsideMetroGstCharge.fleetAsset +
          demurrageChargeGstTotals.fleetAsset,
      );

      // =========================================================================
      // Overall Totals
      // =========================================================================

      const finalTotalLessGst = new ChargeTypeSubtotal();
      finalTotalLessGst.client = RoundCurrencyValue(
        freightCharge.client +
          additionalChargeTotal.client +
          fuelSurcharge.client +
          standbyCharge.client +
          demurrageChargeTotals.client,
      );

      finalTotalLessGst.fleetAsset = RoundCurrencyValue(
        freightCharge.fleetAsset +
          additionalChargeTotal.fleetAsset +
          fuelSurcharge.fleetAsset +
          standbyCharge.fleetAsset +
          demurrageChargeTotals.fleetAsset,
      );

      const finalTotal = new ChargeTypeSubtotal();

      finalTotal.client = RoundCurrencyValue(
        finalTotalLessGst.client + gstTotal.client,
      );
      finalTotal.fleetAsset = RoundCurrencyValue(
        finalTotalLessGst.fleetAsset + gstTotal.fleetAsset,
      );

      // =========================================================================
      // Toll Subtotals
      // =========================================================================
      const tollChargeSubtotals = new TollChargeBreakdown();
      const tollCategoryId = additionalChargeTypeList.find(
        (type) => type.longName === 'Tolls',
      )?._id;

      if (!tollCategoryId) {
        throw new Error(
          'Toll charge category not found in additional charge type list',
        );
      }

      const foundChargeGroup = allAdditionalChargeSubtotals.find(
        (type) => type.chargeRef === tollCategoryId,
      );
      if (foundChargeGroup) {
        tollChargeSubtotals.tollCharges = foundChargeGroup.total;
        tollChargeSubtotals.tollGstCharges = foundChargeGroup.gstCharge;
        tollChargeSubtotals.tollTotal.client = RoundCurrencyValue(
          foundChargeGroup.total.client + foundChargeGroup.gstCharge.client,
        );
        tollChargeSubtotals.tollTotal.fleetAsset = RoundCurrencyValue(
          foundChargeGroup.total.fleetAsset +
            foundChargeGroup.gstCharge.fleetAsset,
        );
        tollChargeSubtotals.jobTotalMinusTollCharges.client =
          RoundCurrencyValue(finalTotal.client - foundChargeGroup.total.client);
        tollChargeSubtotals.jobTotalMinusTollCharges.fleetAsset =
          RoundCurrencyValue(
            finalTotal.fleetAsset - foundChargeGroup.total.fleetAsset,
          );

        tollChargeSubtotals.jobGstMinusTollGst.client = RoundCurrencyValue(
          gstTotal.client - foundChargeGroup.gstCharge.client,
        );
        tollChargeSubtotals.jobGstMinusTollGst.fleetAsset = RoundCurrencyValue(
          gstTotal.fleetAsset - foundChargeGroup.gstCharge.fleetAsset,
        );
      }

      freightCharge.client = RoundCurrencyValue(
        freightCharge.client + outsideMetroCharge.client,
      );
      freightCharge.fleetAsset = RoundCurrencyValue(
        freightCharge.fleetAsset + outsideMetroCharge.fleetAsset,
      );

      freightGstCharge.client = RoundCurrencyValue(
        freightGstCharge.client + outsideMetroGstCharge.client,
      );
      freightGstCharge.fleetAsset = RoundCurrencyValue(
        freightGstCharge.fleetAsset + outsideMetroGstCharge.fleetAsset,
      );

      finalTotalLessGst.client = RoundCurrencyValue(
        finalTotalLessGst.client + outsideMetroCharge.client,
      );
      finalTotalLessGst.fleetAsset = RoundCurrencyValue(
        finalTotalLessGst.fleetAsset + outsideMetroCharge.fleetAsset,
      );

      finalTotal.client = RoundCurrencyValue(
        finalTotal.client + outsideMetroCharge.client,
      );
      finalTotal.fleetAsset = RoundCurrencyValue(
        finalTotal.fleetAsset + outsideMetroCharge.fleetAsset,
      );

      // =========================================================================
      // Margin Calculations - no equipment hire
      // =========================================================================

      const clientFreightTotal = RoundCurrencyValue(
        freightCharge.client +
          standbyCharge.client +
          demurrageChargeTotals.client,
      );
      const fleetAssetFreightTotal = RoundCurrencyValue(
        freightCharge.fleetAsset +
          standbyCharge.fleetAsset +
          demurrageChargeTotals.fleetAsset,
      );

      const overallMargin = new JobAccountingMargin(
        RoundCurrencyValue(clientFreightTotal - fleetAssetFreightTotal),
        RoundCurrencyValue(
          ((clientFreightTotal - fleetAssetFreightTotal) / clientFreightTotal) *
            100,
        ),
      );
      // freightCharge
      // =========================================================================
      // Margin Calculations - Equipment Hire
      // =========================================================================
      const hireOverallMargin = new JobAccountingMargin(
        RoundCurrencyValue(
          clientFreightTotal - fleetAssetFreightTotal - equipmentHireTotal,
        ),
        RoundCurrencyValue(
          ((clientFreightTotal - fleetAssetFreightTotal - equipmentHireTotal) /
            clientFreightTotal) *
            100,
        ),
      );

      // =========================================================================
      // Set Data to return
      // =========================================================================
      const totals: JobAccountingTotals = {
        subtotals: {
          freightCharges: baseFreightCharge,
          freightGstCharges: baseFreightGstCharge,
          freightAdjustmentCharges: freightChargeSummedSubtotal,
          freightAdjustmentGstCharges: freightChargeSummedGstSubtotal,
          freightChargeTotals: freightCharge,
          freightChargeGstTotals: freightGstCharge,
          standbyChargeTotals: standbyCharge,
          standbyChargeGstTotals: standbyGstCharge,
          outsideMetroChargeTotals: outsideMetroCharge,
          outsideMetroChargeGstTotals: outsideMetroGstCharge,
          additionalCharges: additionalChargeTotal,
          additionalGstCharges: additionalChargeGstTotal,
          additionalChargeItems: allAdditionalChargeSubtotals,
          equipmentHireCharges,
          equipmentHireTotal,
          tollCharges: tollChargeSubtotals,
          gstCharges: gstTotal,
          fuelSurcharges: fuelSurcharge,
          fuelGstSurcharges: fuelGst,
          lessGst: finalTotalLessGst,
          plusGst: finalTotal,
          standbyChargeBreakdown: new StandbyCharge(
            clientStandbyChargeBreakdown,
            fleetAssetStandbyChargeBreakdown,
          ),
          demurrageChargeTotals,
          demurrageChargeGstTotals,
        },
        finalTotal,
        margin: overallMargin,
        hireMargin: hireOverallMargin,
      };

      const jobInformation = {
        totals,
        jobDurationData: jobData,
      };
      return jobInformation;
    } catch (error) {
      logConsoleError(
        'Error calculating totals in returnItemizedTotals',
        error,
      );
      return null;
    }
  }

  public generateFinishedJobDetails(
    jobDetails: JobDetails,
    driverGstRegistered: boolean,
  ): FinishedJobDetails {
    const information: FinishedJobDetails = new FinishedJobDetails();

    if (!jobDetails.pudItems.length) {
      throw new Error('Cannot price job with no stops');
    }
    if (!this.clientRates?.[0]) {
      throw new Error(
        'Client rates missing. Cannot compute FinishedJobDetails object.',
      );
    }
    if (!jobDetails.plannedRoute?.routes?.[0]) {
      throw new Error(
        'No planned route data. Cannot compute FinishedJobDetails object.',
      );
    }
    // =========================================================================
    // COMMON CALCULATIONS
    // =========================================================================
    const startedEpoch = jobDetails.pudItems[0]
      ? jobDetails.pudItems[0].epochTime
      : 0;
    const startedReadableTime = returnFormattedDate(startedEpoch, '', true);
    information.actualJobDate = moment(startedEpoch)
      .tz(useCompanyDetailsStore().userLocale)
      .startOf('day')
      .valueOf();
    information.readableJobDate = startedReadableTime;
    // =========================================================================
    // SET DISTANCE AND DURATIONS
    // =========================================================================
    information.additionalDurations = jobDetails.legDurations;

    // =========================================================================
    // SET VARIANCE PERCENTAGES
    // =========================================================================
    // Get the percentage discount/surcharge applied to this rate and service
    const clientRateVariationPct =
      this.clientServiceRateVariations?.clientAdjustmentPercentage;
    const fleetRateVariationPct =
      this.clientServiceRateVariations?.fleetAssetAdjustmentPercentage;

    // =========================================================================
    // CLIENT CALCULATIONS
    // =========================================================================
    const clientData: TimeTypeJobRateData[] = this.clientRates[0].rateData;

    const clientStartTime = clientData[0].mostRecentBegin;
    information.clientDurations.startTime = clientStartTime;

    information.clientDurations.readableStartTime =
      returnFormattedTime(clientStartTime);

    information.clientDurations.actualStandbyDuration =
      this.clientRates[0].standbyDuration.totalEditedDuration;

    information.clientDurations.readableStandbyDuration = returnCorrectDuration(
      information.clientDurations.actualStandbyDuration,
    );

    information.clientDurations.actualBreakDuration =
      this.clientRates[0].breakDuration.currentDuration;

    information.clientDurations.readableBreakDuration = returnCorrectDuration(
      information.clientDurations.actualBreakDuration,
    );

    const clientEndLoadTime =
      clientData[clientData.length - 1].mostRecentLoading;
    const clientEndTime =
      clientData[clientData.length - 1].mostRecentBegin + clientEndLoadTime;

    information.clientDurations.endTime = clientEndTime;
    information.clientDurations.readableEndTime =
      returnFormattedTime(clientEndTime);

    let durationInMilliseconds =
      clientEndTime -
      clientStartTime -
      information.clientDurations.actualBreakDuration -
      information.clientDurations.actualStandbyDuration;

    if (durationInMilliseconds < 0) {
      durationInMilliseconds = 0;
    }

    information.clientDurations.actualBilledDuration = durationInMilliseconds;

    information.clientDurations.readableBilledDuration = returnCorrectDuration(
      durationInMilliseconds,
    );

    ///////////////////////////////////
    // CLIENT RATE AMOUNT; PASSED INTO RETURN TOTALS

    let clientRateTypeId: number;
    if (
      jobDetails.accounting &&
      jobDetails.accounting.clientRates &&
      jobDetails.accounting.clientRates[0]
    ) {
      clientRateTypeId = jobDetails.accounting.clientRates[0].rate.rateTypeId;
    } else {
      clientRateTypeId = jobDetails.serviceTypeObject.rateTypeId;
    }

    let clientZoneToZoneRateData: ZoneToZoneRateData | null = null;

    switch (clientRateTypeId) {
      case JobRateType.TIME:
        const timeRate = this.clientRates[0].rate
          .rateTypeObject as TimeRateType;

        information.clientRateAmount = timeRateCalculation({
          rate: timeRate.rate,
          multiplier: timeRate.rateMultiplier,
          durationInMs: information.clientDurations.actualBilledDuration,
          variancePct: clientRateVariationPct,
        });
        information.clientStandbyRateAmount = timeRateCalculation({
          rate: timeRate.standbyRate,
          multiplier: timeRate.standbyMultiplier,
          durationInMs: information.clientDurations.actualStandbyDuration,
          variancePct: clientRateVariationPct,
        });
        break;
      case JobRateType.ZONE:
        const clientZoneRates = this.clientRates[0].rate
          .rateTypeObject as ZoneRateType[];
        information.clientRateAmount = generateZoneBreakdowns(
          jobDetails.pudItems,
          clientZoneRates,
          clientRateVariationPct,
        ).rate;
        break;
      case JobRateType.DISTANCE:
        if (
          isDistanceRateTypeObject(
            this.clientRates[0].rate.rateTypeId,
            this.clientRates[0].rate.rateTypeObject,
          )
        ) {
          // Get distance additional data. This should always be set for
          // DISTANCE type jobs, so throw an error if it's null
          const distanceAdditionalData = this.additionalData?.distanceRate;
          if (!distanceAdditionalData) {
            throw new TypeError(
              'Distance rate additional data is missing or invalid.',
            );
          }
          if (
            isDistanceRateData(
              this.clientRates[0].rate.rateTypeId,
              this.finishedJobData.clientRateData,
            )
          ) {
            information.clientRateAmount =
              this.finishedJobData.clientRateData.chargeExclGst;
          } else {
            // Check which additional travel to use based on the chargeBasis
            const additionalTravelDistances =
              this.clientRates[0].rate.rateTypeObject.chargeBasis ===
              ChargeBasis.SUBURB_CENTRES
                ? distanceAdditionalData.additionalTravelSuburbCentres.client
                : distanceAdditionalData.additionalTravelDistances.client;

            // Return the appropriate original distance
            const originalDistance = returnOriginalDistanceForChargeBasis(
              distanceAdditionalData.chargeableClientDistance,
              this.clientRates[0].rate.rateTypeObject.chargeBasis,
            );

            // Generate the client rate amount based on the distance rate data
            information.clientRateAmount =
              generateDistanceRateData({
                travelDistance: originalDistance,
                distanceRate: this.clientRates[0].rate.rateTypeObject,
                additionalDistances: additionalTravelDistances,
                additionalDurations: information.additionalDurations,
                editedTravelDistance:
                  distanceAdditionalData.chargeableClientDistance.edited,
                variancePct: clientRateVariationPct,
              })?.chargeExclGst ?? 0;
          }
        } else {
          throw new TypeError('DistanceRateType object invalid or missing.');
        }
        break;
      case JobRateType.POINT_TO_POINT:
        const rateTypeObject = this.clientRates[0].rate.rateTypeObject;
        if (Array.isArray(rateTypeObject)) {
          if (rateTypeObject.length > 0) {
            information.clientRateAmount = RoundCurrencyValue(
              (rateTypeObject as PointToPointRateType[])[0].rate,
            );
          }
        } else {
          information.clientRateAmount = RoundCurrencyValue(
            (rateTypeObject as PointToPointRateType).rate,
          );
        }
        break;

      case JobRateType.UNIT:
        const clientUnitRate = this.clientRates[0].rate
          .rateTypeObject as UnitRate[];
        const rateCharge = unitRateCalculation(
          jobDetails.pudItems,
          clientUnitRate,
          RateEntityType.CLIENT,
          null,
          null,
          clientRateVariationPct,
        );
        if (rateCharge) {
          information.clientRateAmount = rateCharge.chargesTotalExclGst;
        }
        break;
      case JobRateType.TRIP:
        const tripRateTypeObject: TripRate = this.clientRates[0].rate
          .rateTypeObject as TripRate;
        information.clientRateAmount = RoundCurrencyValue(
          tripRateTypeObject.rate,
        );
        break;
      case JobRateType.ZONE_TO_ZONE:
        if (
          isZoneToZoneRateTypeObject(
            this.clientRates[0].rate.rateTypeId,
            this.clientRates[0].rate.rateTypeObject,
          )
        ) {
          const zoneToZoneRateData = generateZoneToZoneRateData({
            type: RateEntityType.CLIENT,
            rateTypes: this.clientRates[0].rate.rateTypeObject,
            isGstRegistered: true,
            variancePct: clientRateVariationPct,
          });
          if (zoneToZoneRateData === null) {
            throw new Error(
              'generateZoneToZoneRateData failed for CLIENT in generateFinishedJobDetails. clientRateAmount could not be set.',
            );
          }
          clientZoneToZoneRateData = zoneToZoneRateData;
          // Generate the client rate amount based on the zone to zone rate data
          information.clientRateAmount = zoneToZoneRateData.chargeExclGst;
        } else {
          throw new TypeError(
            'ZoneToZoneRateType object invalid or missing in CLIENT calculations.',
          );
        }
        break;
    }

    // =========================================================================
    // FLEET ASSET CALCULATIONS
    // =========================================================================

    const fleetAssetData = this.fleetAssetRates[0]
      .rateData as TimeTypeJobRateData[];
    const fleetAssetStartTime = fleetAssetData[0].mostRecentBegin;

    information.fleetAssetDurations.startTime = fleetAssetStartTime;
    information.fleetAssetDurations.readableStartTime =
      returnFormattedTime(fleetAssetStartTime);

    const fleetAssetEndLoadTime =
      fleetAssetData[fleetAssetData.length - 1].mostRecentLoading;
    const fleetAssetEndTime =
      fleetAssetData[fleetAssetData.length - 1].mostRecentBegin +
      fleetAssetEndLoadTime;

    information.fleetAssetDurations.endTime = fleetAssetEndTime;
    information.fleetAssetDurations.readableEndTime =
      returnFormattedTime(fleetAssetEndTime);

    information.fleetAssetDurations.actualStandbyDuration =
      this.fleetAssetRates[0].standbyDuration.totalEditedDuration;
    information.fleetAssetDurations.readableStandbyDuration =
      returnCorrectDuration(
        information.fleetAssetDurations.actualStandbyDuration,
      );

    information.fleetAssetDurations.actualBreakDuration =
      this.fleetAssetRates[0].breakDuration.currentDuration;
    information.fleetAssetDurations.readableBreakDuration =
      returnCorrectDuration(
        information.fleetAssetDurations.actualBreakDuration,
      );

    let durationInMillisecondsFleet =
      fleetAssetEndTime -
      fleetAssetStartTime -
      information.fleetAssetDurations.actualBreakDuration -
      information.fleetAssetDurations.actualStandbyDuration;
    if (durationInMillisecondsFleet < 0) {
      durationInMillisecondsFleet = 0;
    }

    information.fleetAssetDurations.actualBilledDuration =
      durationInMillisecondsFleet;
    information.fleetAssetDurations.readableBilledDuration =
      returnCorrectDuration(durationInMillisecondsFleet);

    ////////////////////////////////////////

    let fleetAssetRateTypeId;
    if (
      jobDetails.accounting &&
      jobDetails.accounting.fleetAssetRates &&
      jobDetails.accounting.fleetAssetRates[0]
    ) {
      fleetAssetRateTypeId =
        jobDetails.accounting.fleetAssetRates[0].rate.rateTypeId;
    } else {
      fleetAssetRateTypeId = jobDetails.serviceTypeObject.rateTypeId;
    }

    switch (fleetAssetRateTypeId) {
      case JobRateType.TIME:
        const timeRate = this.fleetAssetRates[0].rate
          .rateTypeObject as TimeRateType;

        information.fleetAssetRateAmount = timeRateCalculation({
          rate: timeRate.rate,
          multiplier: timeRate.rateMultiplier,
          durationInMs: information.fleetAssetDurations.actualBilledDuration,
          variancePct: fleetRateVariationPct,
        });

        information.fleetAssetStandbyRateAmount = timeRateCalculation({
          rate: timeRate.standbyRate,
          multiplier: timeRate.standbyMultiplier,
          durationInMs: information.fleetAssetDurations.actualStandbyDuration,
          variancePct: fleetRateVariationPct,
        });
        break;
      case JobRateType.ZONE:
        const fleetAssetZoneRates = this.fleetAssetRates[0].rate
          .rateTypeObject as ZoneRateType[];
        const driverRate = fleetAssetZoneBreakDown(
          fleetAssetZoneRates,
          information.clientRateAmount ? information.clientRateAmount : 0,
          driverGstRegistered,
          fleetRateVariationPct,
        );
        information.fleetAssetRateAmount = driverRate.rate;
        break;
      case JobRateType.DISTANCE:
        if (
          isDistanceRateTypeObject(
            this.fleetAssetRates[0].rate.rateTypeId,
            this.fleetAssetRates[0].rate.rateTypeObject,
          )
        ) {
          // Get distance additional data. This should always be set for
          // DISTANCE type jobs, so throw an error if it's null
          const distanceAdditionalData = this.additionalData?.distanceRate;
          if (!distanceAdditionalData) {
            throw new TypeError(
              'Distance rate additional data is missing or invalid.',
            );
          }
          // Check which additional travel to use based on the chargeBasis
          const additionalTravelDistances =
            this.fleetAssetRates[0].rate.rateTypeObject.chargeBasis ===
            ChargeBasis.SUBURB_CENTRES
              ? distanceAdditionalData.additionalTravelSuburbCentres.fleetAsset
              : distanceAdditionalData.additionalTravelDistances.fleetAsset;

          // Return the appropriate original distance
          const originalDistance = returnOriginalDistanceForChargeBasis(
            distanceAdditionalData.chargeableFleetAssetDistance,
            this.fleetAssetRates[0].rate.rateTypeObject.chargeBasis,
          );

          // Generate the fleet asset rate amount based on the distance rate data
          information.fleetAssetRateAmount =
            generateDistanceRateData({
              travelDistance: originalDistance,
              distanceRate: this.fleetAssetRates[0].rate.rateTypeObject,
              additionalDistances: additionalTravelDistances,
              additionalDurations: information.additionalDurations,
              editedTravelDistance:
                distanceAdditionalData.chargeableFleetAssetDistance.edited,
              isGstRegistered: driverGstRegistered,
              variancePct: fleetRateVariationPct,
            })?.chargeExclGst ?? 0;
        } else {
          throw new TypeError('DistanceRateType object invalid or missing.');
        }
        break;
      case JobRateType.POINT_TO_POINT:
        const driverPercentage = (
          this.fleetAssetRates[0].rate.rateTypeObject as PointToPointRateType
        ).percentage;

        const percentToDecimal = driverPercentage / 100;
        information.fleetAssetRateAmount = RoundCurrencyValue(
          percentToDecimal * information.clientRateAmount,
        );
        break;

      case JobRateType.UNIT:
        const fleetAssetUnitRate = this.fleetAssetRates[0].rate
          .rateTypeObject as UnitRate[];
        const rateCharge = unitRateCalculation(
          jobDetails.pudItems,
          fleetAssetUnitRate,
          RateEntityType.FLEET_ASSET,
          information.clientRateAmount,
          driverGstRegistered,
          fleetRateVariationPct,
        );
        if (rateCharge) {
          information.fleetAssetRateAmount = rateCharge.chargesTotalExclGst;
        }
        break;

      case JobRateType.TRIP:
        const tripRateTypeObject: TripRate = this.fleetAssetRates[0].rate
          .rateTypeObject as TripRate;
        information.fleetAssetRateAmount = tripRateTypeObject.rate;
        break;
      case JobRateType.ZONE_TO_ZONE:
        if (
          isZoneToZoneRateTypeObject(
            this.fleetAssetRates[0].rate.rateTypeId,
            this.fleetAssetRates[0].rate.rateTypeObject,
          )
        ) {
          if (clientZoneToZoneRateData === null) {
            throw new TypeError(
              'Client rate is not a ZoneToZoneRateType object. Cannot calculate fleet asset rate.',
            );
          }
          const zoneToZoneRateData = generateZoneToZoneRateData({
            type: RateEntityType.FLEET_ASSET,
            rateTypes: this.fleetAssetRates[0].rate.rateTypeObject,
            isGstRegistered: true,
            clientSubtotals: clientZoneToZoneRateData.zoneRateSubtotals,
            variancePct: fleetRateVariationPct,
          });
          if (zoneToZoneRateData === null) {
            throw new Error(
              'generateZoneToZoneRateData failed in generateFinishedJobDetails. fleetAssetRateAmount could not be set.',
            );
          }
          // Generate the client rate amount based on the zone to zone rate data
          information.fleetAssetRateAmount = zoneToZoneRateData.chargeExclGst;
        } else {
          throw new TypeError(
            'ZoneToZoneRateType object invalid or missing in FLEET_ASSET calculations.',
          );
        }
        break;
    }
    /////////////////////////////////////
    // find first leg duration

    const fleetAssetRateData: TimeTypeJobRateData[] = this.fleetAssetRates[0]
      .rateData as TimeTypeJobRateData[];

    const clientRateData: TimeTypeJobRateData[] = this.clientRates[0]
      .rateData as TimeTypeJobRateData[];

    information.clientDurations.firstAndLastLegDuration =
      firstAndLastLegDuration(clientRateData);
    information.fleetAssetDurations.firstAndLastLegDuration =
      firstAndLastLegDuration(fleetAssetRateData);

    let loadTime = 0;

    const loadPuds: PUDItem[] = jobDetails.pudItems.filter(
      (pud: PUDItem) => pud.legTypeFlag === 'P' || pud.legTypeFlag === 'D',
    );
    // sum total load duration at all pickup and dropoffs. We save this information into our load durations. Utilised in reporting.
    for (const pud of loadPuds) {
      const arrivalEvent: JobStatusUpdate | undefined =
        jobDetails.returnSpecifiedEvent('ARRIVED', pud.pudId);
      const departureEvent: JobStatusUpdate | undefined =
        jobDetails.returnSpecifiedEvent('FINISHED', pud.pudId);

      if (arrivalEvent && departureEvent) {
        const arrivalTimeInEpoch = arrivalEvent.correctEventTime;
        const departureTimeInEpoch = departureEvent.correctEventTime;
        loadTime += departureTimeInEpoch - arrivalTimeInEpoch;
      }
    }

    information.loadDurations.actualLoadDuration = loadTime;

    information.loadDurations.readableLoadDuration = returnCorrectDuration(
      information.loadDurations.actualLoadDuration,
    );

    information.clientDurations.actualDriveDuration =
      information.clientDurations.actualBilledDuration -
      information.loadDurations.actualLoadDuration;

    information.clientDurations.readableDriveDuration = returnCorrectDuration(
      information.clientDurations.actualDriveDuration,
    );

    information.fleetAssetDurations.actualDriveDuration =
      information.fleetAssetDurations.actualBilledDuration -
      information.loadDurations.actualLoadDuration;

    information.fleetAssetDurations.readableDriveDuration =
      returnCorrectDuration(
        information.fleetAssetDurations.actualDriveDuration,
      );

    // ///////////////////////////////////

    // find expected load time
    let expectedLoadTime = 0;
    for (const pud of jobDetails.pudItems) {
      if (pud.legTypeFlag !== 'P' && pud.legTypeFlag !== 'D') {
        continue;
      }
      expectedLoadTime += pud.loadTime;
    }

    information.loadDurations.expectedLoadDuration = expectedLoadTime;

    information.clientDurations.expectedBilledDuration =
      jobDetails.plannedRoute.routes[0].summary.duration * 1000 +
      information.clientDurations.firstAndLastLegDuration +
      information.loadDurations.expectedLoadDuration;

    information.fleetAssetDurations.expectedBilledDuration =
      jobDetails.plannedRoute.routes[0].summary.duration * 1000 +
      information.fleetAssetDurations.firstAndLastLegDuration +
      information.loadDurations.expectedLoadDuration;

    information.clientDurations.expectedDriveDuration =
      information.clientDurations.expectedBilledDuration -
      information.loadDurations.expectedLoadDuration;

    information.fleetAssetDurations.expectedDriveDuration =
      information.fleetAssetDurations.expectedBilledDuration -
      information.loadDurations.expectedLoadDuration;

    information.loadDurations.loadVarianceDuration = Math.abs(
      information.loadDurations.actualLoadDuration -
        information.loadDurations.expectedLoadDuration,
    );

    information.loadDurations.readableLoadVariance = returnCorrectDuration(
      information.loadDurations.loadVarianceDuration,
    );

    information.loadDurations.loadVariancePercent =
      ((information.loadDurations.actualLoadDuration -
        information.loadDurations.expectedLoadDuration) /
        information.loadDurations.expectedLoadDuration) *
      100;

    information.clientDurations.driveVarianceDuration = Math.abs(
      information.clientDurations.actualDriveDuration -
        information.clientDurations.expectedDriveDuration,
    );

    information.clientDurations.readableDriveVariance = returnCorrectDuration(
      information.clientDurations.driveVarianceDuration,
    );

    information.clientDurations.driveVariancePercent =
      ((information.clientDurations.actualDriveDuration -
        information.clientDurations.expectedDriveDuration) /
        information.clientDurations.expectedDriveDuration) *
      100;

    information.fleetAssetDurations.driveVarianceDuration = Math.abs(
      information.fleetAssetDurations.actualDriveDuration -
        information.fleetAssetDurations.expectedDriveDuration,
    );

    information.fleetAssetDurations.readableDriveVariance =
      returnCorrectDuration(
        information.fleetAssetDurations.driveVarianceDuration,
      );

    information.fleetAssetDurations.driveVariancePercent =
      ((information.fleetAssetDurations.actualDriveDuration -
        information.fleetAssetDurations.expectedDriveDuration) /
        information.fleetAssetDurations.expectedDriveDuration) *
      100;

    information.clientDurations.billedVarianceDuration = Math.abs(
      information.clientDurations.actualBilledDuration -
        information.clientDurations.expectedBilledDuration,
    );

    information.clientDurations.readableBilledVariance = returnCorrectDuration(
      information.clientDurations.billedVarianceDuration,
    );

    information.clientDurations.billedVariancePercent =
      ((information.clientDurations.actualBilledDuration -
        information.clientDurations.expectedBilledDuration) /
        information.clientDurations.expectedBilledDuration) *
      100;

    information.fleetAssetDurations.billedVarianceDuration = Math.abs(
      information.clientDurations.actualBilledDuration -
        information.clientDurations.expectedBilledDuration,
    );

    information.fleetAssetDurations.readableBilledVariance =
      returnCorrectDuration(
        information.fleetAssetDurations.billedVarianceDuration,
      );

    information.fleetAssetDurations.billedVariancePercent =
      ((information.fleetAssetDurations.actualBilledDuration -
        information.fleetAssetDurations.expectedBilledDuration) /
        information.fleetAssetDurations.expectedBilledDuration) *
      100;

    return information;
  }

  // Calculate the required break duration in milliseconds based on the Fleet
  // Asset times
  get requiredBreakForJobLength() {
    if (!this.clientRates[0] || !this.fleetAssetRates[0]) {
      return 0;
    }
    const rateData: TimeTypeJobRateData[] | undefined =
      this.fleetAssetRates[0].rateData;

    if (rateData && rateData.length) {
      const epochStart = rateData[0].mostRecentBegin;
      const epochEnd = rateData[rateData.length - 1].mostRecentBegin;
      const duration = epochEnd - epochStart;

      return requiredBreakFromDuration(duration);
    } else {
      return 0;
    }
  }

  /**
   * For a distance rate job, returns the editedTravelDistance property from the
   * finished job data > clientRateData.
   */
  get clientEditedTravelDistance(): number | undefined {
    const clientRate = this.clientRates?.[0]?.rate;
    if (
      clientRate &&
      isDistanceRateData(
        clientRate.rateTypeId,
        this.finishedJobData.clientRateData,
      )
    ) {
      return this.finishedJobData.clientRateData.editedTravelDistance;
    }
  }

  /**
   * Returns whether there is an element in the fleetAssetRates array and
   * whether that element's rateTypeId is TRIP.
   */
  get isDriverTripRate(): boolean {
    return this.fleetAssetRates?.[0]
      ? this.fleetAssetRates[0].rate.rateTypeId === JobRateType.TRIP
      : false;
  }
}
