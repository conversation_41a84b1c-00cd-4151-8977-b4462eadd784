<!-- eslint-disable vue/no-deprecated-slot-scope-attribute -->
<template>
  <section class="invoice-adjustment-adjust-job-values">
    <div class="alert-container">
      <v-alert type="info" :value="alertWarningMessage.length > 0">
        <ul>
          <li v-for="(msg, index) in alertWarningMessage" :key="index">
            {{ msg }}
          </li>
        </ul>
      </v-alert>
    </div>

    <v-layout row wrap>
      <v-flex md12 px-2 pb-1 v-if="jobId" class="header-section"
        ><v-layout align-center>
          <v-flex>
            <v-layout align-center>
              <span
                class="accent-text--card outline-type mr-2"
                style="position: relative"
              >
                <span class="px-1" v-if="!isCustomAdjustment">
                  #{{
                    jobIdSummary && jobIdSummary.recurringJobId
                      ? jobIdSummary.recurringJobId
                      : jobId
                  }}
                </span>
                <span class="px-1" v-else> CUSTOM </span>
                <span
                  v-if="!readOnly && !isCustomAdjustment"
                  style="position: absolute; right: -36px; top: -12px"
                >
                  <v-tooltip right>
                    <template v-slot:activator="{ on }">
                      <v-btn
                        flat
                        v-on="on"
                        icon
                        @click="removeJobFromAdjustment"
                        class="mx-0"
                      >
                        <v-icon size="20" color="grey lighten-1"
                          >far fa-times</v-icon
                        >
                      </v-btn>
                    </template>
                    Remove Job
                  </v-tooltip>
                </span>
              </span>
              <v-flex :pl-4="!readOnly && !isCustomAdjustment">
                <v-divider></v-divider>
              </v-flex>
            </v-layout>
            <v-layout justify-space-between>
              <span class="caption-text px-1 pt-1">
                {{ jobDescription }}
              </span>
            </v-layout>
          </v-flex>
          <CurrencyDetailsDisplay
            v-if="
              adjustedValues.combinedTotalForJob &&
              hasAccountingInformationAvailable
            "
            :currencyDetails="adjustedValues.combinedTotalForJob"
            :prefixType="PrefixType.OPERATOR"
          ></CurrencyDetailsDisplay>
        </v-layout>
      </v-flex>
      <v-flex
        md12
        mb-3
        mt-1
        class="app-bgcolor--400 app-bordercolor--600 app-borderside--a"
        v-if="hasAccountingInformationAvailable"
      >
        <table class="job-adjustments-table">
          <thead>
            <tr>
              <th>Category</th>
              <th>Original</th>
              <th>Adjusted</th>
              <th>Ex GST. ($)</th>
            </tr>
          </thead>
          <tbody>
            <tr v-if="!isCustomAdjustment">
              <td>
                <h5 class="subheader--bold--14">Freight Adjustment:</h5>
              </td>

              <td class="pb-2 pr-2" colspan="2">
                <v-select
                  solo
                  flat
                  color="light-blue"
                  background-color="blue"
                  dense
                  :items="availableFreightAdjustmentTypeOptions"
                  v-model="selectedFreightAdjustmentController"
                  item-text="longName"
                  item-value="id"
                  label="Freight Adjustment Type"
                  :clearable="!readOnly"
                  hide-details
                  :disabled="readOnly"
                >
                  <template v-slot:item="{ item }">
                    <v-layout column>
                      <span>{{ item.longName }}</span>
                      <span style="font-style: italic" v-if="item.disabled">
                        Not available with either Fuel Levy (%) or GST
                        Adjustments selected.
                      </span>
                    </v-layout>
                  </template>
                </v-select>
              </td>
              <td>
                <v-divider class="subheader--divider"></v-divider>
              </td>
            </tr>
            <tr v-if="selectedFreightAdjustmentController === 'RATE'">
              <td class="line-item-header">
                <h6 class="subheader--faded">Hourly Rate:</h6>
              </td>
              <td>
                <v-text-field
                  class="v-solo-custom pr-1"
                  solo
                  flat
                  color="light-blue"
                  prefix="$"
                  label="Hourly Rate"
                  :value="
                    adjustedValues.freightAdjustment?.updatedRate?.original
                  "
                  :suffix="durationSuffix"
                  readonly
                  :disabled="readOnly"
                >
                </v-text-field>
              </td>
              <td v-if="adjustedValues.freightAdjustment?.updatedRate">
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  label="Hourly Rate"
                  prefix="$"
                  type="number"
                  :rules="[
                    validate.nonNegative,
                    validate.currency,
                    validate.required,
                  ]"
                  v-model.number="
                    adjustedValues.freightAdjustment.updatedRate.adjusted
                  "
                  :disabled="readOnly"
                  :suffix="durationSuffix"
                  @change="
                    computeFreightAdjustmentValues(
                      selectedFreightAdjustmentController,
                    )
                  "
                  @focus="$event.target.select()"
                >
                </v-text-field>
              </td>

              <td>
                {{
                  adjustedValues.freightTotal.exclGst
                    ? DisplayCurrencyValue(adjustedValues.freightTotal.exclGst)
                    : '0.00'
                }}
              </td>
            </tr>
            <tr
              v-if="
                selectedFreightAdjustmentController === 'BASE_FREIGHT_DOLLARS'
              "
            >
              <td>
                <h6 class="subheader--faded">Hourly Rate:</h6>
              </td>
              <td v-if="adjustedValues.freightAdjustment?.updatedBaseFreight">
                <v-text-field
                  class="v-solo-custom pr-1"
                  solo
                  flat
                  color="light-blue"
                  prefix="$"
                  label="Base Freight"
                  :value="
                    adjustedValues.freightAdjustment.updatedBaseFreight.original
                  "
                  readonly
                  :disabled="readOnly"
                >
                </v-text-field>
              </td>
              <td v-if="adjustedValues.freightAdjustment?.updatedBaseFreight">
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  label="Base Freight"
                  prefix="$"
                  type="number"
                  :rules="[
                    validate.nonNegative,
                    validate.currency,
                    validate.required,
                  ]"
                  v-model.number="
                    adjustedValues.freightAdjustment.updatedBaseFreight.adjusted
                  "
                  :disabled="readOnly"
                  @change="
                    computeFreightAdjustmentValues(
                      selectedFreightAdjustmentController,
                    )
                  "
                  @focus="$event.target.select()"
                >
                </v-text-field>
              </td>

              <td>
                {{
                  adjustedValues.freightTotal.exclGst
                    ? DisplayCurrencyValue(adjustedValues.freightTotal.exclGst)
                    : '0.00'
                }}
              </td>
            </tr>
            <tr v-if="selectedFreightAdjustmentController === 'DURATION'">
              <td>
                <h6 class="subheader--faded">Job Duration:</h6>
              </td>
              <td>
                <v-text-field
                  class="v-solo-custom pr-1"
                  solo
                  flat
                  color="light-blue"
                  label="Duration"
                  readonly
                  :disabled="readOnly"
                  :value="`${returnHoursFromMilliseconds(
                    adjustedValues.freightAdjustment?.updatedDuration?.original,
                  )}h ${returnMinutesFromMilliseconds(
                    adjustedValues.freightAdjustment?.updatedDuration?.original,
                  )}m`"
                >
                </v-text-field>
              </td>
              <td>
                <v-layout>
                  <v-text-field
                    class="v-solo-custom pr-1"
                    solo
                    flat
                    color="light-blue"
                    label="Duration"
                    v-model.number="jobDurationHoursController"
                    :rules="[
                      validate.nonNegative,
                      validate.numbers,
                      validate.required,
                    ]"
                    suffix="h"
                    type="number"
                    :disabled="readOnly"
                    @change="
                      computeFreightAdjustmentValues(
                        selectedFreightAdjustmentController,
                      )
                    "
                    @focus="$event.target.select()"
                  >
                  </v-text-field>
                  <v-text-field
                    class="v-solo-custom"
                    solo
                    flat
                    color="light-blue"
                    label="Duration"
                    suffix="m"
                    v-model.number="jobDurationMinutesController"
                    :rules="[
                      validate.nonNegative,
                      validate.numbers,
                      validate.required,
                    ]"
                    type="number"
                    :disabled="readOnly"
                    @change="
                      computeFreightAdjustmentValues(
                        selectedFreightAdjustmentController,
                      )
                    "
                    @focus="$event.target.select()"
                  >
                  </v-text-field>
                </v-layout>
              </td>

              <td>
                {{
                  adjustedValues.freightTotal.exclGst
                    ? DisplayCurrencyValue(adjustedValues.freightTotal.exclGst)
                    : '0.00'
                }}
              </td>
            </tr>
            <tr v-if="!isCustomAdjustment">
              <td>
                <h5 class="subheader--bold--14">Fuel Adjustment:</h5>
              </td>

              <td class="pb-2 pr-2" colspan="2">
                <v-select
                  solo
                  flat
                  background-color="blue"
                  color="light-blue"
                  dense
                  :items="availableFuelAdjustmentTypeOptions"
                  v-model="selectedFuelAdjustmentController"
                  item-text="longName"
                  item-value="id"
                  item-disabled="disabled"
                  label="Fuel Adjustment Type"
                  :clearable="!readOnly"
                  hide-details
                  :disabled="readOnly"
                >
                  <template v-slot:item="{ item }">
                    <v-layout column>
                      <span>{{ item.longName }}</span>
                      <span class="not-available-text" v-if="item.disabled">
                        Not available with Freight Adjustment selected.
                      </span>
                    </v-layout>
                  </template>
                </v-select>
              </td>
              <td>
                <v-divider class="subheader--divider"></v-divider>
              </td>
            </tr>
            <tr v-if="selectedFuelAdjustmentController === 'TOTAL_DOLLARS'">
              <td class="subheader--faded">Fuel Levy ($)</td>
              <td v-if="adjustedValues.fuelLevyAdjustment?.updatedValue">
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  prefix="$"
                  label="Fuel Levy"
                  :value="
                    adjustedValues.fuelLevyAdjustment.updatedValue.original
                  "
                  disabled
                >
                </v-text-field>
              </td>
              <td v-if="adjustedValues.fuelLevyAdjustment?.updatedValue">
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  prefix="$"
                  label="Fuel Levy"
                  v-model.number="
                    adjustedValues.fuelLevyAdjustment.updatedValue.adjusted
                  "
                  :rules="[validate.nonNegative, validate.required]"
                  type="number"
                  :disabled="readOnly"
                  :readonly="fuelLevyFreightLinkController"
                  :persistent-hint="fuelLevyFreightLinkController && !readOnly"
                  :hint="
                    fuelLevyFreightLinkController
                      ? 'Fuel Levy is being scaled to match the adjusted Freight Charges and Outside Metro charges. <br> Scaling may not apply correctly if Fuel Levy has been applied to Standby, Demurrage or Freight Adjustment charges.'
                      : ''
                  "
                  @change="
                    computeFuelAdjustmentValues(
                      selectedFuelAdjustmentController,
                    )
                  "
                  @focus="$event.target.select()"
                >
                  <template v-slot:message="{ message, key }">
                    <!-- eslint-disable-next-line vue/no-v-html -->
                    <div v-html="message" :key="key"></div>
                  </template>
                  <template v-slot:append>
                    <v-tooltip bottom v-if="!readOnly">
                      <template v-slot:activator="{ on }">
                        <v-btn
                          v-if="fuelLevyFreightLinkController !== null"
                          flat
                          v-on="on"
                          icon
                          @click="
                            fuelLevyFreightLinkController =
                              !fuelLevyFreightLinkController
                          "
                          class="mx-0"
                        >
                          <v-icon
                            size="17"
                            :color="
                              fuelLevyFreightLinkController
                                ? 'green accent-3'
                                : 'grey lighten-1'
                            "
                            :class="{
                              'fas fa-link': fuelLevyFreightLinkController,
                              'fas fa-unlink': !fuelLevyFreightLinkController,
                            }"
                          ></v-icon>
                        </v-btn>
                      </template>
                      Sync with Freight
                    </v-tooltip>
                  </template>
                </v-text-field>
              </td>

              <td>
                {{
                  adjustedValues.fuelTotal.exclGst
                    ? DisplayCurrencyValue(adjustedValues.fuelTotal.exclGst)
                    : '0.00'
                }}
              </td>
            </tr>
            <tr v-if="selectedFuelAdjustmentController === 'PERCENTAGE'">
              <td class="subheader--faded">Fuel Levy (%)</td>
              <td v-if="adjustedValues.fuelLevyAdjustment?.updatedValue">
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  suffix="%"
                  label="Fuel Levy"
                  :value="
                    adjustedValues.fuelLevyAdjustment.updatedValue.original
                  "
                  disabled
                >
                </v-text-field>
              </td>
              <td v-if="adjustedValues.fuelLevyAdjustment?.updatedValue">
                <v-text-field
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  suffix="%"
                  label="Fuel Levy"
                  v-model.number="
                    adjustedValues.fuelLevyAdjustment.updatedValue.adjusted
                  "
                  :rules="[
                    validate.percentage,
                    validate.nonNegative,
                    validate.required,
                  ]"
                  type="number"
                  :disabled="readOnly"
                  @change="
                    computeFuelAdjustmentValues(
                      selectedFuelAdjustmentController,
                    )
                  "
                  @focus="$event.target.select()"
                >
                </v-text-field>
              </td>

              <td>
                {{
                  adjustedValues.fuelTotal.exclGst
                    ? DisplayCurrencyValue(adjustedValues.fuelTotal.exclGst)
                    : '0.00'
                }}
              </td>
            </tr>
            <tr v-if="!isCustomAdjustment">
              <td>
                <h5 class="subheader--bold--14">Additional Adjustments:</h5>
              </td>

              <td class="pb-2 pr-2" colspan="2">
                <v-select
                  solo
                  flat
                  multiple
                  v-model="selectedAdditionalAdjustmentsController"
                  :items="availableAdditionalAdjustmentTypeOptions"
                  item-value="id"
                  item-text="longName"
                  background-color="blue"
                  color="light-blue"
                  label="Additional Adjustments"
                  :disabled="readOnly"
                  item-disabled="disabled"
                  hide-details
                  :clearable="!readOnly"
                >
                  <template v-slot:item="{ item }">
                    <v-list-tile
                      dense
                      :disabled="item.disabled"
                      @click="selectOrDeselectAdditionalAdjustmentType(item)"
                    >
                      <v-list-tile-action>
                        <v-checkbox
                          color="light-blue"
                          :value="
                            !!selectedAdditionalAdjustmentsController &&
                            selectedAdditionalAdjustmentsController.includes(
                              item.id,
                            )
                          "
                          :disabled="item.disabled"
                        ></v-checkbox>
                      </v-list-tile-action>
                      <v-list-tile-content>
                        <v-list-tile-title class="pr-2 ma-0">
                          <span>{{ item.longName }}</span>
                        </v-list-tile-title>
                        <v-list-tile-sub-title
                          class="pa-0"
                          v-if="item.disabled"
                        >
                          <span style="font-size: 10px; font-style: italic">
                            Not available with Freight Adjustment selected.
                          </span>
                        </v-list-tile-sub-title>
                      </v-list-tile-content>
                    </v-list-tile>
                  </template>
                  <template v-slot:selection="{ item, index }">
                    <v-chip
                      v-if="index === 0"
                      :color="!readOnly ? 'white' : 'grey darken-3'"
                      light
                    >
                      <span class="blue--text">{{ item.longName }}</span>
                    </v-chip>
                    <span v-if="index === 1" class="white--text caption"
                      >(+{{
                        (selectedAdditionalAdjustmentsController?.length ?? 0) -
                        1
                      }}
                      {{
                        (selectedAdditionalAdjustmentsController?.length ??
                          0) === 2
                          ? 'other'
                          : 'others'
                      }})</span
                    >
                  </template>
                </v-select>
              </td>
              <td>
                <v-divider class="subheader--divider"></v-divider>
              </td>
            </tr>
            <tr
              v-for="adjustment in adjustedValues.additionalAdjustments"
              :key="adjustment.type"
            >
              <td>
                <h6 class="subheader--faded">
                  {{ returnReadableAdditionalAdjustmentType(adjustment.type) }}:
                </h6>
              </td>
              <td v-if="adjustment.type !== 'CUSTOM'">
                <v-text-field
                  v-if="adjustment.updatedValue"
                  class="v-solo-custom pr-1"
                  solo
                  flat
                  color="light-blue"
                  prefix="$"
                  :label="`Original ${returnReadableAdditionalAdjustmentType(
                    adjustment.type,
                  )}`"
                  :value="adjustment.updatedValue.original"
                  readonly
                  :disabled="readOnly"
                >
                </v-text-field>
              </td>
              <td v-else>
                <v-select
                  class="v-solo-custom pr-1"
                  solo
                  flat
                  color="light-blue"
                  :items="increaseDecreaseOptions"
                  item-value="id"
                  item-text="longName"
                  label="Type"
                  v-model="adjustment.sumType"
                  :disabled="readOnly"
                >
                </v-select>
              </td>

              <td v-if="adjustment.type !== 'OUTSIDE_METRO'">
                <v-text-field
                  v-if="adjustment.updatedValue"
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  :label="
                    returnReadableAdditionalAdjustmentType(adjustment.type)
                  "
                  prefix="$"
                  type="number"
                  :rules="[
                    validate.nonNegative,
                    validate.currency,
                    validate.required,
                  ]"
                  v-model.number="adjustment.updatedValue.adjusted"
                  :disabled="readOnly"
                  @change="computeAdditionalAdjustmentValues"
                  @focus="$event.target.select()"
                >
                </v-text-field>
              </td>
              <td v-else>
                <v-text-field
                  v-if="adjustment.updatedValue"
                  class="v-solo-custom"
                  solo
                  flat
                  color="light-blue"
                  :label="
                    returnReadableAdditionalAdjustmentType(adjustment.type)
                  "
                  prefix="$"
                  type="number"
                  :rules="[
                    validate.nonNegative,
                    validate.currency,
                    validate.required,
                  ]"
                  v-model.number="adjustment.updatedValue.adjusted"
                  :disabled="readOnly"
                  :readonly="outsideMetroFreightLinkController"
                  :persistent-hint="
                    outsideMetroFreightLinkController && !readOnly
                  "
                  :hint="
                    outsideMetroFreightLinkController
                      ? 'Outside Metro charge is being scaled to match the adjusted Freight Charge.'
                      : ''
                  "
                  @change="computeAdditionalAdjustmentValues"
                  @focus="$event.target.select()"
                >
                  <template v-slot:append>
                    <v-tooltip bottom v-if="!readOnly">
                      <template v-slot:activator="{ on }">
                        <v-btn
                          v-if="outsideMetroFreightLinkController !== null"
                          flat
                          v-on="on"
                          icon
                          @click="
                            outsideMetroFreightLinkController =
                              !outsideMetroFreightLinkController
                          "
                          class="mx-0"
                        >
                          <v-icon
                            size="17"
                            :color="
                              outsideMetroFreightLinkController
                                ? 'green accent-3'
                                : 'grey lighten-1'
                            "
                            :class="{
                              'fas fa-link': outsideMetroFreightLinkController,
                              'fas fa-unlink':
                                !outsideMetroFreightLinkController,
                            }"
                          ></v-icon>
                        </v-btn>
                      </template>
                      Sync with Freight
                    </v-tooltip>
                  </template>
                </v-text-field>
              </td>

              <td v-if="adjustment.type !== 'GST'">
                {{
                  !!adjustment.adjustmentSubtotal
                    ? DisplayCurrencyValue(
                        returnExclGstTotalForAdditionalAdjustment(adjustment),
                      )
                    : '0.00'
                }}
              </td>
              <td v-else>
                {{
                  !!adjustment.adjustmentSubtotal
                    ? DisplayCurrencyValue(adjustment.adjustmentSubtotal.gst)
                    : '0.00'
                }}
                <span class="pl-2" style="position: relative; top: -2px">
                  <InformationTooltip
                    :right="true"
                    :tooltipType="HealthLevel.INFO"
                  >
                    <v-layout slot="content" row wrap>
                      <v-flex md12>
                        <p class="mb-1">
                          The displayed value indicates an increase or reduction
                          in
                          <strong>GST only</strong>.
                        </p>
                      </v-flex>
                    </v-layout>
                  </InformationTooltip>
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </v-flex>
      <v-flex v-else md12>
        <v-layout justify-center align-center>
          <p class="ma-0 pb-0 subheader--bold" style="text-transform: none">
            Pricing details could not be found for job #{{
              jobIdSummary && jobIdSummary.recurringJobId
                ? jobIdSummary.recurringJobId
                : jobId
            }}.
          </p>
        </v-layout>
        <v-layout justify-center align-center>
          <p
            class="ma-0 pb-3 subheader--faded--13 font-italic"
            style="text-transform: none"
          >
            This may indicate this job is incomplete or has been cancelled.
            Please select a different job, or create a Custom Adjustment.
          </p>
        </v-layout>
      </v-flex>
    </v-layout>
  </section>
</template>

<script setup lang="ts">
import InformationTooltip from '@/components/common/ui-elements/information_tooltip.vue';
import CurrencyDetailsDisplay from '@/components/support/InvoiceAdjustment/currency_details_display.vue';
import {
  DisplayCurrencyValue,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  returnFormattedDate,
  returnMillisecondsFromDuration,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import {
  returnRateTypeLongNameFromId,
  returnServiceTypeLongNameFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import {
  CurrencyDetails,
  PrefixType,
  returnCurrencyDetailsForSumType,
} from '@/interface-models/Accounting/CurrencyDetails';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { SumType } from '@/interface-models/Generic/Accounting/SumType';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { SearchByOption } from '@/interface-models/Generic/SearchByOption';
import rateMultipliers from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import { Validation } from '@/interface-models/Generic/Validation';
import { InvoiceEntityCategory } from '@/interface-models/InvoiceAdjustment/EntityTypes/InvoiceEntityCategory';
import AdjustedValueDouble from '@/interface-models/InvoiceAdjustment/Generic/AdjustedValueDouble';
import AdjustedValueLong from '@/interface-models/InvoiceAdjustment/Generic/AdjustedValueLong';
import AdditionalAdjustment from '@/interface-models/InvoiceAdjustment/PerJobValues/AdditionalAdjustment';
import {
  AdditionalAdjustmentType,
  returnReadableAdditionalAdjustmentType,
} from '@/interface-models/InvoiceAdjustment/PerJobValues/AdditionalAdjustmentType';
import {
  FreightAdjustmentType,
  returnReadableFreightAdjustmentType,
} from '@/interface-models/InvoiceAdjustment/PerJobValues/FreightAdjustmentType';
import FuelAdjustment from '@/interface-models/InvoiceAdjustment/PerJobValues/FuelAdjustment';
import {
  FuelAdjustmentType,
  returnReadableFuelAdjustmentType,
} from '@/interface-models/InvoiceAdjustment/PerJobValues/FuelAdjustmentType';
import InvoiceAdjustmentPerJobValues from '@/interface-models/InvoiceAdjustment/PerJobValues/InvoiceAdjustmentPerJobValues';
import { InvoiceAdjustmentReportType } from '@/interface-models/InvoiceAdjustment/ReportType/InvoiceAdjustmentReportType';
import { JobIdLookupSummary } from '@/interface-models/Jobs/JobIdLookup/JobIdLookupSummary';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import TripRate from '@/interface-models/ServiceRates/ServiceTypes/TripRate/TripRate';
import moment from 'moment-timezone';
import { ComputedRef, Ref, WritableComputedRef, computed, ref } from 'vue';

import { validationRules } from '@/helpers/ValidationHelpers/ValidationHelpers';
import { timeRateCalculation } from '@/helpers/RateHelpers/TimeRateHelpers';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';

interface AdjustmentTypeOriginal {
  freightType?: FreightAdjustmentType;
  fuelType?: FuelAdjustmentType;
  additionalType?: AdditionalAdjustmentType;
}

interface SelectOption {
  id: number | string;
  longName: string;
  disabled: boolean;
}

const emit = defineEmits(['removeJobFromAdjustment']);

const props = withDefaults(
  defineProps<{
    jobId?: number;
    entityCategory: InvoiceEntityCategory;
    reportType?: InvoiceAdjustmentReportType;
    jobAccountingDetails?: JobAccountingDetails;
    jobIdSummary?: JobIdLookupSummary | null;
    adjustedValues: InvoiceAdjustmentPerJobValues;
    readOnly: boolean;
  }>(),
  {
    entityCategory: InvoiceEntityCategory.SUBCONTRACTOR,
    reportType: InvoiceAdjustmentReportType.INVOICE,
    readOnly: false,
    jobIdSummary: null,
    jobAccountingDetails: undefined,
    jobId: 0,
  },
);
const linkFuelLevyDollarsWithFreight: Ref<boolean> = ref(true);
const linkOutsideMetroDollarsWithFreight: Ref<boolean> = ref(true);

// Trigger app notification. Defaults to ERROR type message, but type can be
// provided to produce other types. Includes componentTitle as a title for the
// notification.
function showAppNotification(text: string, type?: HealthLevel): void {
  showNotification(text, {
    type,
    title: 'Invoice Adjustment - Adjust Job Values',
  });
}

// // Return a string containing job related information to be used as a
// // subheader
const jobDescription: ComputedRef<string> = computed(() => {
  const descriptionValues: string[] = [];
  if (props.jobIdSummary) {
    descriptionValues.push(
      props.jobIdSummary.jobDate
        ? returnFormattedDate(props.jobIdSummary.jobDate)
        : 'Date Unknown',
    );
  }
  if (rateTableItem.value) {
    let rateTypeName = returnRateTypeLongNameFromId(
      rateTableItem.value.rateTypeId,
    );
    if (isTimeRate.value) {
      const rate = returnOriginalValueForType({
        freightType: FreightAdjustmentType.RATE,
      });
      rateTypeName += ` ($${rate ? rate : 0} ${durationSuffix.value})`;
    } else if (rateTableItem.value && rateTableItem.value.rateTypeId === 6) {
      // If it is trip/Quoted rate display the original
      const rate: number = rateTableItem.value.rateTypeObject
        ? (rateTableItem.value.rateTypeObject as TripRate).rate
        : 0;
      rateTypeName += ` ($${rate ? rate : 0})`;
    }
    descriptionValues.push(rateTypeName);
    descriptionValues.push(
      returnServiceTypeLongNameFromId(
        rateTableItem.value.serviceTypeId
          ? rateTableItem.value.serviceTypeId
          : 0,
        '',
      ),
    );
  }
  // Display what Outside Metro rate was applied (if one was applied) and N/A
  // if none was applied
  if (props.jobAccountingDetails) {
    // TODO: Add the trip/Quoted rate value
    const fuelPercent = returnOriginalValueForType({
      fuelType: FuelAdjustmentType.PERCENTAGE,
    });
    descriptionValues.push(
      'Fuel Levy: ' + (fuelPercent ? `${fuelPercent}%` : 'N/A'),
    );
    const outsideMetroRate = returnOriginalValueForType({
      additionalType: AdditionalAdjustmentType.OUTSIDE_METRO_PERCENT,
    });
    descriptionValues.push(
      'Outside Metro: ' + (outsideMetroRate ? `${outsideMetroRate}%` : 'N/A'),
    );
    if (props.jobAccountingDetails.invoiceId) {
      descriptionValues.push('Inv. #' + props.jobAccountingDetails.invoiceId);
    }
    if (props.jobAccountingDetails.rctiId) {
      descriptionValues.push('RCTI #' + props.jobAccountingDetails.rctiId);
    }
  }
  return descriptionValues.filter((d) => !!d).join(' - ');
});

// Displays an error message instead of selects in the case that we don't have
// full accounting information for this job
const hasAccountingInformationAvailable: ComputedRef<boolean> = computed(() => {
  if (isCustomAdjustment.value) {
    return true;
  }
  return (
    (props.entityCategory === InvoiceEntityCategory.CLIENT
      ? props.jobAccountingDetails?.clientRates &&
        !!props.jobAccountingDetails.clientRates[0]
      : props.jobAccountingDetails?.fleetAssetRates &&
        !!props.jobAccountingDetails.fleetAssetRates[0]) || false
  );
});

const increaseDecreaseOptions: ComputedRef<SearchByOption[]> = computed(() => {
  if (props.entityCategory === InvoiceEntityCategory.CLIENT) {
    return [
      {
        id: SumType.ADDITION,
        longName: 'Increase Charge',
      },
      {
        id: SumType.DEDUCTION,
        longName: 'Decrease Charge',
      },
    ];
  } else if (props.entityCategory === InvoiceEntityCategory.SUBCONTRACTOR) {
    return [
      {
        id: SumType.ADDITION,
        longName:
          props.reportType === InvoiceAdjustmentReportType.INVOICE
            ? 'Invoice'
            : 'Increase Charge',
      },
      {
        id: SumType.DEDUCTION,
        longName:
          props.reportType === InvoiceAdjustmentReportType.INVOICE
            ? 'Credit Note'
            : 'Decrease Charge',
      },
    ];
  } else {
    return [];
  }
});

const fuelLevyFreightLinkController: WritableComputedRef<boolean | null> =
  computed({
    get(): boolean | null {
      // Return null if there is no value to the freight adjustment
      if (
        !props.adjustedValues.freightAdjustment ||
        !props.adjustedValues.freightAdjustment.adjustmentSubtotal
      ) {
        return null;
      }
      return linkFuelLevyDollarsWithFreight.value;
    },
    set(value: boolean | null): void {
      if (value === null || value === undefined) {
        linkFuelLevyDollarsWithFreight.value = false;
        return;
      }
      linkFuelLevyDollarsWithFreight.value = value;
      if (value) {
        // If fuelLevyFreightLinkController is true and we have a current freight
        // adjustment, then try and sync this to the fuel dollars
        if (props.adjustedValues.freightTotal) {
          syncFuelLevyDollarsValueWithFreight(
            props.adjustedValues.freightTotal.exclGst,
            props.adjustedValues.fuelLevyAdjustment,
          );
        }
      }
    },
  });

const outsideMetroFreightLinkController: WritableComputedRef<boolean | null> =
  computed({
    get(): boolean | null {
      // Return null if there is no value to the freight adjustment
      if (
        !props.adjustedValues.freightAdjustment ||
        !props.adjustedValues.freightAdjustment.adjustmentSubtotal
      ) {
        return null;
      }
      // Return null if there is no OUTSIDE METRO additional adjustment being
      // applied
      if (
        !props.adjustedValues.additionalAdjustments ||
        !props.adjustedValues.additionalAdjustments.length ||
        !props.adjustedValues.additionalAdjustments.some(
          (aa) => aa.type === AdditionalAdjustmentType.OUTSIDE_METRO,
        )
      ) {
        return null;
      }
      return linkOutsideMetroDollarsWithFreight.value;
    },
    set(value: boolean | null): void {
      if (value === null || value === undefined) {
        linkOutsideMetroDollarsWithFreight.value = false;
        return;
      }
      linkOutsideMetroDollarsWithFreight.value = value;
      if (value === true) {
        // If outsideMetroFreightLinkController is true and we have a current
        // freight adjustment, then try and sync this to the outside metro
        // additional adjustment
        if (props.adjustedValues.freightTotal) {
          syncOutsideMetroValueWithFreight(
            props.adjustedValues.freightTotal.exclGst,
            props.adjustedValues.additionalAdjustments,
          );
          // If we have fuel selected as DOLLARS and have the boolean
          // linkFuelLevyDollarsWithFreight
          syncFuelLevyDollarsValueWithFreight(
            props.adjustedValues.freightTotal.exclGst,
            props.adjustedValues.fuelLevyAdjustment,
          );
        }
      }
    },
  });

const rateTableItem: ComputedRef<RateTableItems | null> = computed(() => {
  const acc = props.jobAccountingDetails;
  if (!acc) {
    return null;
  }
  return props.entityCategory === InvoiceEntityCategory.CLIENT
    ? acc.clientRates && acc.clientRates[0]
      ? acc.clientRates[0].rate
      : null
    : acc.fleetAssetRates && acc.fleetAssetRates[0]
      ? acc.fleetAssetRates[0].rate
      : null;
});

const freightAdjustmentTypeOptions: Ref<SearchByOption[]> = ref(
  Object.values(FreightAdjustmentType).map((c) => {
    return { id: c, longName: returnReadableFreightAdjustmentType(c) };
  }),
);

// Return list of available fuel adjustment types to be used in v-select
// component. Disables selection of options if there are any incompatibilities
// with other selected adjustments.
const availableFreightAdjustmentTypeOptions: ComputedRef<SearchByOption[]> =
  computed(() => {
    return freightAdjustmentTypeOptions.value.map((c) => {
      return {
        ...c,
        disabled:
          selectedFuelAdjustmentController.value ===
            FuelAdjustmentType.PERCENTAGE ||
          (!!selectedAdditionalAdjustmentsController.value &&
            selectedAdditionalAdjustmentsController.value.includes(
              AdditionalAdjustmentType.GST,
            )),
      };
    });
  });

// Return list of available fuel adjustment types to be used in v-select
// component. Disables selection of options if there are any incompatibilities
// with other selected adjustments.
const availableFuelAdjustmentTypeOptions: ComputedRef<SearchByOption[]> =
  computed(() => {
    return Object.values(FuelAdjustmentType).map((c) => {
      return {
        id: c,
        longName: returnReadableFuelAdjustmentType(c),
        disabled:
          c === FuelAdjustmentType.PERCENTAGE &&
          !!selectedFreightAdjustmentController.value,
      };
    });
  });

// Return list of available additional adjustment types to be used in v-select
// component. Disables selection of options if there are any incompatibilities
// with other selected adjustments.
const availableAdditionalAdjustmentTypeOptions: ComputedRef<SearchByOption[]> =
  computed(() => {
    return Object.values(AdditionalAdjustmentType)
      .filter((c) => c !== AdditionalAdjustmentType.OUTSIDE_METRO_PERCENT)
      .map((c) => {
        return {
          id: c,
          longName: returnReadableAdditionalAdjustmentType(c),
          disabled:
            c === AdditionalAdjustmentType.GST &&
            (!!selectedFreightAdjustmentController.value ||
              !!selectedFuelAdjustmentController.value),
        };
      });
  });

// Modelled to v-select for Freight Adjustment. Returns value from
// adjustedValues.freightAdjustment. When setting, if the incoming value is
// undefined then clear the freightAdjustment property, and add it if the
// incoming value is not undefined
const selectedFreightAdjustmentController: WritableComputedRef<
  FreightAdjustmentType | undefined
> = computed({
  get(): FreightAdjustmentType | undefined {
    if (!props.adjustedValues.freightAdjustment) {
      return;
    }
    return props.adjustedValues.freightAdjustment.type;
  },
  set(type: FreightAdjustmentType | undefined): void {
    // Clear whole property if incoming value is undefined
    if (!type) {
      props.adjustedValues.freightAdjustment = undefined;
      return;
    }
    // Helper function to return the original value for a given adjustment type
    const returnOriginalForCase = (adjType: FreightAdjustmentType) =>
      type === adjType ? returnOriginalValueForType({ freightType: type }) : 0;
    // Return AdjustedValueDouble if a condition is met, otherwise return
    // undefined
    const returnAdjustedDoubleValue = (
      cond: boolean,
      value: number,
    ): AdjustedValueDouble | undefined =>
      cond
        ? {
            original: value,
            adjusted: value,
          }
        : undefined;

    // For all cases of FreightAdjustmentType, return either AdjustedValueDouble
    // (or AdjustedValueLong) object. If the selected adjustment type does not
    // require the property then return undefined instead.
    const updatedBaseFreight: AdjustedValueDouble | undefined =
      returnAdjustedDoubleValue(
        type === FreightAdjustmentType.BASE_FREIGHT_DOLLARS,
        returnOriginalForCase(FreightAdjustmentType.BASE_FREIGHT_DOLLARS),
      );
    const updatedDuration: AdjustedValueLong | undefined =
      returnAdjustedDoubleValue(
        type === FreightAdjustmentType.DURATION,
        returnOriginalForCase(FreightAdjustmentType.DURATION),
      );
    const updatedRate: AdjustedValueDouble | undefined =
      returnAdjustedDoubleValue(
        type === FreightAdjustmentType.RATE,
        returnOriginalForCase(FreightAdjustmentType.RATE),
      );

    // If not undefined and freightAdjustment is defined, then set value to type
    // property
    if (props.adjustedValues.freightAdjustment) {
      // Set updated values to what we calculated above
      props.adjustedValues.freightAdjustment.updatedBaseFreight =
        updatedBaseFreight;
      props.adjustedValues.freightAdjustment.updatedDuration = updatedDuration;
      props.adjustedValues.freightAdjustment.updatedRate = updatedRate;
      props.adjustedValues.freightAdjustment.adjustmentSubtotal =
        returnEmptyCurrencyDetails();
      props.adjustedValues.freightAdjustment.type = type;
    } else {
      // If freightAdjustment is not defined then create new empty object and
      // with incoming type, including values calculated above. NOTE: Must use
      // $set to add reactivity since freightAdjustment was previous undefined
      props.adjustedValues.freightAdjustment = {
        type,
        adjustmentSubtotal: returnEmptyCurrencyDetails(),
        updatedBaseFreight,
        updatedDuration,
        updatedRate,
        sumType: SumType.ADDITION,
      };
    }
    computeFreightAdjustmentValues(type);
  },
});

// Modelled to v-select for Fuel Adjustment. Returns value from
// adjustedValues.fuelLevyAdjustment. When setting, if the incoming value is
// undefined then clear the fuelLevyAdjustment property, and add it if the
// incoming value is not undefined
const selectedFuelAdjustmentController: WritableComputedRef<
  FuelAdjustmentType | undefined
> = computed({
  get(): FuelAdjustmentType | undefined {
    if (!props.adjustedValues.fuelLevyAdjustment) {
      return;
    }
    return props.adjustedValues.fuelLevyAdjustment.type;
  },
  set(type: FuelAdjustmentType | undefined): void {
    // Clear whole property if incoming value is undefined
    if (!type) {
      props.adjustedValues.fuelLevyAdjustment = undefined;
      return;
    }

    // Helper function to return the original value for a given adjustment type
    const returnOriginalForCase = (adjType: FuelAdjustmentType) =>
      type === adjType
        ? returnOriginalValueForType({
            fuelType: type,
          })
        : 0;
    // Return AdjustedValueDouble for provided value
    const returnAdjustedDoubleValue = (
      value: number,
    ): AdjustedValueDouble | undefined => {
      return {
        original: value,
        adjusted: value,
      };
    };

    // Return updated AdjustedValueDouble property for fuel based on type
    const updatedValue: AdjustedValueDouble | undefined =
      returnAdjustedDoubleValue(returnOriginalForCase(type));

    if (props.adjustedValues.fuelLevyAdjustment) {
      // If not undefined and fuelLevyAdjustment is defined, then set value to
      // type property. Set updated values to what we calculated above
      props.adjustedValues.fuelLevyAdjustment.updatedValue = updatedValue;
      props.adjustedValues.fuelLevyAdjustment.adjustmentSubtotal =
        returnEmptyCurrencyDetails();
      props.adjustedValues.fuelLevyAdjustment.type = type;
    } else {
      // If fuelLevyAdjustment is not defined then create new empty object and
      // with incoming type, including values calculated above. NOTE: Must use
      // $set to add reactivity since fuelLevyAdjustment was previous undefined
      props.adjustedValues.fuelLevyAdjustment = {
        type,
        adjustmentSubtotal: returnEmptyCurrencyDetails(),
        updatedValue,
        sumType: SumType.ADDITION,
      };
    }
    // If outsideMetroFreightLinkController is true and we have a current
    // freight adjustment, then try and sync this to the outside metro
    // additional adjustment
    if (
      props.adjustedValues.freightTotal &&
      outsideMetroFreightLinkController.value
    ) {
      syncOutsideMetroValueWithFreight(
        props.adjustedValues.freightTotal.exclGst,
        props.adjustedValues.additionalAdjustments,
      );
    }
    // If fuelLevyFreightLinkController is true and we have a current freight
    // adjustment, then try and sync this to the fuel dollars
    if (
      props.adjustedValues.freightTotal &&
      fuelLevyFreightLinkController.value
    ) {
      syncFuelLevyDollarsValueWithFreight(
        props.adjustedValues.freightTotal.exclGst,
        props.adjustedValues.fuelLevyAdjustment,
      );
    }
    computeFuelAdjustmentValues(type);
  },
});

// Modelled to v-select for Fuel Adjustment. Returns value from
// adjustedValues.fuelLevyAdjustment. When setting, if the incoming value is
// undefined then clear the fuelLevyAdjustment property, and add it if the
// incoming value is not undefined
const selectedAdditionalAdjustmentsController: WritableComputedRef<
  AdditionalAdjustmentType[] | undefined
> = computed({
  get(): AdditionalAdjustmentType[] | undefined {
    if (!props.adjustedValues.additionalAdjustments) {
      return [];
    }
    return props.adjustedValues.additionalAdjustments.map((a) => a.type);
  },
  set(updatedTypes: AdditionalAdjustmentType[] | undefined): void {
    // Clear whole property if incoming value is undefined
    if (!updatedTypes || !updatedTypes.length) {
      props.adjustedValues.additionalAdjustments = undefined;

      syncFuelLevyDollarsValueWithFreight(
        props.adjustedValues.freightTotal.exclGst,
        props.adjustedValues.fuelLevyAdjustment,
      );
      return;
    }
    if (!props.adjustedValues.additionalAdjustments) {
      props.adjustedValues.additionalAdjustments = [];
    }
    // Return AdjustedValueDouble if a condition is met, otherwise return
    // undefined
    const returnAdjustedDoubleValue = (
      type: AdditionalAdjustmentType,
    ): AdjustedValueDouble => {
      const originalVal = returnOriginalValueForType({
        additionalType: type,
      });
      return { original: originalVal, adjusted: originalVal };
    };

    const current = props.adjustedValues.additionalAdjustments
      ? [
          ...new Set(
            props.adjustedValues.additionalAdjustments.map((a) => a.type),
          ),
        ]
      : [];
    const toRemove = current.filter((curr) => !updatedTypes.includes(curr));
    const toAdd = updatedTypes.filter((u) => !current.includes(u));

    // If toRemove has any items, then we should remove elements of those types
    // from additionalAdjustments
    if (toRemove.length) {
      props.adjustedValues.additionalAdjustments =
        props.adjustedValues.additionalAdjustments!.filter(
          (a) => !toRemove.includes(a.type),
        );
    }
    // If toAdd has any items, then we should add new empty objects to
    // additionalAdjustments list for those types
    if (toAdd.length) {
      toAdd.forEach((a) => {
        props.adjustedValues.additionalAdjustments!.push({
          type: a,
          adjustmentSubtotal: returnEmptyCurrencyDetails(),
          updatedValue: returnAdjustedDoubleValue(a),
          sumType: SumType.ADDITION,
        });
      });
    }
    // If outsideMetroFreightLinkController is true and we have a current
    // freight adjustment, then try and sync this to the outside metro
    // additional adjustment
    if (
      props.adjustedValues.freightTotal &&
      outsideMetroFreightLinkController
    ) {
      syncOutsideMetroValueWithFreight(
        props.adjustedValues.freightTotal.exclGst,
        props.adjustedValues.additionalAdjustments,
      );
    }
    computeAdditionalAdjustmentValues();
    // If fuelLevyFreightLinkController is true and we have a current freight
    // adjustment, then try and sync this to the fuel dollars
    if (props.adjustedValues.freightTotal && fuelLevyFreightLinkController) {
      syncFuelLevyDollarsValueWithFreight(
        props.adjustedValues.freightTotal.exclGst,
        props.adjustedValues.fuelLevyAdjustment,
      );
    }
  },
});

// Modelled to v-select multi to handle click event on custom list tiles
function selectOrDeselectAdditionalAdjustmentType(item: SelectOption) {
  const type = item.id as AdditionalAdjustmentType;
  // Check if item being selected or deselected is already in the selected list
  const isAlreadySelected =
    !!selectedAdditionalAdjustmentsController.value &&
    selectedAdditionalAdjustmentsController.value.includes(type);
  // If it is already selected, then we should remove it from the list
  if (isAlreadySelected && !!selectedAdditionalAdjustmentsController.value) {
    selectedAdditionalAdjustmentsController.value =
      selectedAdditionalAdjustmentsController.value.filter((a) => a !== type);
  } else {
    // If it is not already selected then we should add it to the list
    if (!!selectedAdditionalAdjustmentsController.value) {
      selectedAdditionalAdjustmentsController.value = [
        ...selectedAdditionalAdjustmentsController.value,
        type,
      ];
    } else {
      selectedAdditionalAdjustmentsController.value = [type];
    }
  }
}

function returnEmptyCurrencyDetails(): CurrencyDetails {
  return {
    exclGst: 0,
    gst: 0,
    total: 0,
  };
}

const isCustomAdjustment: ComputedRef<boolean> = computed(() => {
  return props.jobId === -1;
});

// Return true if the rateTableItem is a time rate
const isTimeRate: ComputedRef<boolean> = computed(() => {
  if (!rateTableItem.value) {
    return false;
  }
  return rateTableItem.value.rateTypeId === 1;
});

// Returns the duration property in adjustedValues as hours (from
// milliseconds). When setting, convert the incoming hour value back to
// milliseconds and set to adjustedValues.duration
const jobDurationHoursController: WritableComputedRef<number> = computed({
  get(): number {
    if (
      !isTimeRate.value ||
      !rateTableItem.value ||
      !props.adjustedValues.freightAdjustment ||
      props.adjustedValues.freightAdjustment.type !==
        FreightAdjustmentType.DURATION
    ) {
      return 0;
    }

    return returnHoursFromMilliseconds(
      props.adjustedValues.freightAdjustment.updatedDuration
        ? props.adjustedValues.freightAdjustment.updatedDuration.adjusted
        : 0,
    );
  },
  set(value: number): void {
    const incomingValue = value ?? 0;
    const adjusted =
      returnMillisecondsFromDuration(incomingValue, 'hours') +
      returnMillisecondsFromDuration(
        jobDurationMinutesController.value,
        'minutes',
      );
    if (props.adjustedValues.freightAdjustment?.updatedDuration) {
      props.adjustedValues.freightAdjustment.updatedDuration.adjusted =
        adjusted;
    }
  },
});

// Returns the duration property in adjustedValues as minutes (from
// milliseconds). When setting, convert the incoming minute value back to
// milliseconds and set to adjustedValues.duration
const jobDurationMinutesController: WritableComputedRef<number> = computed({
  get(): number {
    if (
      !isTimeRate.value ||
      !rateTableItem.value ||
      !props.adjustedValues.freightAdjustment ||
      props.adjustedValues.freightAdjustment.type !==
        FreightAdjustmentType.DURATION
    ) {
      return 0;
    }

    return returnMinutesFromMilliseconds(
      props.adjustedValues.freightAdjustment.updatedDuration
        ? props.adjustedValues.freightAdjustment.updatedDuration.adjusted
        : 0,
    );
  },
  set(value: number): void {
    const incomingValue = value ?? 0;
    const adjusted =
      returnMillisecondsFromDuration(
        jobDurationHoursController.value,
        'hours',
      ) + returnMillisecondsFromDuration(incomingValue, 'minutes');
    if (props.adjustedValues.freightAdjustment?.updatedDuration) {
      props.adjustedValues.freightAdjustment.updatedDuration.adjusted =
        adjusted;
    }
  },
});

// Pass in milliseconds, convert to moment duration and return the hours value
function returnHoursFromMilliseconds(millis: number | undefined) {
  const hours = moment.duration(millis ? millis : 0).hours();
  return hours;
}

// Pass in milliseconds, convert to moment duration and return the minutes value
function returnMinutesFromMilliseconds(millis: number | undefined) {
  // const hours = moment.duration(millis ? millis : 0).asHours();
  const minutes = moment.duration(millis ? millis : 0).minutes();
  return minutes ? Math.floor(minutes) : 0;
}

function returnExclGstTotalForAdditionalAdjustment(
  additionalAdjustment: AdditionalAdjustment,
): number {
  const currencyDetails =
    additionalAdjustment && additionalAdjustment.adjustmentSubtotal
      ? returnCurrencyDetailsForSumType(
          additionalAdjustment.adjustmentSubtotal,
          additionalAdjustment.sumType,
        )
      : { gst: 0, exclGst: 0, total: 0 };
  return currencyDetails.exclGst ? currencyDetails.exclGst : 0;
}

/**
 * Computes the validation rules from the store.
 * @returns {ComputedRef<Validation>} A computed reference to the validation rules.
 */
const validate: ComputedRef<Validation> = computed(() => {
  return validationRules;
});

// Called when components for Freight Adjustments are updated. For the selected
// FreightAdjustmentType, update adjustmentSubtotal and sumType based on the
// current adjusted values
function computeFreightAdjustmentValues(
  adjustmentType: FreightAdjustmentType | undefined,
): void {
  const errorMessage = `Some required freight information could not be found. Please try again later or contact GoDesta if problems persist.`;
  // Fetch current data from maps using jobId
  const jobAccounting = props.jobAccountingDetails;
  const adjustmentValues = props.adjustedValues.freightAdjustment;
  const rti = rateTableItem.value;

  const isClient = props.entityCategory === InvoiceEntityCategory.CLIENT;
  const jobPrimaryRate: JobPrimaryRate | null = isClient
    ? jobAccounting?.clientRates[0] ?? null
    : jobAccounting?.fleetAssetRates[0] ?? null;

  if (!adjustmentValues || !jobPrimaryRate) {
    showAppNotification(errorMessage);
    return;
  }

  const computeTimeRate = (duration: number, rate: number): number => {
    if (!rti) {
      return 0;
    }
    if (rti.rateTypeId === JobRateType.TIME) {
      const rateTypeObject = rti.rateTypeObject as TimeRateType;
      const rateAmount = timeRateCalculation({
        rate: rate,
        multiplier: rateTypeObject.rateMultiplier,
        durationInMs: duration,
        variancePct:
          props.entityCategory === InvoiceEntityCategory.CLIENT
            ? jobAccounting?.clientServiceRateVariations
                ?.clientAdjustmentPercentage
            : jobAccounting?.clientServiceRateVariations
                ?.fleetAssetAdjustmentPercentage,
      });

      return RoundCurrencyValue(rateAmount);
    }
    return 0;
  };

  let freightExclGst: number | undefined;

  switch (adjustmentType) {
    case FreightAdjustmentType.DURATION:
      if (!adjustmentValues.updatedDuration) {
        showAppNotification(errorMessage);
        return;
      }
      // Use duration from adjustment, original value for rate and compute the
      // updated value for freight
      const duration1 = adjustmentValues.updatedDuration!.adjusted;
      const rate1 = returnOriginalValueForType({
        freightType: FreightAdjustmentType.RATE,
      });
      // Compare new (adjusted) total with the original total, then set to
      // freightExclGst
      const origFreight1 = returnOriginalValueForType({
        freightType: FreightAdjustmentType.BASE_FREIGHT_DOLLARS,
      });
      const adjustedFreight1 = computeTimeRate(duration1, rate1);
      freightExclGst = RoundCurrencyValue(adjustedFreight1 - origFreight1);
      break;
    case FreightAdjustmentType.RATE:
      if (!adjustmentValues.updatedRate) {
        showAppNotification(errorMessage);
        return;
      }
      // Use duration from adjustment, original value for rate and compute the
      // updated value for freight
      const duration2 = returnOriginalValueForType({
        freightType: FreightAdjustmentType.DURATION,
      });
      const rate2 = adjustmentValues.updatedRate!.adjusted;

      // Compare new (adjusted) total with the original total, then set to
      // freightExclGst
      const origFreight2 = returnOriginalValueForType({
        freightType: FreightAdjustmentType.BASE_FREIGHT_DOLLARS,
      });
      const adjustedFreight2 = computeTimeRate(duration2, rate2);
      freightExclGst = RoundCurrencyValue(adjustedFreight2 - origFreight2);
      break;
    case FreightAdjustmentType.BASE_FREIGHT_DOLLARS:
      if (!adjustmentValues.updatedBaseFreight) {
        showAppNotification(errorMessage);
        return;
      }
      freightExclGst = RoundCurrencyValue(
        adjustmentValues.updatedBaseFreight.adjusted -
          adjustmentValues.updatedBaseFreight.original,
      );
      break;
  }
  // If we successfully found the excl GST value then we can set to the
  // adjustment subtotals
  if (freightExclGst === undefined) {
    return;
  }
  // If sumType is gte zero then set to SumType ADDITION
  adjustmentValues.sumType = returnSumTypeForDollarValue(freightExclGst);
  // Set to freight adjustment values
  adjustmentValues.adjustmentSubtotal =
    returnCurrencyDetailsForGstExclValue(freightExclGst);

  // If we have an OUTSIDE METRO adjustment applied and have the
  // boolean linkOutsideMetroDollarsWithFreight. We must do this
  // before fuel as the fuel calculation will use any OUTSIDE METRO
  // charges we have applied
  syncOutsideMetroValueWithFreight(
    freightExclGst,
    props.adjustedValues.additionalAdjustments,
  );
  // If we have fuel selected as DOLLARS and have the boolean
  // linkFuelLevyDollarsWithFreight
  syncFuelLevyDollarsValueWithFreight(
    freightExclGst,
    props.adjustedValues.fuelLevyAdjustment,
  );
}

function syncFuelLevyDollarsValueWithFreight(
  freightExclGst: number,
  fuelAdjustment?: FuelAdjustment,
) {
  // If we have fuel selected as DOLLARS and have the boolean linkFuelLevyDollarsWithFreight
  if (
    !!selectedFuelAdjustmentController.value &&
    fuelAdjustment &&
    fuelAdjustment.updatedValue &&
    selectedFuelAdjustmentController.value ===
      FuelAdjustmentType.TOTAL_DOLLARS &&
    fuelLevyFreightLinkController
  ) {
    // Use original fuel dollars and original fuel percentage to find the new
    // total using the updated freight for freight
    const originalFuelDollars = fuelAdjustment.updatedValue.original;
    const originalFuelPercent = returnOriginalValueForType({
      fuelType: FuelAdjustmentType.PERCENTAGE,
    });

    // Attempt to find an additionalAdjustment item for OUTSIDE METRO
    const foundOutsideMetroAdjustment = props.adjustedValues
      .additionalAdjustments
      ? props.adjustedValues.additionalAdjustments.find(
          (aa) => aa.type === AdditionalAdjustmentType.OUTSIDE_METRO,
        )
      : null;

    // If we found an Outside Metro adjustment, use the exclGst value. If we
    // did not find one, use the original value for OUTSIDE METRO
    const outsideMetroAdjustmentValue = foundOutsideMetroAdjustment
      ? returnExclGstTotalForAdditionalAdjustment(foundOutsideMetroAdjustment)
      : 0;

    const freightTotal = freightExclGst + outsideMetroAdjustmentValue;

    // Calculate adjusted fuel dollars
    const adjustedFuelDollars =
      originalFuelDollars + freightTotal * (originalFuelPercent / 100);

    // Set to updated value
    fuelAdjustment.updatedValue.adjusted =
      RoundCurrencyValue(adjustedFuelDollars);
    // Adjusted minus original is the dollar value of the adjustment
    const adjustmentValue = adjustedFuelDollars - originalFuelDollars;
    // Set sumType and adjustmentSubtotal
    fuelAdjustment.sumType = returnSumTypeForDollarValue(adjustmentValue);
    fuelAdjustment.adjustmentSubtotal =
      returnCurrencyDetailsForGstExclValue(adjustmentValue);
  }
}

function syncOutsideMetroValueWithFreight(
  freightExclGst: number,
  additionalAdjustments?: AdditionalAdjustment[],
) {
  // If we have fuel selected as DOLLARS and have the boolean
  // linkFuelLevyDollarsWithFreight
  if (
    !!selectedAdditionalAdjustmentsController.value &&
    additionalAdjustments &&
    additionalAdjustments.length &&
    selectedAdditionalAdjustmentsController.value.some(
      (a) => a === AdditionalAdjustmentType.OUTSIDE_METRO,
    ) &&
    outsideMetroFreightLinkController
  ) {
    // Find the OUTSIDE METRO adjustment so we can apply values to it
    const outsideMetroAdjustment = additionalAdjustments.find(
      (aa) => aa.type === AdditionalAdjustmentType.OUTSIDE_METRO,
    );
    if (!outsideMetroAdjustment || !outsideMetroAdjustment.updatedValue) {
      return;
    }
    // Use original Outside Metro (OM) dollars and OM percentage to find the new
    // total using the updated freight for freight
    const originalOutsideMetroDollars =
      outsideMetroAdjustment.updatedValue.original;
    const originalOutsideMetroPercent = returnOriginalValueForType({
      additionalType: AdditionalAdjustmentType.OUTSIDE_METRO_PERCENT,
    });
    // Calculate adjusted outside metro dollars
    const adjustedOutsideMetroDollars =
      originalOutsideMetroDollars +
      freightExclGst * (originalOutsideMetroPercent / 100);
    // Set to updated value
    outsideMetroAdjustment.updatedValue.adjusted = RoundCurrencyValue(
      adjustedOutsideMetroDollars,
    );
    // Adjusted minus original is the dollar value of the adjustment
    const adjustmentValue =
      adjustedOutsideMetroDollars - originalOutsideMetroDollars;
    // Set sumType and adjustmentSubtotal
    outsideMetroAdjustment.sumType =
      returnSumTypeForDollarValue(adjustmentValue);
    outsideMetroAdjustment.adjustmentSubtotal =
      returnCurrencyDetailsForGstExclValue(adjustmentValue);
  }
}

function returnSumTypeForDollarValue(value: number): SumType {
  return value >= 0 ? SumType.ADDITION : SumType.DEDUCTION;
}
// Called when components for Fuel Adjustments are updated. For the selected
// FuelAdjustmentType, update adjustmentSubtotal and sumType based on the
// current adjusted values
function computeFuelAdjustmentValues(
  adjustmentType: FuelAdjustmentType | undefined,
): void {
  const errorMessage = `Some required fuel information could not be found. Please try again later or contact GoDesta if problems persist.`;
  const jobAccounting = props.jobAccountingDetails;
  const adjustmentValues = props.adjustedValues.fuelLevyAdjustment;
  const isClient = props.entityCategory === InvoiceEntityCategory.CLIENT;

  if (!adjustmentValues || !adjustmentValues.updatedValue || !jobAccounting) {
    showAppNotification(errorMessage);
    return;
  }

  const computeFuelPercentAmount = (): number => {
    const originalFuelRate: number = adjustmentValues.updatedValue!.original;
    const adjustedFuelRate: number = adjustmentValues.updatedValue!.adjusted;
    const originalFuelTotalExclGst = isClient
      ? jobAccounting.totals.subtotals.fuelSurcharges.client
      : jobAccounting.totals.subtotals.fuelSurcharges.fleetAsset;
    // Find the total of charges that were applicable to the original fuel
    // levy percentage
    const fuelApplicableCharges =
      originalFuelTotalExclGst / (originalFuelRate / 100);

    // Apply the applicable charge value against the adjusted fuel percentage rate
    const adjustedFuelTotalExclGst =
      fuelApplicableCharges * (adjustedFuelRate / 100);

    // Subtract the original value from the adjusted value to find the dollar
    // value difference
    return RoundCurrencyValue(
      adjustedFuelTotalExclGst - originalFuelTotalExclGst,
    );
  };
  const computeFuelDollarsAmount = (): number => {
    const originalFuelDollars: number = adjustmentValues.updatedValue!.original;
    const adjustedFuelDollars: number = adjustmentValues.updatedValue!.adjusted;
    return RoundCurrencyValue(adjustedFuelDollars - originalFuelDollars);
  };

  let fuelExclGst;

  switch (adjustmentType) {
    case FuelAdjustmentType.PERCENTAGE:
      fuelExclGst = computeFuelPercentAmount();
      break;
    case FuelAdjustmentType.TOTAL_DOLLARS:
      fuelExclGst = computeFuelDollarsAmount();
      break;
  }

  // If we successfully found the excl GST value then we can set to the
  // adjustment subtotals
  if (fuelExclGst !== undefined) {
    // If sumType is gte zero then set to SumType ADDITION
    adjustmentValues.sumType = returnSumTypeForDollarValue(fuelExclGst);
    // Set to freight adjustment values
    adjustmentValues.adjustmentSubtotal =
      returnCurrencyDetailsForGstExclValue(fuelExclGst);
  }
}

// Called when component for the Additional Adjustments is updated. Iterates
// over all current additionalAdjustments and updates values for
// adjustmentSubtotal and sumType based on the current adjusted values
function computeAdditionalAdjustmentValues() {
  const errorMessage = `Some required additional charge information could not be found. Please try again later or contact GoDesta if problems persist.`;
  const additionalAdjustments = props.adjustedValues.additionalAdjustments;
  // Validate and show error if values are missing
  if (!additionalAdjustments || !additionalAdjustments.length) {
    showAppNotification(errorMessage);
    return;
  }

  const computeAdditionalChargeAmount = (
    adjustment: AdditionalAdjustment,
  ): number => {
    const original = adjustment.updatedValue!.original;
    const adjusted = adjustment.updatedValue!.adjusted;
    return RoundCurrencyValue(adjusted - original);
  };
  const computeAdjustmentSubtotalForType = (
    adjustment: AdditionalAdjustment,
  ): void => {
    if (!adjustment.updatedValue) {
      showAppNotification(errorMessage);
      return;
    }
    // All additional adjustments can be calculated simply by subtracting the
    // original from the adjusted
    const adjValue = computeAdditionalChargeAmount(adjustment);
    // Set SumType if we are dealing with non CUSTOM type
    if (adjustment.type !== AdditionalAdjustmentType.CUSTOM) {
      // If sumType is gte zero then set to SumType ADDITION
      adjustment.sumType = returnSumTypeForDollarValue(adjValue);
    }
    // Set to freight adjustment values
    adjustment.adjustmentSubtotal =
      adjustment.type !== AdditionalAdjustmentType.GST
        ? returnCurrencyDetailsForGstExclValue(adjValue)
        : returnCurrencyDetailsForGstValue(adjValue);
  };
  // Iterate over list of adjustments and update the adjustmentSubtotal
  // property
  additionalAdjustments
    .filter((a) => !!a.updatedValue)
    .forEach((adj) => {
      computeAdjustmentSubtotalForType(adj);
    });

  // If fuelLevyFreightLinkController is true and we have a current freight
  // adjustment, then try and sync this to the fuel dollars
  if (props.adjustedValues.freightTotal && fuelLevyFreightLinkController) {
    syncFuelLevyDollarsValueWithFreight(
      props.adjustedValues.freightTotal.exclGst,
      props.adjustedValues.fuelLevyAdjustment,
    );
  }
}
// Return a CurrencyDetails object with values for gst and total based on the
// provided value for gstExcl
function returnCurrencyDetailsForGstExclValue(exGst: number): CurrencyDetails {
  const exclGst = RoundCurrencyValue(Math.abs(exGst));
  const gst = RoundCurrencyValue(exclGst * 0.1);
  const total = RoundCurrencyValue(exclGst + gst);
  return {
    exclGst,
    gst,
    total,
  };
}
// Return a CurrencyDetails object with value gst and other property as zero
function returnCurrencyDetailsForGstValue(gst: number): CurrencyDetails {
  const gstRounded = RoundCurrencyValue(Math.abs(gst));
  return {
    exclGst: 0,
    gst: gstRounded,
    total: gstRounded,
  };
}

// Return the original value from jobAccountingDetails for the type provided.
// We should provide one of either freightType, fuelType and additionalType as
// a parameter.
function returnOriginalValueForType({
  freightType,
  fuelType,
  additionalType,
}: Partial<AdjustmentTypeOriginal> = {}): number {
  const isClient = props.entityCategory === InvoiceEntityCategory.CLIENT;
  const acc: JobAccountingDetails | undefined = props.jobAccountingDetails;
  if (!acc) {
    return 0;
  }
  const subtotals = acc?.totals?.subtotals ?? null;
  const rateTableItem: RateTableItems = isClient
    ? acc.clientRates[0].rate
    : acc.fleetAssetRates[0].rate;
  const isTimeRate: boolean = rateTableItem.rateTypeId === 1;
  // If it a freightType parameter, then return associated original value for
  // FreightAdjustmentType
  if (freightType) {
    switch (freightType) {
      case FreightAdjustmentType.BASE_FREIGHT_DOLLARS:
        return isClient
          ? acc.totals.subtotals.freightCharges.client
          : acc.totals.subtotals.freightCharges.fleetAsset;
      case FreightAdjustmentType.DURATION:
        return isClient
          ? acc.finishedJobData.clientDurations.actualBilledDuration
          : acc.finishedJobData.fleetAssetDurations.actualBilledDuration;
      case FreightAdjustmentType.RATE:
        return isTimeRate
          ? (rateTableItem.rateTypeObject as TimeRateType).rate
          : 0;
      default:
        return 0;
    }
    // If it a fuelType parameter, then return associated original value for
    // FuelAdjustmentType
  } else if (fuelType) {
    switch (fuelType) {
      case FuelAdjustmentType.TOTAL_DOLLARS:
        return isClient
          ? subtotals
            ? subtotals.fuelSurcharges.client
            : 0
          : subtotals
            ? subtotals.fuelSurcharges.fleetAsset
            : 0;
      case FuelAdjustmentType.PERCENTAGE:
        return isClient
          ? acc.additionalCharges.clientFuelSurcharge &&
            acc.additionalCharges.clientFuelSurcharge.appliedFuelSurchargeRate
            ? acc.additionalCharges.clientFuelSurcharge.appliedFuelSurchargeRate
            : 0
          : acc.additionalCharges.fleetAssetFuelSurcharge &&
              acc.additionalCharges.fleetAssetFuelSurcharge
                .appliedFuelSurchargeRate
            ? acc.additionalCharges.fleetAssetFuelSurcharge
                .appliedFuelSurchargeRate
            : 0;
      default:
        return 0;
    }
    // If it a additionalType parameter, then return associated original value for
    // AdditionalAdjustmentType
  } else if (additionalType) {
    switch (additionalType) {
      case AdditionalAdjustmentType.GST:
        if (!acc.totals.subtotals.gstCharges) {
          return 0;
        }
        return props.entityCategory === InvoiceEntityCategory.CLIENT
          ? acc.totals.subtotals.gstCharges.client
          : props.entityCategory === InvoiceEntityCategory.SUBCONTRACTOR
            ? acc.totals.subtotals.gstCharges.fleetAsset
            : 0;
      case AdditionalAdjustmentType.STANDBY:
        if (!acc.totals.subtotals.standbyChargeBreakdown) {
          return 0;
        }
        return props.entityCategory === InvoiceEntityCategory.CLIENT
          ? acc.totals.subtotals.standbyChargeBreakdown.client
            ? acc.totals.subtotals.standbyChargeBreakdown.client.reduce(
                (prev, curr) => prev + curr.totalExclGst,
                0,
              )
            : 0
          : props.entityCategory === InvoiceEntityCategory.SUBCONTRACTOR
            ? acc.totals.subtotals.standbyChargeBreakdown.fleetAsset
              ? acc.totals.subtotals.standbyChargeBreakdown.fleetAsset.reduce(
                  (prev, curr) => prev + curr.totalExclGst,
                  0,
                )
              : 0
            : 0;
      case AdditionalAdjustmentType.DEMURRAGE:
        if (!acc.totals.subtotals.demurrageChargeTotals) {
          return 0;
        }
        return props.entityCategory === InvoiceEntityCategory.CLIENT
          ? acc.totals.subtotals.demurrageChargeTotals.client
          : props.entityCategory === InvoiceEntityCategory.SUBCONTRACTOR
            ? acc.totals.subtotals.demurrageChargeTotals.fleetAsset
            : 0;
      case AdditionalAdjustmentType.TOLLS:
        if (!acc.totals.subtotals.tollCharges) {
          return 0;
        }
        return props.entityCategory === InvoiceEntityCategory.CLIENT
          ? acc.totals.subtotals.tollCharges.tollCharges &&
            acc.totals.subtotals.tollCharges.tollCharges.client
            ? acc.totals.subtotals.tollCharges.tollCharges.client
            : 0
          : props.entityCategory === InvoiceEntityCategory.SUBCONTRACTOR
            ? acc.totals.subtotals.tollCharges.tollCharges &&
              acc.totals.subtotals.tollCharges.tollCharges.fleetAsset
              ? acc.totals.subtotals.tollCharges.tollCharges.fleetAsset
              : 0
            : 0;
      case AdditionalAdjustmentType.OUTSIDE_METRO:
        if (!acc.totals.subtotals.outsideMetroChargeTotals) {
          return 0;
        }
        return props.entityCategory === InvoiceEntityCategory.CLIENT
          ? acc.totals.subtotals.outsideMetroChargeTotals.client
          : props.entityCategory === InvoiceEntityCategory.SUBCONTRACTOR
            ? acc.totals.subtotals.outsideMetroChargeTotals.fleetAsset
            : 0;
      case AdditionalAdjustmentType.OUTSIDE_METRO_PERCENT:
        return props.entityCategory === InvoiceEntityCategory.CLIENT
          ? acc.clientRates &&
            acc.clientRates[0] &&
            acc.clientRates[0].outsideMetroRate
            ? acc.clientRates[0].outsideMetroRate
            : 0
          : props.entityCategory === InvoiceEntityCategory.SUBCONTRACTOR
            ? acc.fleetAssetRates &&
              acc.fleetAssetRates[0] &&
              acc.fleetAssetRates[0].outsideMetroRate
              ? acc.fleetAssetRates[0].outsideMetroRate
              : 0
            : 0;
      case AdditionalAdjustmentType.CUSTOM:
        return 0;
      default:
        return 0;
    }
  } else {
    return 0;
  }
}

function removeJobFromAdjustment() {
  emit('removeJobFromAdjustment', props.jobId);
}

const durationSuffix: ComputedRef<string> = computed(() => {
  if (!isTimeRate.value || !rateTableItem.value?.rateTypeObject) {
    return '';
  }
  const timeRate = rateTableItem.value.rateTypeObject as TimeRateType;
  const rateMultiplier = rateMultipliers.find(
    (r) => r.id === timeRate.rateMultiplier,
  );
  const chargeIncrement = timeRate.rateMultiplier;

  return chargeIncrement && rateMultiplier
    ? `per ${rateMultiplier.shortName}`
    : '';
});

// return list of alert messages for Adjustment Value(s)
const alertWarningMessage = computed(() => {
  if (!props.reportType) {
    return [];
  }

  const isClient = props.entityCategory === InvoiceEntityCategory.CLIENT;

  const amount = props.adjustedValues.combinedTotalForJob.exclGst ?? 0;
  const positive = amount > 0;
  const negative = amount < 0;

  if (isClient) {
    if (positive) {
      return ['You are creating an invoice for the selected client.'];
    } else if (negative) {
      return ['You are creating a credit note for the selected client.'];
    }
  } else {
    switch (props.reportType) {
      case InvoiceAdjustmentReportType.RECIPIENT_CREATED:
        if (positive) {
          return [
            'This Recipient Created adjustment will increase the amount the driver is paid.',
          ];
        } else if (negative) {
          return [
            'This Recipient Created adjustment will decrease the amount the driver is paid.',
          ];
        }
        break;

      case InvoiceAdjustmentReportType.INVOICE:
        if (positive) {
          return [
            'You are creating an invoice for the selected driver.',
            'This will result in the driver being paid less because they are being charged for additional fees',
          ];
        } else if (negative) {
          return [
            'You are creating a credit note for the selected driver.',
            'This will result in the driver being paid more because they are being credited.',
          ];
        }
        break;
    }
  }
  return [];
});

defineExpose({
  selectedFreightAdjustmentController,
  computeFreightAdjustmentValues,
  selectedFuelAdjustmentController,
  computeFuelAdjustmentValues,
  selectedAdditionalAdjustmentsController,
  computeAdditionalAdjustmentValues,
});
</script>
<style scoped lang="scss">
.invoice-adjustment-adjust-job-values {
  position: relative;
  .shrink-input {
    transform: scale(0.8);
    transform-origin: right;
  }

  .alert-container {
    margin: 0;
    padding-bottom: 18px;
    align-items: center;
    display: flex;
    justify-content: center;
    justify-items: center;
  }

  .header-section {
    .overall-total-text {
      font-family: $sub-font-family;
      font-size: $font-size-20;
      font-weight: 700;
      text-align: right;
      letter-spacing: 1px;
      &.negative-value {
        color: rgb(255, 55, 55);
      }
      &.positive-value {
        color: rgb(49, 254, 49);
      }
    }
    .caption-text {
      color: rgb(176, 176, 189);
      font-size: $font-size-12;
    }
  }

  .job-adjustments-table {
    border-collapse: collapse;
    width: 100%;

    .subheader--divider {
      margin-bottom: 10px;
    }
    tbody {
      tr {
        td {
          vertical-align: top;
        }
        td:nth-child(1) {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding-top: 10px;
          padding: 10px 16px 0px 16px;
        }

        td:nth-child(3) {
          max-width: 15vw;
          padding-left: 8px;
          padding-right: 8px;
        }
        td:nth-child(4) {
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: bold;
          font-size: $font-size-16;
          background-color: rgba(224, 220, 220, 0.118);
          padding: 10px;
        }
        td:nth-child(5) {
          font-weight: bold;
          font-size: $font-size-16;
          background-color: rgba(228, 56, 56, 0.118);
          padding: 10px;
        }
      }
    }

    thead {
      th {
        text-align: center;
        font-weight: 600;
        padding-bottom: 10px;
        padding-top: 10px;
        font-size: $font-size-16;
      }
    }
  }

  .not-available-text {
    font-size: $font-size-10;
    font-style: italic;
  }
}
</style>
