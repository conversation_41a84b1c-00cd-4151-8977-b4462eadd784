<template>
  <section class="service-rate-table-read-only">
    <!-- Loading State -->
    <div
      v-if="isLoadingServiceRate || isLoadingVariations"
      class="text-center py-4"
    >
      <v-progress-circular
        indeterminate
        color="primary"
        size="40"
      ></v-progress-circular>
      <p class="mt-2 text-muted">Loading service rates and variations...</p>
    </div>

    <!-- No Data State -->
    <v-alert
      v-else-if="!serviceRate || !serviceRate.rateTableItems.length"
      :value="true"
      color="info"
      icon="info"
      class="mb-3"
    >
      No service rates are currently configured.
    </v-alert>

    <!-- Rate Type Navigation Buttons -->
    <v-flex v-else class="button-group">
      <v-tooltip
        v-for="rateType in availableRateTypes"
        :key="rateType.rateTypeId"
        bottom
        :disabled="true"
      >
        <template v-slot:activator="{ on }">
          <span v-on="on">
            <v-btn
              :class="{
                'v-btn--active': selectedRateTypeId === rateType.rateTypeId,
              }"
              flat
              @click="selectedRateTypeId = rateType.rateTypeId"
            >
              <span class="px-2">
                <strong>{{ rateType.longName.toUpperCase() }} RATES</strong>
              </span>
            </v-btn>
          </span>
        </template>
      </v-tooltip>
    </v-flex>

    <!-- tables -->
    <div
      v-if="
        !isLoadingServiceRate &&
        !isLoadingVariations &&
        serviceRate &&
        serviceRate.rateTableItems.length > 0
      "
    >
      <!-- Time Rate Table -->
      <div
        v-if="
          selectedRateTypeId === JobRateType.TIME && timeRateItems.length > 0
        "
      >
        <v-data-table
          class="default-table-dark client-invoice-accounting-table gd-dark-theme"
          :headers="timeHeaders"
          :items="timeRateItems"
          hide-actions
          :rows-per-page-items="[10, 20]"
        >
          <template v-slot:items="slotProps">
            <tr>
              <td>{{ getServiceName(slotProps.item.serviceTypeId) }}</td>
              <td>
                <v-tooltip v-if="slotProps.item.rateTypeObject.rate > 0" bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <span v-bind="attrs" v-on="on" class="rate-text">
                      {{ formatTimeRateWithVariation(slotProps.item) }}
                      <span v-if="slotProps.item.rateTypeObject.rate > 0">{{
                        getRateVariationSymbol(slotProps.item)
                      }}</span>
                    </span>
                  </template>
                  <span>{{ getRateTooltip(slotProps.item, 'time') }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ formatTimeRateWithVariation(slotProps.item) }}
                </span>
              </td>
              <td>{{ formatMinCharge(slotProps.item.rateTypeObject) }}</td>
              <td>
                {{ formatChargeIncrement(slotProps.item.rateTypeObject) }}
              </td>
              <td>
                {{
                  returnGraceShortName(slotProps.item.rateTypeObject.graceType)
                }}
              </td>
              <td>
                <v-tooltip
                  v-if="slotProps.item.rateTypeObject.standbyRate > 0"
                  bottom
                >
                  <template v-slot:activator="{ on, attrs }">
                    <span v-bind="attrs" v-on="on" class="rate-text">
                      {{ formatStandbyRateWithVariation(slotProps.item) }}
                      <span
                        v-if="slotProps.item.rateTypeObject.standbyRate > 0"
                        >{{ getRateVariationSymbol(slotProps.item) }}</span
                      >
                    </span>
                  </template>
                  <span>{{ getRateTooltip(slotProps.item, 'standby') }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ formatStandbyRateWithVariation(slotProps.item) }}
                </span>
              </td>
              <td>
                {{
                  formatFuelSurcharge(
                    slotProps.item.rateTypeObject.appliedFuelSurchargeId,
                  )
                }}
              </td>
            </tr>
          </template>
        </v-data-table>
      </div>

      <!-- Zone Rate Table -->
      <div v-if="selectedRateTypeId === JobRateType.ZONE">
        <v-data-table
          class="gd-dark-theme"
          :headers="zoneHeaders"
          :items="zoneRateTableData"
          hide-actions
          :rows-per-page-items="[10, 20]"
        >
          <template v-slot:items="slotProps">
            <tr
              :class="{
                'service-header-row': slotProps.item.isServiceHeader,
                'zone-child-row': slotProps.item.isZoneRow,
              }"
            >
              <td>
                <span
                  v-if="slotProps.item.isServiceHeader"
                  class="service-header-text"
                >
                  <strong>{{ slotProps.item.serviceTypeName }}</strong>
                </span>
                <span v-else class="zone-child-text">
                  {{ slotProps.item.serviceTypeName }}
                </span>
              </td>
              <td>
                <template v-if="!slotProps.item.isServiceHeader">
                  <v-tooltip v-if="slotProps.item.rate > 0" bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="rate-text">
                        {{ slotProps.item.zoneRate }}
                        <span v-if="slotProps.item.baseRate > 0">{{
                          getRateVariationSymbol(slotProps.item)
                        }}</span>
                      </span>
                    </template>
                    <span>{{ getRateTooltip(slotProps.item, 'zone') }}</span>
                  </v-tooltip>
                  <v-tooltip v-else-if="!slotProps.item.zoneRate" bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        class="rate-text missing-rate"
                      >
                        No rate configured
                      </span>
                    </template>
                    <span
                      >This zone does not have a rate configured. Please contact
                      your administrator to set up rates for this zone.</span
                    >
                  </v-tooltip>
                  <span v-else class="rate-text">
                    {{ slotProps.item.zoneRate }}
                  </span>
                </template>
              </td>
              <td>{{ slotProps.item.pickupFlagfall }}</td>
              <td>{{ slotProps.item.dropoffFlagfall }}</td>
              <td>{{ slotProps.item.percentage }}</td>
              <td>{{ slotProps.item.demurrageRate }}</td>
              <td v-if="shouldApplyRateVariationsToDemurrage">
                <template v-if="!slotProps.item.isServiceHeader">
                  <v-tooltip v-if="slotProps.item.demurrageVariation" bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        class="rate-variation-text"
                      >
                        {{ slotProps.item.demurrageVariation }}
                      </span>
                    </template>
                    <span
                      >Rate variation applied to demurrage calculations</span
                    >
                  </v-tooltip>
                  <v-tooltip v-else bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="missing-variation">
                        No variation
                      </span>
                    </template>
                    <span
                      >No rate variation is currently applied to this service's
                      demurrage calculations.</span
                    >
                  </v-tooltip>
                </template>
              </td>
              <td>{{ slotProps.item.appliedFuelSurcharge }}</td>
            </tr>
          </template>
        </v-data-table>
      </div>

      <!-- Distance Rate Table -->
      <div
        v-if="
          selectedRateTypeId === JobRateType.DISTANCE &&
          distanceRateItems.length > 0
        "
      >
        <v-data-table
          class="gd-dark-theme"
          :headers="distanceHeaders"
          :items="distanceRateTableData"
          hide-actions
        >
          <template v-slot:items="slotProps">
            <tr
              :class="{
                'service-header-row': slotProps.item.isServiceHeader,
                'distance-child-row': slotProps.item.isDistanceRow,
              }"
            >
              <td>
                <span
                  v-if="slotProps.item.isServiceHeader"
                  class="service-header-text"
                >
                  <strong>{{ slotProps.item.serviceTypeName }}</strong>
                </span>
                <span v-else class="distance-child-text">
                  {{ slotProps.item.serviceTypeName }}
                </span>
              </td>
              <td>
                <template v-if="!slotProps.item.isServiceHeader">
                  <v-tooltip
                    v-if="slotProps.item.rateTypeObject?.baseFreightCharge > 0"
                    bottom
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="rate-text">
                        {{ slotProps.item.baseFreight }}
                        <span
                          v-if="
                            slotProps.item.rateTypeObject?.baseFreightCharge > 0
                          "
                          >{{ getRateVariationSymbol(slotProps.item) }}</span
                        >
                      </span>
                    </template>
                    <span>{{
                      getRateTooltip(slotProps.item, 'distance')
                    }}</span>
                  </v-tooltip>
                  <span v-else class="rate-text">
                    {{ slotProps.item.baseFreight }}
                  </span>
                </template>
              </td>
              <td>{{ slotProps.item.calculation }}</td>
              <td>{{ slotProps.item.increment }}</td>
              <td>{{ slotProps.item.legs }}</td>
              <td class="text-center">{{ slotProps.item.minimumCharge }}</td>
              <td>{{ slotProps.item.demurrage }}</td>
              <td v-if="shouldApplyRateVariationsToDemurrage">
                <template v-if="!slotProps.item.isServiceHeader">
                  <v-tooltip v-if="slotProps.item.demurrageVariation" bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <span
                        v-bind="attrs"
                        v-on="on"
                        class="rate-variation-text"
                      >
                        {{ slotProps.item.demurrageVariation }}
                      </span>
                    </template>
                    <span
                      >Rate variation applied to demurrage calculations</span
                    >
                  </v-tooltip>
                  <v-tooltip v-else bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on" class="missing-variation">
                        No variation
                      </span>
                    </template>
                    <span
                      >No rate variation is currently applied to this service's
                      demurrage calculations.</span
                    >
                  </v-tooltip>
                </template>
              </td>
              <td>{{ slotProps.item.range }}</td>
            </tr>
          </template>
        </v-data-table>
      </div>

      <!-- Point-to-Point Rate Table -->
      <div v-if="selectedRateTypeId === JobRateType.POINT_TO_POINT">
        <v-data-table
          class="default-table-dark client-invoice-accounting-table gd-dark-theme"
          :headers="pointToPointHeaders"
          :items="pointToPointRateTableData"
          hide-actions
          :rows-per-page-items="[10, 20]"
        >
          <template v-slot:items="slotProps">
            <tr>
              <td>{{ slotProps.item.serviceTypeName }}</td>
              <td>{{ slotProps.item.fromAddress }}</td>
              <td>{{ slotProps.item.toAddress }}</td>
              <td>
                <v-tooltip v-if="slotProps.item.baseRate > 0" bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <span v-bind="attrs" v-on="on" class="rate-text">
                      {{ slotProps.item.rate }}
                      <span v-if="slotProps.item.rateTypeObject.baseRate > 0">
                        {{ getRateVariationSymbol(slotProps.item) }}
                      </span>
                    </span>
                  </template>
                  <span>{{
                    getRateTooltip(slotProps.item, 'pointToPoint')
                  }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ slotProps.item.rate }}
                </span>
              </td>
              <td>{{ slotProps.item.demurrageRate }}</td>
              <td>{{ slotProps.item.appliedFuelSurcharge }}</td>
            </tr>
          </template>
        </v-data-table>
      </div>

      <!-- Unit Rate Table -->
      <div v-if="selectedRateTypeId === JobRateType.UNIT">
        <v-data-table
          class="default-table-dark client-invoice-accounting-table gd-dark-theme"
          :headers="unitHeaders"
          :items="unitRateTableData"
          hide-actions
          :rows-per-page-items="[10, 20]"
        >
          <template v-slot:items="slotProps">
            <tr>
              <td>{{ slotProps.item.unitTypeName }}</td>
              <td>
                <v-tooltip v-if="showRateVariationsHighlight" bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <span v-bind="attrs" v-on="on" class="rate-text">
                      {{ slotProps.item.rate }}
                      <span
                        v-if="
                          slotProps.item.unitRateObject.unitRanges.some(
                            (range) => range.unitRate > 0,
                          )
                        "
                      >
                        {{ getRateVariationSymbol(slotProps.item) }}
                      </span>
                    </span>
                  </template>
                  <span>{{ getRateTooltip(slotProps.item, 'unit') }}</span>
                </v-tooltip>
                <span v-else class="rate-text">
                  {{ slotProps.item.rate }}
                </span>
              </td>
              <td>{{ slotProps.item.zoneName }}</td>
              <td>
                {{ slotProps.item.unitAmountMultiplier }}
              </td>
              <td>
                {{ slotProps.item.forklift }}
              </td>
              <td class="rate-text">
                {{ slotProps.item.puFlagFall }}
              </td>
              <td class="rate-text">
                {{ slotProps.item.doFlagFall }}
              </td>
              <td class="rate-text">
                {{ slotProps.item.flPuFlagFall }}
              </td>
              <td class="rate-text">
                {{ slotProps.item.flDoFlagFall }}
              </td>
              <td class="rate-text">
                {{ slotProps.item.dgFlagFall }}
              </td>
              <!-- <td>{{ slotProps.item.appliedFuelSurcharge }}</td>
              <td>
                {{ slotProps.item.demurrageRate }}
              </td> -->
            </tr>
          </template>
        </v-data-table>
      </div>
    </div>
    <!-- End Rate Tables Container -->
  </section>
</template>

<script setup lang="ts">
import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { getServiceTypeById } from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { CurrentClientServiceRateResponse } from '@/interface-models/Client/CurrentClientServiceRateResponse';
import { returnGraceShortName } from '@/interface-models/Generic/ServiceTypes/GraceTypes';
import { rateMultipliers } from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import serviceTypeRates, {
  JobRateType,
  ServiceTypeRates,
} from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import TableHeader from '@/interface-models/Generic/Table/TableHeader';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import applicableFuelSurcharges from '@/interface-models/ServiceRates/FuelSurcharges/applicableFuelSurcharges';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { returnReadableChargeBasisName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import DistanceRateType from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/DistanceRateType';
import { returnReadableRateBracketTypeName } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { useServiceRateVariationsStore } from '@/store/modules/ServiceRateVariationsStore';
import moment from 'moment-timezone';
import { computed, ComputedRef, onMounted, ref, Ref, watch } from 'vue';

import {
  returnChargeIncrementDescription,
  returnMinimumChargeDescription,
  returnRangeRateListSummary,
} from '@/helpers/RateHelpers/DistanceRateHelpers';
import { returnStartAndReturnLegsLongNameById } from '@/interface-models/Generic/StartAndReturnLegs/StartAndReturnLegs';
import { demurrageAppliesLabel } from '@/interface-models/ServiceRates/Demurrage/applicableDemurrages';

const props = withDefaults(
  defineProps<{
    clientId: string;
    searchDate?: number;
    showRateVariationsHighlight?: boolean;
  }>(),
  {
    searchDate: Date.now(),
    showRateVariationsHighlight: true,
  },
);

const serviceRateStore = useServiceRateStore();
const serviceRateVariationsStore = useServiceRateVariationsStore();
const companyDetailsStore = useCompanyDetailsStore();

const selectedRateTypeId: Ref<JobRateType> = ref(JobRateType.TIME);

// service rates and rate variations
const serviceRate: Ref<ClientServiceRate | null> = ref(null);
const isLoadingServiceRate: Ref<boolean> = ref(false);
const rateVariations: Ref<ClientServiceRateVariations[]> = ref([]);
const isLoadingVariations: Ref<boolean> = ref(false);

const availableRateTypes: ComputedRef<ServiceTypeRates[]> = computed(() => {
  if (!serviceRate.value || !serviceRate.value.rateTableItems.length) {
    return [];
  }
  // Get unique rate type IDs
  const rateTypeIdsInData = new Set(
    serviceRate.value.rateTableItems.map((item) => item.rateTypeId),
  );
  // Filter serviceTypeRates to only include rate types that have data
  return serviceTypeRates.filter(
    (rateType) => !rateType.adhoc && rateTypeIdsInData.has(rateType.rateTypeId),
  );
});

/**
 * Computed property to check if demurrage rate variations should be applied
 * based on the division's custom configuration
 */
const shouldApplyRateVariationsToDemurrage: ComputedRef<boolean> = computed(
  () => {
    return (
      companyDetailsStore.divisionCustomConfig?.accounting
        ?.applyRateVariationsToDemurrage ?? false
    );
  },
);

/**
 * Loads service rates for the specified client
 */
async function loadServiceRate(): Promise<void> {
  if (!props.clientId) {
    return;
  }

  isLoadingServiceRate.value = true;
  try {
    const searchDate = props.searchDate || Date.now();
    const response: CurrentClientServiceRateResponse | null =
      await serviceRateStore.getMergedClientServiceRates(
        props.clientId,
        searchDate,
      );
    if (response) {
      serviceRate.value = response.clientServiceRate;
    }
  } catch (error) {
    console.error('Error loading service rates:', error);
    serviceRate.value = null;
  } finally {
    isLoadingServiceRate.value = false;
  }
}

/**
 * Loads rate variations for the specified client
 */
async function loadRateVariations(): Promise<void> {
  if (!props.clientId) {
    return;
  }

  isLoadingVariations.value = true;
  try {
    const searchDate = Date.now(); // Use current date for active variations
    const variations =
      await serviceRateVariationsStore.getServiceRateVariationsByClient(
        props.clientId,
        searchDate,
      );
    rateVariations.value = variations || [];
  } catch (error) {
    console.error('Error loading rate variations:', error);
    rateVariations.value = [];
  } finally {
    isLoadingVariations.value = false;
  }
}

// Time Rate Items and Headers
const timeRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.TIME,
  );
});
const timeHeaders: ComputedRef<TableHeader[]> = computed(() => [
  {
    text: 'Service Type',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  { text: 'Rate', align: 'left', value: 'rate', sortable: false },
  { text: 'Min Charge', align: 'left', value: 'minCharge', sortable: false },
  {
    text: 'Charge Increment',
    align: 'left',
    value: 'chargeIncrement',
    sortable: false,
  },
  { text: 'Grace', align: 'left', value: 'grace', sortable: false },
  {
    text: 'Standby Rate',
    align: 'left',
    value: 'standbyRate',
    sortable: false,
  },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
]);

// Distance Rate Items and Headers
const distanceRateItems: ComputedRef<RateTableItems[]> = computed(() => {
  if (!serviceRate.value) {
    return [];
  }
  return serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.DISTANCE,
  );
});

/**
 * Computed property for grouped distance rate table data
 * @returns Array of grouped distance rate table items with services as parent rows and distance configurations as child rows
 */
const distanceRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const distanceItems = serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.DISTANCE,
  );
  if (!distanceItems.length) {
    return [];
  }

  const groupedData: any[] = [];

  distanceItems.forEach((service) => {
    const serviceName = getServiceName(service.serviceTypeId);
    const rateObj: DistanceRateType =
      service.rateTypeObject as DistanceRateType;

    // Calculate demurrage variation if applicable
    const variationPercentage = getRateVariationPercentage(service);
    const demurrageVariationText =
      variationPercentage !== null && shouldApplyRateVariationsToDemurrage.value
        ? `${variationPercentage > 0 ? '+' : ''}${variationPercentage.toFixed(
            2,
          )}%`
        : '';

    // Add service parent row
    groupedData.push({
      isServiceHeader: true,
      serviceTypeName: serviceName,
      serviceTypeId: service.serviceTypeId ?? null,
      baseFreight: '',
      calculation: '',
      increment: '',
      legs: '',
      minimumCharge: '',
      demurrage: '',
      demurrageVariation: '',
      range: '',
      rateTableItem: service,
    });

    // Add distance configuration child row
    groupedData.push({
      isServiceHeader: false,
      isDistanceRow: true,
      serviceTypeName: `  Distance Configuration`, // Indented configuration name
      serviceTypeId: service.serviceTypeId ?? null,
      baseFreight: formatBaseFreightWithVariation(service),
      calculation: `${returnReadableChargeBasisName(
        rateObj.chargeBasis,
      )} (${returnReadableRateBracketTypeName(rateObj.rateBracketType)})`,
      increment: returnChargeIncrementDescription(rateObj),
      legs: `${
        returnStartAndReturnLegsLongNameById(rateObj.firstLegTypeId) || 'N/A'
      } / ${
        returnStartAndReturnLegsLongNameById(rateObj.lastLegTypeId) || 'N/A'
      }`,
      minimumCharge: returnMinimumChargeDescription(rateObj),
      demurrage: demurrageAppliesLabel(rateObj.demurrage?.appliedDemurrageId),
      demurrageVariation: demurrageVariationText,
      range: returnRangeRateListSummary(rateObj.rates, 0),
      rateTableItem: service,
      rateTypeObject: rateObj,
    });
  });

  return groupedData;
});

const distanceHeaders: ComputedRef<TableHeader[]> = computed(() => {
  const baseHeaders: TableHeader[] = [
    {
      text: 'Service Type',
      align: 'left' as const,
      value: 'serviceType',
      sortable: false,
    },
    {
      text: 'Base Freight',
      align: 'left' as const,
      value: 'baseFreight',
      sortable: false,
    },
    {
      text: 'Calculation',
      align: 'left' as const,
      value: 'calculation',
      sortable: false,
    },
    {
      text: 'Rate Increment',
      align: 'left' as const,
      value: 'increment',
      sortable: false,
    },
    {
      text: 'First Leg / Last Leg',
      align: 'left' as const,
      value: 'legs',
      sortable: false,
    },
    {
      text: 'Minimum Charge',
      align: 'left' as const,
      value: 'minimumCharge',
      sortable: false,
    },
  ];

  // Conditionally add demurrage columns based on configuration
  if (shouldApplyRateVariationsToDemurrage.value) {
    baseHeaders.push(
      {
        text: 'Demurrage',
        align: 'left' as const,
        value: 'demurrage',
        sortable: false,
      },
      {
        text: 'Demurrage Variation',
        align: 'left' as const,
        value: 'demurrageVariation',
        sortable: false,
      },
    );
  } else {
    baseHeaders.push({
      text: 'Demurrage',
      align: 'left' as const,
      value: 'demurrage',
      sortable: false,
    });
  }

  baseHeaders.push({
    text: 'Range',
    align: 'left' as const,
    value: 'range',
    sortable: false,
  });

  return baseHeaders;
});

// Zone Rate Items and Headers
const zoneHeaders: ComputedRef<TableHeader[]> = computed(() => {
  const baseHeaders: TableHeader[] = [
    {
      text: 'Service Type / Zone',
      align: 'left' as const,
      value: 'serviceType',
      sortable: false,
    },
    {
      text: 'Zone Rate',
      align: 'left' as const,
      value: 'zoneRate',
      sortable: false,
    },
    {
      text: 'Pickup Flag Fall',
      align: 'left' as const,
      value: 'pickupFlagfall',
      sortable: false,
    },
    {
      text: 'Drop-off Flag Fall',
      align: 'left' as const,
      value: 'dropoffFlagfall',
      sortable: false,
    },
    {
      text: 'Percentage',
      align: 'left' as const,
      value: 'percentage',
      sortable: false,
    },
  ];

  // Conditionally add demurrage columns based on configuration
  if (shouldApplyRateVariationsToDemurrage.value) {
    baseHeaders.push(
      {
        text: 'Demurrage',
        align: 'left' as const,
        value: 'demurrage',
        sortable: false,
      },
      {
        text: 'Demurrage Variation',
        align: 'left' as const,
        value: 'demurrageVariation',
        sortable: false,
      },
    );
  } else {
    baseHeaders.push({
      text: 'Demurrage',
      align: 'left' as const,
      value: 'demurrage',
      sortable: false,
    });
  }

  baseHeaders.push({
    text: 'Fuel Surcharge',
    align: 'left' as const,
    value: 'fuelSurcharge',
    sortable: false,
  });

  return baseHeaders;
});
/**
 * Computed property for grouped zone rate table data
 * @returns Array of grouped zone rate table items with services as parent rows and zones as child rows
 */
const zoneRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const zoneRateItems = serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.ZONE,
  );
  if (!zoneRateItems.length) {
    return [];
  }

  const groupedData: any[] = [];

  zoneRateItems.forEach((service) => {
    const zoneRates: ZoneRateType[] = service.rateTypeObject as ZoneRateType[];
    const serviceName = getServiceName(service.serviceTypeId);

    // Add service parent row
    groupedData.push({
      isServiceHeader: true,
      serviceTypeName: serviceName,
      serviceTypeId: service.serviceTypeId ?? null,
      zoneRate: '',
      pickupFlagfall: '',
      dropoffFlagfall: '',
      percentage: '',
      demurrageRate: '',
      demurrageVariation: '',
      appliedFuelSurcharge: '',
      rateTableItem: service,
    });

    // Add zone child rows
    zoneRates.forEach((zone: ZoneRateType, index: number) => {
      const appliedFuelSurcharge = applicableFuelSurcharges.find(
        (x) => zone.appliedFuelSurchargeId === x.id,
      );

      const demurrageGraceAsMinutes = moment
        .duration(zone.demurrage.graceTimeInMilliseconds)
        .asMinutes();

      // Calculate demurrage variation if applicable
      const variationPercentage = getRateVariationPercentage(service);
      const demurrageVariationText =
        variationPercentage !== null &&
        shouldApplyRateVariationsToDemurrage.value
          ? `${variationPercentage > 0 ? '+' : ''}${variationPercentage.toFixed(
              2,
            )}%`
          : '';

      groupedData.push({
        isServiceHeader: false,
        isZoneRow: true,
        index,
        serviceTypeName: `  ${zone.zoneName}`, // Indented zone name
        serviceTypeId: service.serviceTypeId ?? null,
        zoneName: zone.zoneName,
        rate: formatZoneRateWithVariation(service, zone.rate),
        baseRate: zone.rate,
        zoneRate: formatZoneRateWithVariation(service, zone.rate),
        baseZoneRate: zone.rate,
        pickupFlagfall: `${zone.additionalPickUpFlagFall} mins`,
        dropoffFlagfall: `${zone.additionalDropOffFlagFall} mins`,
        demurrageGrace: !demurrageGraceAsMinutes
          ? 'None'
          : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
        demurrageRate: `Apply: $${DisplayCurrencyValue(
          zone.demurrage.rate,
        )}/hr - ${demurrageGraceAsMinutes} min grace`,
        demurrageVariation: demurrageVariationText,
        appliedFuelSurcharge: appliedFuelSurcharge
          ? appliedFuelSurcharge.shortName
          : '-',
        percentage: `${zone.percentage}%`,
        isClient: true,
        demurrageFuelSurchargeApplies: zone.demurrage
          .demurrageFuelSurchargeApplies
          ? 'Apply'
          : "Don't Apply",
        rateTableItem: service,
      });
    });
  });

  return groupedData;
});

// POINT to POINT Rate Items and Headers
const pointToPointHeaders: ComputedRef<TableHeader[]> = computed(() => [
  {
    text: 'Service Type',
    align: 'left',
    value: 'serviceType',
    sortable: false,
  },
  { text: 'From', align: 'left', value: 'from', sortable: false },
  { text: 'To', align: 'left', value: 'to', sortable: false },
  { text: 'Rate', align: 'left', value: 'rate', sortable: false },
  {
    text: 'Percentage',
    align: 'left',
    value: 'percentage',
    sortable: false,
  },
  { text: 'Demurrage', align: 'left', value: 'demurrage', sortable: false },
  {
    text: 'Fuel Surcharge',
    align: 'left',
    value: 'fuelSurcharge',
    sortable: false,
  },
]);
/**
 * Computed property for point-to-point rate table data
 * @returns Array of point-to-point rate table items
 */
const pointToPointRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const pointToPointRateItems = serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.POINT_TO_POINT,
  );
  if (!pointToPointRateItems.length) {
    return [];
  }

  return pointToPointRateItems.flatMap((service) => {
    const rates: PointToPointRateType[] =
      service.rateTypeObject as PointToPointRateType[];

    return rates.map((rateType: PointToPointRateType, index: number) => {
      const appliedFuelSurcharge = applicableFuelSurcharges.find(
        (x) => rateType.appliedFuelSurchargeId === x.id,
      );
      const demurrageGraceAsMinutes = moment
        .duration(rateType.demurrage.graceTimeInMilliseconds)
        .asMinutes();
      return {
        index,
        serviceTypeName: getServiceName(service.serviceTypeId),
        serviceTypeId: service.serviceTypeId ?? null,
        fromAddress: rateType.fromAddressReference,
        toAddress: rateType.toAddressReference,
        rate: formatZoneRateWithVariation(service, rateType.rate),
        baseRate: rateType.rate,
        demurrageGrace: !demurrageGraceAsMinutes
          ? 'None'
          : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
        demurrageRate: `Apply: $${DisplayCurrencyValue(
          rateType.demurrage.rate,
        )}/hr - ${demurrageGraceAsMinutes} min grace`,
        appliedFuelSurcharge: appliedFuelSurcharge
          ? appliedFuelSurcharge.shortName
          : '-',
        percentage: `${rateType.percentage}%`,
        isClient: true,
        demurrageFuelSurchargeApplies: rateType.demurrage
          .demurrageFuelSurchargeApplies
          ? 'Apply'
          : "Don't Apply",
        rateTableItem: service,
      };
    });
  });
});

// Unit Rate Items and Headers
const unitHeaders: ComputedRef<TableHeader[]> = computed(() => [
  { text: 'Unit Type', align: 'left', value: 'unitType', sortable: false },
  { text: 'Rate', align: 'left', value: 'rate', sortable: false },
  { text: 'Zone', align: 'left', value: 'zone', sortable: false },
  {
    text: 'Per Amount',
    align: 'left',
    value: 'perAmount',
    sortable: false,
  },
  { text: 'Forklift', align: 'left', value: 'zone', sortable: false },
  { text: 'PU Flagfall', align: 'left', value: 'zone', sortable: false },
  { text: 'DO Flagfall', align: 'left', value: 'zone', sortable: false },
  { text: 'FL PU Flagfall', align: 'left', value: 'zone', sortable: false },
  { text: 'FL DO Flagfall', align: 'left', value: 'zone', sortable: false },
  { text: 'DG Flagfall', align: 'left', value: 'zone', sortable: false },
  // { text: 'DG Charge', align: 'left', value: 'zone', sortable: false },
]);
/**
 * Computed property for unit rate table data
 * @returns Array of unit rate table items
 */
const unitRateTableData = computed(() => {
  if (!serviceRate.value) {
    return [];
  }

  const unitRateItems = serviceRate.value.rateTableItems.filter(
    (item) => item.rateTypeId === JobRateType.UNIT,
  );
  if (!unitRateItems.length) {
    return [];
  }

  return unitRateItems.flatMap((service) => {
    const unitRates: UnitRate[] = service.rateTypeObject as UnitRate[];

    return unitRates.map((unit: UnitRate, index: number) => {
      const appliedFuelSurcharge = applicableFuelSurcharges.find(
        (x) => unit.appliedFuelSurchargeId === x.id,
      );

      // Get the variation percentage from the original service
      const variationPercentage = getRateVariationPercentage(service);

      const adjustedRates = unit.unitRanges.map((range) => {
        const adjustedRate = calculateAdjustedRate(
          range.unitRate,
          variationPercentage,
        );
        return `$${DisplayCurrencyValue(adjustedRate)}`;
      });

      function calculateVariation(rate: number) {
        const adjustedRate = calculateAdjustedRate(rate, variationPercentage);

        return `$${DisplayCurrencyValue(adjustedRate)}`;
      }

      const demurrageGraceAsMinutes = moment
        .duration(unit.demurrage.graceTimeInMilliseconds)
        .asMinutes();

      return {
        index,
        serviceTypeName: getServiceName(service.serviceTypeId),
        serviceTypeId: service.serviceTypeId ?? null,
        unitTypeName: unit.unitTypeName,
        zoneName: unit.zoneName,
        rate: adjustedRates.join(', '),
        puFlagFall: calculateVariation(unit.pickUpFlagFallHand),
        doFlagFall: calculateVariation(unit.dropOffFlagFallHand),
        flPuFlagFall: calculateVariation(unit.pickUpFlagFallForkLift),
        flDoFlagFall: calculateVariation(unit.dropOffFlagFallForkLift),
        dgFlagFall: calculateVariation(unit.dangerousGoodsFlagFall),
        forklift: unit.forkLiftRequired ? 'Yes' : 'NO',
        unitAmountMultiplier: unit.unitRanges
          .map((range) => range.unitAmountMultiplier)
          .join(', '),
        demurrageGrace: !demurrageGraceAsMinutes
          ? 'None'
          : moment.duration(demurrageGraceAsMinutes, 'minutes').humanize(),
        demurrageRate: `Apply: $${DisplayCurrencyValue(
          unit.demurrage.rate,
        )}/hr - ${demurrageGraceAsMinutes} min grace`,
        appliedFuelSurcharge: appliedFuelSurcharge
          ? appliedFuelSurcharge.shortName
          : '-',
        fleetPercentage: `${unit.fleetAssetPercentage}%`,
        isClient: true,
        demurrageFuelSurchargeApplies: unit.demurrage
          .demurrageFuelSurchargeApplies
          ? 'Apply'
          : "Don't Apply",
        rateTableItem: service,
        unitRateObject: unit,
      };
    });
  });
});

/**
 * Gets the rate variation percentage for a specific rate table item
 * @param item - The rate table item to check for variations
 * @returns The variation percentage or null if no variation applies
 */
function getRateVariationPercentage(item: RateTableItems): number | null {
  if (!rateVariations.value.length) {
    return null;
  }

  const matchingVariation = rateVariations.value.find((variation) => {
    const serviceTypeMatches =
      variation.serviceTypeId === null ||
      variation.serviceTypeId === item.serviceTypeId;

    const rateTypeMatches =
      variation.rateTypeId === null ||
      variation.rateTypeId === selectedRateTypeId.value;

    const now = Date.now();
    const validFrom = variation.validFromDate || 0;
    const validTo = variation.validToDate || Number.MAX_SAFE_INTEGER;
    const isActive = now >= validFrom && now <= validTo;

    return serviceTypeMatches && rateTypeMatches && isActive;
  });

  if (!matchingVariation) {
    return null;
  }

  const clientAdjustment = matchingVariation.clientAdjustmentPercentage;
  const fleetAssetAdjustment = matchingVariation.fleetAssetAdjustmentPercentage;
  return clientAdjustment !== null ? clientAdjustment : fleetAssetAdjustment;
}

/**
 * Calculates the adjusted rate based on base rate and variation percentage
 * @param baseRate - The base rate value
 * @param variationPercentage - The percentage variation to apply
 * @returns The adjusted rate value
 */
function calculateAdjustedRate(
  baseRate: number,
  variationPercentage: number | null,
): number {
  if (variationPercentage === null) {
    return baseRate;
  }
  return baseRate * (1 + variationPercentage / 100);
}

/**
 * Gets the color class for rate variation display
 * @param item - The rate table item to check for variations
 * @returns Color class name for styling
 */
function getRateVariationSymbol(item: RateTableItems): string {
  const variationPercentage = getRateVariationPercentage(item);

  if (variationPercentage === null || !props.showRateVariationsHighlight) {
    return '';
  }

  if (variationPercentage > 0) {
    return '*';
  } else {
    return '';
  }
}

/**
 * Gets the display name for a service type by ID
 * @param serviceTypeId - The service type ID to look up
 * @returns The service type display name or '-' if not found
 */
function getServiceName(serviceTypeId: number | null | undefined): string {
  if (!serviceTypeId) {
    return '-';
  }
  const serviceType = getServiceTypeById(serviceTypeId);
  return serviceType ? serviceType.optionSelectName : '-';
}

/**
 * Formats fuel surcharge display text
 * @param fuelSurchargeId - The fuel surcharge ID
 * @returns Formatted fuel surcharge text
 */
function formatFuelSurcharge(fuelSurchargeId: number): string {
  const fuelSurcharge = applicableFuelSurcharges.find(
    (f) => f.id === fuelSurchargeId,
  );
  return fuelSurcharge?.shortName || '-';
}

/**
 * Formats minimum charge with multiplier unit
 * @param rateObj - Time rate object
 * @returns Formatted minimum charge string
 */
function formatMinCharge(rateObj: TimeRateType): string {
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.minChargeMultiplier,
  );
  return `${rateObj.minCharge} ${multiplier?.longName || 'mins'}`;
}

/**
 * Formats charge increment with multiplier unit
 * @param rateObj - Time rate object
 * @returns Formatted charge increment string
 */
function formatChargeIncrement(rateObj: TimeRateType): string {
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.chargeIncrementMultiplier,
  );
  return `${rateObj.chargeIncrement} ${multiplier?.longName || 'mins'}`;
}

/**
 * Formats distance rate range display
 * @param rateObj - Distance rate object
 * @returns Formatted range string
 */
function formatRange(rateObj: DistanceRateType): string {
  if (rateObj.rates && rateObj.rates.length > 0) {
    const firstRange = rateObj.rates[0];
    const lastRange = rateObj.rates[rateObj.rates.length - 1];
    return `${firstRange.bracketMin}km - ${
      lastRange.bracketMax === -1 ? '∞' : lastRange.bracketMax + 'km'
    }`;
  }
  return '-';
}

/**
 * Formats time rate with variation applied and appropriate unit
 * @param item - The rate table item containing time rate data
 * @returns Formatted time rate string with currency and unit
 */
function formatTimeRateWithVariation(item: RateTableItems): string {
  const rateObj: TimeRateType = item.rateTypeObject as TimeRateType;
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.rateMultiplier,
  );
  const unit = multiplier?.shortName || 'hr';

  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(rateObj.rate, variationPercentage);

  return `$${DisplayCurrencyValue(adjustedRate)}/${unit}`;
}

/**
 * Formats standby rate with variation applied and appropriate unit
 * @param item - The rate table item containing time rate data
 * @returns Formatted standby rate string with currency and unit
 */
function formatStandbyRateWithVariation(item: RateTableItems): string {
  const rateObj: TimeRateType = item.rateTypeObject as TimeRateType;
  const multiplier = rateMultipliers.find(
    (m) => m.id === rateObj.standbyMultiplier,
  );
  const unit = multiplier?.shortName || 'hr';

  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(
    rateObj.standbyRate,
    variationPercentage,
  );

  return `$${DisplayCurrencyValue(adjustedRate)}/${unit}`;
}

/**
 * Formats zone rate with variation applied
 * @param item - The rate table item containing service information
 * @param baseRate - The base rate value to format
 * @returns Formatted zone rate string with currency per hour
 */
function formatZoneRateWithVariation(
  item: RateTableItems,
  baseRate: number,
): string {
  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(baseRate, variationPercentage);

  return `$${DisplayCurrencyValue(adjustedRate)}`;
}

/**
 * Formats distance rate base freight charge with variation applied
 * @param item - The rate table item containing distance rate data
 * @returns Formatted base freight charge with currency
 */
function formatBaseFreightWithVariation(item: RateTableItems): string {
  const rateObj: DistanceRateType = item.rateTypeObject as DistanceRateType;
  const baseFreight = rateObj.baseFreightCharge || 0;
  const variationPercentage = getRateVariationPercentage(item);
  const adjustedRate = calculateAdjustedRate(baseFreight, variationPercentage);

  return `$${DisplayCurrencyValue(adjustedRate)}`;
}

/**
 * Gets consolidated tooltip text for all rate types showing variation calculations
 * @param item - The table item (can be any rate table item type)
 * @param rateType - The type of rate to format tooltip for
 * @returns Tooltip text showing base rate and variation calculation
 * @throws Will return error message if rate type is unsupported
 */
function getRateTooltip(
  item: any,
  rateType: 'time' | 'standby' | 'zone' | 'pointToPoint' | 'distance' | 'unit',
): string {
  try {
    const serviceItem = item.rateTableItem || item;
    const variationPercentage = getRateVariationPercentage(serviceItem);
    const sign =
      variationPercentage !== null && variationPercentage >= 0 ? '+' : '';

    switch (rateType) {
      case 'time': {
        const rateObj: TimeRateType =
          serviceItem.rateTypeObject as TimeRateType;
        const multiplier = rateMultipliers.find(
          (m) => m.id === rateObj.rateMultiplier,
        );
        const unit = multiplier?.shortName || 'hr';

        if (variationPercentage === null) {
          return `Base rate: $${DisplayCurrencyValue(rateObj.rate)}/${unit}`;
        }

        const adjustedRate = calculateAdjustedRate(
          rateObj.rate,
          variationPercentage,
        );
        return `$${DisplayCurrencyValue(
          rateObj.rate,
        )}/${unit} ${sign}${variationPercentage.toFixed(
          2,
        )}% = $${DisplayCurrencyValue(adjustedRate)}/${unit}`;
      }

      case 'standby': {
        const rateObj: TimeRateType =
          serviceItem.rateTypeObject as TimeRateType;
        const multiplier = rateMultipliers.find(
          (m) => m.id === rateObj.standbyMultiplier,
        );
        const unit = multiplier?.shortName || 'hr';

        if (variationPercentage === null) {
          return `Base standby rate: $${DisplayCurrencyValue(
            rateObj.standbyRate,
          )}/${unit}`;
        }

        const adjustedRate = calculateAdjustedRate(
          rateObj.standbyRate,
          variationPercentage,
        );
        return `$${DisplayCurrencyValue(
          rateObj.standbyRate,
        )}/${unit} ${sign}${variationPercentage.toFixed(
          2,
        )}% = $${DisplayCurrencyValue(adjustedRate)}/${unit}`;
      }

      case 'zone': {
        const baseRate = item.baseZoneRate;

        if (variationPercentage === null) {
          return `Base zone rate: $${DisplayCurrencyValue(baseRate)}/hr`;
        }

        const adjustedRate = calculateAdjustedRate(
          baseRate,
          variationPercentage,
        );
        return `$${DisplayCurrencyValue(
          baseRate,
        )}/hr ${sign}${variationPercentage.toFixed(
          2,
        )}% = $${DisplayCurrencyValue(adjustedRate)}/hr`;
      }

      case 'pointToPoint': {
        const baseRate = item.baseRate;

        if (variationPercentage === null) {
          return `Base rate: $${DisplayCurrencyValue(baseRate)}/hr`;
        }

        const adjustedRate = calculateAdjustedRate(
          baseRate,
          variationPercentage,
        );
        return `$${DisplayCurrencyValue(
          baseRate,
        )}/hr ${sign}${variationPercentage.toFixed(
          2,
        )}% = $${DisplayCurrencyValue(adjustedRate)}/hr`;
      }

      case 'distance': {
        const rateObj: DistanceRateType =
          serviceItem.rateTypeObject as DistanceRateType;
        const baseFreight = rateObj.baseFreightCharge || 0;

        if (variationPercentage === null) {
          return `Base freight charge: $${DisplayCurrencyValue(baseFreight)}`;
        }

        const adjustedRate = calculateAdjustedRate(
          baseFreight,
          variationPercentage,
        );
        return `$${DisplayCurrencyValue(
          baseFreight,
        )} ${sign}${variationPercentage.toFixed(2)}% = $${DisplayCurrencyValue(
          adjustedRate,
        )}`;
      }

      case 'unit': {
        // For unit rates, we need to access the original service data
        const rateTableItem = item.rateTableItem;
        const unitRateObject: UnitRate = item.unitRateObject;

        if (!rateTableItem || !unitRateObject) {
          return 'Unit rate information not available';
        }

        // Get the variation percentage from the original service
        const serviceVariationPercentage =
          getRateVariationPercentage(rateTableItem);

        if (serviceVariationPercentage === null) {
          return `Base unit rates: ${unitRateObject.unitRanges
            .map((range) => `$${DisplayCurrencyValue(range.unitRate)}`)
            .join(', ')}`;
        }

        const adjustedRates = unitRateObject.unitRanges.map((range) => {
          const adjustedRate = calculateAdjustedRate(
            range.unitRate,
            serviceVariationPercentage,
          );
          return `$${DisplayCurrencyValue(
            range.unitRate,
          )} ${sign}${serviceVariationPercentage.toFixed(
            2,
          )}% = $${DisplayCurrencyValue(adjustedRate)}`;
        });

        return adjustedRates.join(', ');
      }

      default:
        return 'Unsupported rate type';
    }
  } catch (error) {
    console.error('Error generating rate tooltip:', error);
    return 'Error loading rate information';
  }
}

/**
 * Component mounted lifecycle hook
 * Loads service rates and rate variations, then auto-selects first available rate type
 */
onMounted(async () => {
  try {
    await Promise.all([loadServiceRate(), loadRateVariations()]);

    // Auto-select the first available rate type if the current selection is not available
    if (availableRateTypes.value.length > 0) {
      const currentRateTypeAvailable = availableRateTypes.value.some(
        (rateType) => rateType.rateTypeId === selectedRateTypeId.value,
      );

      if (!currentRateTypeAvailable) {
        selectedRateTypeId.value = availableRateTypes.value[0].rateTypeId;
      }
    }
  } catch (error) {
    console.error('Error during component initialization:', error);
  }
});

/**
 * Watcher for clientId prop changes
 * Reloads service rates and variations when client changes
 */
watch(
  () => props.clientId,
  async (newClientId: string) => {
    if (newClientId) {
      try {
        await Promise.all([loadServiceRate(), loadRateVariations()]);
      } catch (error) {
        console.error('Error reloading data for new client:', error);
      }
    }
  },
);

// /**
//  * Watcher for searchDate prop changes
//  * Reloads service rates when search date changes
//  */
// watch(
//   () => props.searchDate,
//   async (newSearchDate: number | undefined) => {
//     if (newSearchDate && props.clientId) {
//       try {
//         await loadServiceRate();
//       } catch (error) {
//         console.error('Error reloading data for new search date:', error);
//       }
//     }
//   },
// );
</script>

<style scoped lang="scss">
.service-rate-table-read-only {
  .rate-text {
    font-weight: bold;
    cursor: pointer;

    &.neutral {
      color: var(--text-color);
    }

    &.positive {
      color: $success;
    }
    &.negative {
      color: $error;
    }
  }
}

.positive {
  border-color: $success;
}
.negative {
  border-color: $error;
}

// Loading state styling
.text-center {
  .text-muted {
    color: #6c757d;
    font-size: 14px;
  }
}

.btn-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  flex-direction: row-reverse;
}

// Grouped table styling
.service-header-row {
  background-color: rgba(255, 255, 255, 0.05) !important;
  font-weight: bold;

  .service-header-text {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-color);
  }
}

.zone-child-row {
  .zone-child-text {
    padding-left: 20px;
    font-size: 13px;
    color: var(--text-color-secondary);
  }
}

.distance-child-row {
  .distance-child-text {
    padding-left: 20px;
    font-size: 13px;
    color: var(--text-color-secondary);
  }
}

.missing-rate {
  color: #ff9800 !important;
  font-style: italic;
}

.missing-variation {
  color: #9e9e9e !important;
  font-style: italic;
  font-size: 12px;
}

.rate-variation-text {
  font-weight: 500;
  color: var(--success-color);
}
</style>
