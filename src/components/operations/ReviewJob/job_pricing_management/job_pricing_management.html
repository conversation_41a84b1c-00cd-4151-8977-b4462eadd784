<section class="job-pricing-management">
  <v-flex md12 v-if="hasErrors" px-4>
    <v-alert :value="hasErrors && hasErrors.length > 0" type="warning">
      <v-flex md12>
        <v-layout pb-2>
          <span style="font-weight: 600">
            The following issue(s) must be resolved before this job may be
            priced:
          </span>
        </v-layout>
        <v-layout pt-1 v-for="(error, index) in hasErrors" :key="error">
          <span class="pl-3 pr-2">{{index + 1}}.</span> {{error}}
        </v-layout>
      </v-flex>
    </v-alert>
  </v-flex>
  <v-alert type="warning" class="mb-3" :value="isOutsideHire && !readOnlyView"
    >This job is allocated to an <b>OUTSIDE HIRE</b>.
    <ul>
      <li>
        Please ensure the below pricing matches the quoted Outside Hire rates.
      </li>
      <li>Some estimated times may not be accurate.</li>
    </ul>
  </v-alert>

  <v-alert
    type="warning"
    class="mb-3"
    :value="showEDIConfirmation && jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED"
    >This Client has a requirement for <b>Electronic Data Interchange</b>, and
    it is important to confirm that the leg matching process is complete before
    proceeding with the review.
  </v-alert>

  <v-flex md12 v-if="!hasErrors">
    <v-layout
      row
      v-if="!dataHasLoaded"
      justify-center
      align-center
      style="height: calc(85vh - 74px)"
    >
      <img
        src="@/static/loader/infinity-loader-light.svg"
        height="80px"
        width="80px"
      />
    </v-layout>

    <v-layout
      v-if="((readOnlyView) || (dataHasLoaded && jobRateData && jobDetails)) && !isOutsideHire"
      pb-2
      row
      wrap
      class="header-section"
    >
      <v-flex nd12>
        <v-layout>
          <v-flex md10 class="app-bordercolor--600 app-borderside--r px-3 pb-1">
            <table class="simple-data-table">
              <thead>
                <tr>
                  <th class="column-header">Stop</th>
                  <th class="column-header">Arrival/Departures</th>
                  <th class="column-header pr-4">Travel</th>
                  <th class="column-header">Load</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(row, index) in summaryTableData"
                  :key="row.id"
                  class="eventtime-card"
                  v-if="(viewingExpandedEventInfo && !row.isOverallRow) || (!viewingExpandedEventInfo && row.isOverallRow)"
                >
                  <template v-if="row.rowType === 'PUD'">
                    <td class="eventtime-card__header eventtime-card__value">
                      {{ row.title }}
                    </td>
                    <td class="eventtime-card__value">
                      <span>
                        <span
                          class="eventtime-card__value"
                          :class="row.isFirst ? 'highlighted' : ''"
                          >{{ row.arrival }}</span
                        >
                        -
                        <span
                          class="eventtime-card__value"
                          :class="row.isLast ? 'highlighted' : ''"
                          >{{ row.departure }}</span
                        >
                      </span>
                    </td>
                    <td class="eventtime-card__value">
                      <template
                        v-if="jobLegsInCorrectOrder || row.isOverallRow"
                      >
                        <span
                          style="position: relative"
                          :class="{'show-asterisk-marker-left': !jobLegsInCorrectOrder}"
                        >
                          <span v-if="row.actualTravel"
                            >{{ row.actualTravel }}</span
                          >
                          <span v-else-if="row.estimatedTravel"
                            >{{ row.estimatedTravel }}</span
                          >
                        </span>
                        <span
                          v-if="row.travelDifferencePercent"
                          :class="['eventtime-card__diff', { 'eventtime-card__diff--error': row.travelIsError }]"
                        >
                          (est. {{row.estimatedTravel}}, {{
                          row.travelDifferencePercent }})
                        </span>
                      </template>
                      <template v-else>
                        <span class="asterisk-message">N/A</span>
                      </template>
                    </td>
                    <td class="eventtime-card__value">
                      <span>
                        <span v-if="row.actualLoad">{{ row.actualLoad }}</span>
                        <span v-else-if="row.estimatedLoad"
                          >{{ row.estimatedLoad }}</span
                        >
                      </span>
                      <span
                        v-if="row.loadDifferenceTime && row.actualLoad"
                        :class="['eventtime-card__diff', { 'eventtime-card__diff--error': row.loadIsError }]"
                      >
                        (est. {{row.estimatedLoad}}, {{ row.loadDifferenceTime
                        }})
                      </span>
                    </td>
                  </template>
                  <template
                    v-if="row.rowType === 'PRE_FIRST_LEG' || row.rowType === 'POST_LAST_LEG'"
                  >
                    <td class="eventtime-card__header eventtime-card__value">
                      <span v-if="row.isOverallRow">{{ row.title }}</span>
                    </td>
                    <td class="eventtime-card__value">
                      <span v-if="!row.isOverallRow">{{ row.title }}</span>
                    </td>
                    <td class="eventtime-card__value">{{row.actualTravel}}</td>
                    <td class="eventtime-card__value"></td>
                  </template>
                </tr>
              </tbody>
            </table>
          </v-flex>
          <v-flex class="px-3" v-if="rateSearchEpoch">
            <v-layout>
              <span class="column-header">Rates Applied</span>
              <v-menu left style="z-index: 1000">
                <template v-slot:activator="{ on }">
                  <v-icon size="16" class="pl-3 pr-1" v-on="on"
                    >fas fa-ellipsis-v
                  </v-icon>
                </template>
                <v-list class="v-list-custom" dense>
                  <ConfirmationDialog
                    :buttonText="jobHasBeenPreviouslyReviewed ? 'Refresh Rates (Restore to Defaults)' : 'Clear Changes'"
                    message="You are about to restore the data below to their default values. All custom values will be replaced, including START and FINISH times, BREAK durations and Custom Charges."
                    title="Refresh Rates (Restore to Defaults)"
                    @confirm="setDefaultsFromJob(); refreshRatesToDefault();"
                    :buttonDisabled="false"
                    :isOutlineButton="true"
                    :buttonColor="'teal accent-2'"
                    confirmationButtonText="Confirm"
                    :isCheckbox="true"
                    :isListTile="true"
                    :listTileAccent="false"
                    checkboxLabel="Do you wish to proceed?"
                    :dialogIsActive="true"
                  />
                  <v-list-tile dense @click="isViewingDateSelectDialog = true">
                    <v-list-tile-title>
                      Use Rates for Custom Date
                    </v-list-tile-title>
                  </v-list-tile>
                </v-list>
              </v-menu>
            </v-layout>
            <v-layout class="eventtime-card">
              <span class="eventtime-card__value highlighted">
                {{returnFormattedDate(rateSearchEpoch)}}
              </span>
            </v-layout>
          </v-flex>
        </v-layout>
      </v-flex>
      <v-flex md12 px-2 class="header-handle">
        <v-layout justify-center>
          <span v-if="!jobLegsInCorrectOrder" class="asterisk-message">
            Driver completed stops out of order. Travel times between stops may
            be impacted as a result
          </span>
          <span
            v-if="!viewingExpandedEventInfo"
            class="px-2 pt-1 pb-0 minimize-button"
            @click="viewingExpandedEventInfo = true"
          >
            <v-icon size="14">far fa-chevron-down</v-icon>
          </span>
          <span
            v-else="viewingExpandedEventInfo"
            class="px-2 pt-1 pb-0 minimize-button"
            @click="viewingExpandedEventInfo = false"
          >
            <v-icon size="14">far fa-chevron-up</v-icon>
          </span>
        </v-layout>
      </v-flex>
      <v-flex md12>
        <v-divider class="mb-1 mt-0"></v-divider>
      </v-flex>
    </v-layout>
    <v-layout row wrap v-if="dataHasLoaded && jobRateData">
      <v-flex md12>
        <v-layout v-if="showDriverRates" pr-2>
          <span class="pricing-section__header">CLIENT RATES</span>
          <span v-if="clientServiceRate && clientServiceRate._id">
            <span
              v-if="clientServiceRate.name"
              class="pricing-section__header--tablename"
            >
              {{clientServiceRate.name}}</span
            >
            <span class="pricing-section__header--tablename">
              (Valid {{ clientServiceRate.validFromDate ?
              returnFormattedDate(clientServiceRate.validFromDate) : 'Unknown'
              }} - {{ clientServiceRate.validToDate ?
              returnFormattedDate(clientServiceRate.validToDate) : 'Unknown'
              }})</span
            >
          </span>
          <span
            v-if="jobHasBeenPreviouslyReviewed"
            :style="{'padding-right': jobAccountingDetails.clientRates[0].rate.rateTypeId === 1 ? '20px': '0px'}"
          >
            <v-tooltip right>
              <template v-slot:activator="{ on }">
                <v-icon size="20" v-on="on" color="amber" class="ml-2"
                  >fad fa-exclamation-triangle</v-icon
                >
              </template>
              <span>This job has been previously priced.<br /></span>
            </v-tooltip>
          </span>
          <v-spacer></v-spacer>
        </v-layout>
        <v-layout>
          <v-flex md2 px-2 pt-1>
            <v-layout style="font-size: 12px">
              <h6 class="accent-text--primary mr-1">
                {{ jobDetails.rateTypeName}} -
              </h6>
              <h6 class="accent-text--primary mr-1">
                {{jobDetails.serviceTypeShortName}}
              </h6>
              <RateVariationTooltip
                v-if="(!readOnlyView && jobAccountingDetails.clientServiceRateVariations) || (readOnlyView && jobDetails.accounting.clientServiceRateVariations)"
                type="CLIENT"
                :rateVariation="readOnlyView ? jobDetails.accounting.clientServiceRateVariations : jobAccountingDetails.clientServiceRateVariations"
                :rateTypeId="jobDetails.rateTypeId"
                :applyRateVariationsToDemurrage="
                    companyDetailsStore.divisionCustomConfig?.accounting
                      ?.applyRateVariationsToDemurrage
                  "
              ></RateVariationTooltip>
            </v-layout>
          </v-flex>
          <v-flex md10 class="px-1">
            <ZoneManagement
              v-if="jobRateData.client.zone"
              :pudItems="jobDetails.pudItems"
              :jobDetails="jobDetails"
              :zoneRate="jobAccountingDetails.clientRates[0].rate"
              :accounting="jobAccountingDetails"
              @refreshAccountingTotals="refreshAccountingTotals"
              :readOnlyView="readOnlyView"
            />
            <UnitManagement
              v-if="jobRateData.client.unitRate"
              :pudItems="jobDetails.pudItems"
              height="calc(50vh - 160px)"
              :rates="jobAccountingDetails.clientRates"
              :accounting="jobAccountingDetails"
              type="CLIENT"
              @refreshAccountingTotals="refreshAccountingTotals"
              :readOnlyView="readOnlyView"
            />
            <DistanceManagement
              v-if="jobRateData.client.distance"
              height="calc(50vh - 160px)"
              type="CLIENT"
              :jobDetails="jobDetails"
              :accounting="jobAccountingDetails"
              :readOnlyView="readOnlyView"
              @refreshAccountingTotals="refreshAccountingTotals"
            />
            <PointToPointManagement
              v-if="jobRateData.client.p2p"
              :accounting="jobAccountingDetails"
              :clientNickNameAddresses="clientCommonAddressList"
              :rates="jobAccountingDetails.clientRates"
              title="Client"
              :isClient="true"
              @refreshAccountingTotals="refreshAccountingTotals"
              :pudItems="jobDetails.pudItems"
              :readOnlyView="readOnlyView"
            />

            <TripRateManagement
              v-if="jobRateData.client.tripRate && allTotalsComputed"
              :title="'Client (Quoted Rate)'"
              :jobDetails="jobDetails"
              :tripRate="jobAccountingDetails.clientRates[0].rate.rateTypeObject"
              height="calc(50vh - 160px)"
            >
            </TripRateManagement>
            <ZoneToZoneManagement
              v-if="jobRateData.client.zoneToZone && allTotalsComputed"
              height="calc(50vh - 160px)"
              type="CLIENT"
              :jobDetails="jobDetails"
              :accounting="jobAccountingDetails"
              :readOnlyView="readOnlyView"
              @refreshAccountingTotals="refreshAccountingTotals"
            />

            <RouteTimeManagement
              v-if="!readOnlyView && allTotalsComputed && jobRateData.client.time"
              ref="clientRouteTimeManagement"
              :standbyCharge="jobAccountingDetails.totals.subtotals.standbyChargeTotals.client"
              :legDurations="jobDetails.legDurations"
              :freightCharges="jobAccountingDetails.totals.subtotals.freightCharges"
              :rateDetails="jobAccountingDetails.clientRates[0]"
              type="CLIENT"
              :jobEventList="jobDetails.eventList"
              :standbyDurations="jobAccountingDetails.clientRates[0].standbyDuration"
              :durationData="jobAccountingDetails.finishedJobData.clientDurations"
              :pudItems="jobDetails.pudItems"
              :isEquipmentHire="isEquipmentHire"
              :enableTimesCopy="enableCopyForTimeRate"
              :workDiaryList="jobAccountingDetails.clientRates[0].breakDuration && jobAccountingDetails.clientRates[0].breakDuration.breakSummaryList ? jobAccountingDetails.clientRates[0].breakDuration.breakSummaryList : []"
              :rateVariation="jobAccountingDetails.clientServiceRateVariations"
              @setClientMinimumDurationMet="setClientMinimumDurationMet"
              @refreshAccountingTotals="refreshAccountingTotals"
              @copyTimeRateDataFromOther="copyTimeRateDataFromOther"
              @setViewingBreakDurationDialog="setViewingBreakDurationDialog"
            >
            </RouteTimeManagement>
            <RouteTimeManagement
              v-if="readOnlyView && allTotalsComputed && jobRateData.client.time"
              :readOnlyStartTime="jobDetails.accounting.finishedJobData.clientDurations.startTime"
              :readOnlyEndTime="jobDetails.accounting.finishedJobData.clientDurations.endTime"
              :freightCharges="jobDetails.accounting.totals.subtotals.freightCharges"
              :readOnly="true"
              :standbyDurations="jobAccountingDetails.clientRates[0].standbyDuration"
              :jobEventList="jobDetails.eventList"
              :legDurations="jobDetails.legDurations"
              :rateDetails="jobDetails.accounting.clientRates[0]"
              :durationData="jobDetails.accounting.finishedJobData.clientDurations"
              :pudItems="jobDetails.pudItems"
              :standbyCharge="jobDetails.accounting.totals.subtotals.standbyChargeTotals.client"
              :rateVariation="jobDetails.accounting.clientServiceRateVariations"
              type="CLIENT"
            >
            </RouteTimeManagement>
          </v-flex>
        </v-layout>
        <v-layout>
          <v-flex md5 offset-md7>
            <CurrentAppliedCharges
              v-if="!readOnlyView"
              :additionalCharges="jobAccountingDetails.additionalCharges"
              :isOutsideHire="isOutsideHire"
              :jobAccountingDetails="jobAccountingDetails"
              :newJobNotes="newJobNotes"
              :readOnly="false"
              :entityType="'CLIENT'"
              :forceOutsideMetroChargeClient="forceOutsideMetroChargeClient"
              :forceOutsideMetroChargeFleetAsset="forceOutsideMetroChargeFleetAsset"
              :allFuelSurcharges="clientFuelSurcharges"
              :jobRangeData="jobDetails.fuelRangeDeterminantValues"
              @addNewAdditionalCharge="addChargeDialogIsOpen = true"
              @updateAdditionalCharge="updateExistingAdditionalCharge"
              @updateForceOutsideMetroChargeClient="updateForceOutsideMetroChargeClient"
              @updateForceOutsideMetroChargeFleetAsset="updateForceOutsideMetroChargeFleetAsset"
              @setViewingBreakDurationDialog="setViewingBreakDurationDialog"
              key="client"
            >
            </CurrentAppliedCharges>

            <CurrentAppliedCharges
              v-if="readOnlyView"
              :additionalCharges="jobDetails.accounting.additionalCharges"
              :readOnly="true"
              :jobAccountingDetails="jobDetails.accounting"
              :forceOutsideMetroChargeClient="forceOutsideMetroChargeClient"
              :forceOutsideMetroChargeFleetAsset="forceOutsideMetroChargeFleetAsset"
              :entityType="'CLIENT'"
              :jobRangeData="jobDetails.fuelRangeDeterminantValues"
              @updateForceOutsideMetroChargeClient="updateForceOutsideMetroChargeClient"
              @updateForceOutsideMetroChargeFleetAsset="updateForceOutsideMetroChargeFleetAsset"
              @setViewingBreakDurationDialog="setViewingBreakDurationDialog"
            >
            </CurrentAppliedCharges>
          </v-flex>
        </v-layout>
        <v-layout v-if="showDriverRates">
          <v-divider class="my-3 mx-1"></v-divider>
        </v-layout>
        <!-- ================================================================= -->
        <!-- Driver Rates and Additional Charges -->
        <!-- ================================================================= -->
        <v-layout v-if="showDriverRates">
          <span class="pricing-section__header">FLEET ASSET RATES</span>
          <span v-if="fleetAssetServiceRate && fleetAssetServiceRate._id">
            <span
              v-if="fleetAssetServiceRate.name"
              class="pricing-section__header--tablename"
            >
              {{fleetAssetServiceRate.name}}</span
            >
            <span class="pricing-section__header--tablename">
              (Valid {{ fleetAssetServiceRate.validFromDate ?
              returnFormattedDate(fleetAssetServiceRate.validFromDate) :
              'Unknown' }} - {{ fleetAssetServiceRate.validToDate ?
              returnFormattedDate(fleetAssetServiceRate.validToDate) : 'Unknown'
              }})</span
            >
          </span>
        </v-layout>
        <v-layout v-if="showDriverRates">
          <v-flex md2 px-2 pt-1>
            <v-layout pb-2 style="font-size: 12px">
              <h6 class="accent-text--primary mr-1">
                {{ jobDetails.fleetAssetRateTypeName }} -
              </h6>
              <h6 class="accent-text--primary mr-1">
                {{jobDetails.fleetAssetServiceTypeShortName}}
              </h6>
              <RateVariationTooltip
                v-if="(!readOnlyView && jobAccountingDetails.clientServiceRateVariations) || (readOnlyView && jobDetails.accounting.clientServiceRateVariations)"
                type="FLEET_ASSET"
                :rateVariation="readOnlyView ? jobDetails.accounting.clientServiceRateVariations : jobAccountingDetails.clientServiceRateVariations"
                :rateTypeId="jobDetails.fleetAssetRateTypeId"
                :applyRateVariationsToDemurrage="
                    companyDetailsStore.divisionCustomConfig?.accounting
                      ?.applyRateVariationsToDemurrage
                  "
              ></RateVariationTooltip>
            </v-layout>

            <!-- <v-layout
              class="app-borderside--a app-bordercolor--600"
              v-if="fleetAssetHasValidPercentageTypeRate && !readOnlyView"
            >
              <v-select
                class="v-solo-custom"
                solo
                flat
                label="Select Rate Type"
                hide-details
                :items="switchRateTypeSelectOptions"
                item-text="longName"
                item-value="id"
                color="light-blue"
                v-model="currentActiveFleetAssetRate"
              >
              </v-select>
            </v-layout> -->
          </v-flex>
          <v-flex class="px-1" md10>
            <ZoneManagement
              v-if="!isOutsideHire && jobRateData.fleetAsset.zone"
              :jobDetails="jobDetails"
              :zoneRate="jobAccountingDetails.fleetAssetRates[0].rate"
              :accounting="jobAccountingDetails"
              :isClient="false"
              :readOnlyView="readOnlyView"
              :driverRegisteredForGst="fleetAssetOwner.gstRegistered"
              :clientZoneRate="jobAccountingDetails.clientRates[0].rate"
              :pudItems="jobDetails.pudItems"
              @refreshAccountingTotals="refreshAccountingTotals"
            />
            <DistanceManagement
              v-if="jobRateData.fleetAsset.distance"
              height="calc(50vh - 160px)"
              type="FLEET_ASSET"
              :jobDetails="jobDetails"
              :accounting="jobAccountingDetails"
              :readOnlyView="readOnlyView"
              @refreshAccountingTotals="refreshAccountingTotals"
            />
            <UnitManagement
              :pudItems="jobDetails.pudItems"
              :rates="jobAccountingDetails.fleetAssetRates"
              :accounting="jobAccountingDetails"
              type="FLEET_ASSET"
              :readOnlyView="readOnlyView"
              @refreshAccountingTotals="refreshAccountingTotals"
              :driverRegisteredForGst="fleetAssetOwner.gstRegistered"
              v-if="!isOutsideHire && jobRateData.fleetAsset.unitRate"
            />
            <PointToPointManagement
              v-if="!isOutsideHire && jobRateData.fleetAsset.p2p"
              :isClient="false"
              :clientPointToPointRate="jobAccountingDetails.clientRates[0].rate.rateTypeObject"
              :accounting="jobAccountingDetails"
              :clientNickNameAddresses="clientCommonAddressList"
              :title="'Fleet Asset'"
              :readOnlyView="readOnlyView"
              :rates="jobAccountingDetails.fleetAssetRates"
              @changeToTimeRateType="changeFleetAssetRateToTime"
              @refreshAccountingTotals="refreshAccountingTotals"
              :pudItems="jobDetails.pudItems"
            />
            <TripRateManagement
              v-if="allTotalsComputed && jobRateData.fleetAsset.tripRate"
              :tripRate="jobAccountingDetails.fleetAssetRates[0].rate.rateTypeObject"
              :jobDetails="jobDetails"
              :accountingDetails="jobAccountingDetails"
              :isOutsideHire="isOutsideHire"
              :outsideHireFuelSurcharge="isOutsideHire ? jobAccountingDetails.fleetAssetRates[0].rate.fuelSurcharge : null"
              @outsideHireFuelSurchargeChanged="outsideHireFuelSurchargeChanged"
            >
            </TripRateManagement>
            <ZoneToZoneManagement
              v-if="jobRateData.fleetAsset.zoneToZone && allTotalsComputed"
              height="calc(50vh - 160px)"
              type="FLEET_ASSET"
              :jobDetails="jobDetails"
              :accounting="jobAccountingDetails"
              :readOnlyView="readOnlyView"
              @refreshAccountingTotals="refreshAccountingTotals"
            />
            <RouteTimeManagement
              v-if="!readOnlyView && allTotalsComputed && jobRateData.fleetAsset.time"
              ref="fleetAssetRouteTimeManagement"
              type="FLEET_ASSET"
              :freightCharges="jobAccountingDetails.totals.subtotals.freightCharges"
              :pudItems="jobDetails.pudItems"
              :standbyCharge="jobAccountingDetails.totals.subtotals.standbyChargeTotals.fleetAsset"
              :standbyDurations="jobAccountingDetails.fleetAssetRates[0].standbyDuration"
              :legDurations="jobDetails.legDurations"
              :rateDetails="jobAccountingDetails.fleetAssetRates[0]"
              :jobEventList="jobDetails.eventList"
              :durationData="jobAccountingDetails.finishedJobData.fleetAssetDurations"
              :isEquipmentHire="isEquipmentHire"
              :enableTimesCopy="enableCopyForTimeRate"
              :workDiaryList="jobAccountingDetails.fleetAssetRates[0].breakDuration && jobAccountingDetails.fleetAssetRates[0].breakDuration.breakSummaryList ? jobAccountingDetails.fleetAssetRates[0].breakDuration.breakSummaryList : []"
              :rateVariation="jobAccountingDetails.clientServiceRateVariations"
              @setDriverMinimumDurationMet="setDriverMinimumDurationMet"
              @refreshAccountingTotals="refreshAccountingTotals"
              @copyTimeRateDataFromOther="copyTimeRateDataFromOther"
              @setViewingBreakDurationDialog="setViewingBreakDurationDialog"
            >
            </RouteTimeManagement>
            <RouteTimeManagement
              v-if="readOnlyView && allTotalsComputed && jobRateData.fleetAsset.time"
              type="FLEET_ASSET"
              :readOnlyStartTime="jobDetails.accounting.finishedJobData.fleetAssetDurations.startTime"
              :readOnlyEndTime="jobDetails.accounting.finishedJobData.fleetAssetDurations.endTime"
              :readOnly="true"
              :freightCharges="jobDetails.accounting.totals.subtotals.freightCharges"
              :standbyDurations="jobAccountingDetails.fleetAssetRates[0].standbyDuration"
              :jobEventList="jobDetails.eventList"
              :legDurations="jobDetails.legDurations"
              :rateDetails="jobDetails.accounting.fleetAssetRates[0]"
              :pudItems="jobDetails.pudItems"
              :standbyCharge="jobDetails.accounting.totals.subtotals.standbyChargeTotals.fleetAsset"
              :durationData="jobDetails.accounting.finishedJobData.fleetAssetDurations"
              :rateVariation="jobDetails.accounting.clientServiceRateVariations"
              @setViewingBreakDurationDialog="setViewingBreakDurationDialog"
            >
            </RouteTimeManagement>
          </v-flex>
        </v-layout>
        <v-layout v-if="showDriverRates">
          <v-flex md5 offset-md7>
            <CurrentAppliedCharges
              v-if="!readOnlyView"
              key="driver"
              :additionalCharges="jobAccountingDetails.additionalCharges"
              height="calc(50vh - 160px)"
              :isOutsideHire="isOutsideHire"
              :jobAccountingDetails="jobAccountingDetails"
              :newJobNotes="newJobNotes"
              :entityType="'FLEET_ASSET'"
              :readOnly="false"
              :forceOutsideMetroChargeClient="forceOutsideMetroChargeClient"
              :forceOutsideMetroChargeFleetAsset="forceOutsideMetroChargeFleetAsset"
              :allFuelSurcharges="fleetAssetFuelSurcharges"
              :jobRangeData="jobDetails.fuelRangeDeterminantValues"
              @addNewAdditionalCharge="addChargeDialogIsOpen = true"
              @updateAdditionalCharge="updateExistingAdditionalCharge"
              @updateForceOutsideMetroChargeClient="updateForceOutsideMetroChargeClient"
              @updateForceOutsideMetroChargeFleetAsset="updateForceOutsideMetroChargeFleetAsset"
              @setViewingBreakDurationDialog="setViewingBreakDurationDialog"
            >
            </CurrentAppliedCharges>
            <CurrentAppliedCharges
              v-if="readOnlyView"
              :readOnly="true"
              :additionalCharges="jobDetails.accounting.additionalCharges"
              :entityType="'FLEET_ASSET'"
              :jobAccountingDetails="jobDetails.accounting"
              :forceOutsideMetroChargeClient="forceOutsideMetroChargeClient"
              :forceOutsideMetroChargeFleetAsset="forceOutsideMetroChargeFleetAsset"
              :jobRangeData="jobDetails.fuelRangeDeterminantValues"
              @updateForceOutsideMetroChargeClient="updateForceOutsideMetroChargeClient"
              @updateForceOutsideMetroChargeFleetAsset="updateForceOutsideMetroChargeFleetAsset"
              @setViewingBreakDurationDialog="setViewingBreakDurationDialog"
            >
            </CurrentAppliedCharges>
          </v-flex>
        </v-layout>
        <!-- ================================================================= -->
        <!-- Equipment Hire -->
        <!-- ================================================================= -->
        <v-layout
          v-if="isEquipmentHire && allTotalsComputed && showDriverRates"
        >
          <v-divider class="my-3 mx-1"></v-divider>
        </v-layout>
        <v-layout
          v-if="isEquipmentHire && allTotalsComputed && showDriverRates"
          align-center
        >
          <span class="pricing-section__header pr-2">Equipment Hire</span>

          <v-tooltip bottom v-if="!allHireContractsValid">
            <template v-slot:activator="{ on }">
              <v-icon
                size="13"
                class="mr-1"
                v-on="on"
                @click="viewAssetManagement"
                color="error"
              >
                fas fa-exclamation-triangle
              </v-icon>
            </template>
            <span
              >One or more contracts is not active. Click to go to asset
              management</span
            >
          </v-tooltip>
        </v-layout>
        <v-layout
          v-if="isEquipmentHire && allTotalsComputed && showDriverRates"
        >
          <v-flex md2 pa-2>
            <h6 class="subheader--faded">Hire Charges</h6></v-flex
          >
          <v-flex md5></v-flex>
          <v-flex md5>
            <EquipmentHirePricingSummary
              v-if="isEquipmentHire"
              height="calc(50vh - 160px)"
              :equipmentHireCost="jobAccountingDetails.totals.subtotals.equipmentHireTotal"
              :readOnly="false"
              :additionalAssets="jobDetails.additionalAssets"
              :hireContracts="hireContracts"
              :jobDate="jobDetails.workDate"
            ></EquipmentHirePricingSummary>
          </v-flex>
        </v-layout>
      </v-flex>
    </v-layout>
    <AddAdditionalCharge
      v-if="showDriverRates && jobRateData"
      :isDialogOpen.sync="addChargeDialogIsOpen"
      :chargeItems="additionalChargeItems"
      @addChargesToJob="addAdditionalChargesToJob"
      :currentAppliedCharges="jobAccountingDetails.additionalCharges.chargeList ? jobAccountingDetails.additionalCharges.chargeList : []"
      :showFleetAssetFuelApplied="jobRateData.fleetAsset.unitRate || jobRateData.fleetAsset.zone"
      :showClientFuelApplied="jobRateData.client.unitRate || jobRateData.client.zone"
      :fleetAssetOwnerGstRegistered="fleetAssetOwner.gstRegistered"
    ></AddAdditionalCharge>

    <v-dialog
      v-model="isViewingDateSelectDialog"
      width="400px"
      content-class="v-dialog-custom"
    >
      <v-layout
        justify-space-between
        class="task-bar app-theme__center-content--header no-highlight"
      >
        <span>Select Date for Service Rates</span>
        <div
          class="app-theme__center-content--closebutton"
          @click="isViewingDateSelectDialog = false"
        >
          <v-icon class="app-theme__center-content--closebutton--icon"
            >fal fa-times</v-icon
          >
        </div>
      </v-layout>
      <v-layout class="app-theme__center-content--body pa-3">
        <v-flex md12>
          <v-layout>
            <v-flex md12 px-2 pb-3>
              <v-alert type="warning" :value="true">
                <strong class="pr-1">Please note:</strong>Performing this action
                will clear all custom values, including:
                <ul>
                  <li>START and FINISH times</li>
                  <li>BREAK times</li>
                  <li>Additional CUSTOM charges</li>
                </ul>
              </v-alert>
            </v-flex>
          </v-layout>
          <v-layout>
            <DatePickerBasic
              @setEpoch="setRateSearchEpoch"
              :hideIcon="true"
              labelName="Apply Rates for Date"
              :boxInput="true"
              :epochTime="rateSearchEpochTemp"
            >
            </DatePickerBasic>
          </v-layout>
          <v-layout justify-space-between>
            <v-btn
              color="white"
              flat
              @click="isViewingDateSelectDialog = false"
            >
              Cancel</v-btn
            >
            <v-btn color="blue" depressed @click="applyRateSearchAndRefresh"
              >Apply Selected Date</v-btn
            >
          </v-layout>
        </v-flex>
      </v-layout>
    </v-dialog>
    <BreakDurationManagement
      v-if="jobDetails.driverId"
      ref="breakDurationManagement"
      :jobDetails="jobDetails"
      :driverId="jobDetails.driverId"
      :jobAccountingDetails="jobAccountingDetails"
      :isViewingDialog.sync="viewingBreakDurationDialog"
      :readOnly="readOnlyView"
      @breakDurationsUpdated="breakDurationsUpdated"
    ></BreakDurationManagement>
  </v-flex>
  <v-flex></v-flex>
  <IntegrationRequirements
    v-if="showEDIConfirmation && jobDetails.workStatus === WorkStatus.DRIVER_COMPLETED"
    :pudItems="jobDetails.pudItems"
    :jobId="jobDetails.jobId"
  ></IntegrationRequirements>
</section>
