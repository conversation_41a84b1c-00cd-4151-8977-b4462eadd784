import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';

export type FuelLevyEvents = {
  /**
   * Request and response for saving a new Client Fuel Surcharge Rate
   * @request
   * * payload:   ClientFuelSurchargeRate
   * * endpoint: '/clientFuelSurchargeRate/save'
   * @response
   * * payload:   ClientFuelSurchargeRate
   * * id:        savedClientFuelSurchargeRates
   * @portal OPERATIONS
   * @subscription USER
   */
  savedClientFuelSurchargeRates: ClientFuelSurchargeRate | null;
  /**
   * Requests and response for getting all client fuel surcharge rates by a clientId
   * @request
   * * payload:   string
   * * endpoint:  '/clientFuelSurchargeRate/getAll'
   * @response
   * * payload:   ClientFuelSurchargeRate[]
   * * id:        selectedAllClientFuelSurchargeRatesList
   * @portal OPERATIONS
   * @subscription USER
   */
  selectedAllClientFuelSurchargeRatesList: ClientFuelSurchargeRate[] | null;

  /**
   * Requests and response for getting all current fuel surcharge rates for the
   * division, regardless of any clientId. This is for use in the division fuel
   * administration screens
   * @request
   * * payload:   N/A
   * * endpoint:  '/clientFuelSurchargeRate/listAll'
   * @response
   * * payload:   ClientFuelSurchargeRate[]
   * * id:        latestClientFuelSurchargeRateList
   * @portal OPERATIONS
   * @subscription USER
   */
  latestClientFuelSurchargeRateList: ClientFuelSurchargeRate[] | null;

  /**
   * Request and response for getting all current client fuel surcharge rate
   * @request
   * * payload:   { clientId: string; searchDate: number }
   * * endpoint: '/clientFuelSurchargeRate/getFuelSurchargeRate'
   * @response
   * * payload:   ClientFuelSurchargeRate[]
   * * id:        selectedCurrentClientFuelSurchargeRates
   * @portal OPERATIONS
   * @subscription USER
   */
  selectedCurrentClientFuelSurchargeRates: ClientFuelSurchargeRate[] | null;

  /**
   * Request and response for saving a new Fleet Asset Fuel Surcharge Rate
   * @request
   * * payload:   FleetAssetFuelSurchargeRate
   * * endpoint: '/fleetAssetFuelSurchargeRate/save'
   * @response
   * * payload:   FleetAssetFuelSurchargeRate
   * * id:        savedFleetAssetFuelSurchargeRates
   * @portal OPERATIONS
   * @subscription USER
   */
  savedFleetAssetFuelSurchargeRates: FleetAssetFuelSurchargeRate | null;
  /**
   * Requests and response for getting all fleet asset fuel surcharge rates by a fleetAssetId
   * @request
   * * payload:   string
   * * endpoint:  '/fleetAssetFuelSurchargeRate/getAll'
   * @response
   * * payload:   FleetAssetFuelSurchargeRate[]
   * * id:        selectedAllFleetAssetFuelSurchargeRatesList
   * @portal OPERATIONS
   * @subscription USER
   */
  selectedAllFleetAssetFuelSurchargeRatesList:
    | FleetAssetFuelSurchargeRate[]
    | null;
  /**
   * Request and response for getting the current fleet asset fuel surcharge rate
   * @request
   * * payload:   RateRequest
   * * endpoint: '/fleetAssetFuelSurchargeRate/getFuelSurchargeRates'
   * @response
   * * payload:   FleetAssetFuelSurchargeRate[]
   * * id:        selectedCurrentFleetAssetFuelSurchargeRates
   * @portal OPERATIONS
   * @subscription USER
   */
  selectedCurrentFleetAssetFuelSurchargeRates:
    | FleetAssetFuelSurchargeRate[]
    | null;

  /**
   * Requests and response for getting all current fleet asset fuel surcharge
   * rates for the division. This is for use in the division fuel administration
   * screens
   * @request
   * * payload:   N/A
   * * endpoint:  '/fleetAssetFuelSurchargeRate/listAll'
   * @response
   * * payload:   FleetAssetFuelSurchargeRate[]
   * * id:        latestFleetAssetFuelSurchargeRateList
   * @portal OPERATIONS
   * @subscription USER
   */
  latestFleetAssetFuelSurchargeRateList: FleetAssetFuelSurchargeRate[] | null;
};
