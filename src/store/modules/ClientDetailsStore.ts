import { initialiseClientDetails } from '@/helpers/classInitialisers/InitialiseClientDetails';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { ClientJobBookingData } from '@/interface-models/Booking/ClientJobBookingData';
import type { ClientApiUserUpdate } from '@/interface-models/Client/APIListUsernamesForClientResponse';
import type { ClientActiveAssociations } from '@/interface-models/Client/ClientActiveAssociations';
import { type ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import type { AddNationalClientIdToClientRequestResponse } from '@/interface-models/Client/ClientDetails/AddNationalClientIdToClientRequestResponse';
import CashSalesClientSelectItem from '@/interface-models/Client/ClientDetails/CashSalesClientSelectItem';
import type { ClientDetails } from '@/interface-models/Client/ClientDetails/ClientDetails';
import ClientListSummary from '@/interface-models/Client/ClientDetails/ClientListSummary';
import type Client<PERSON>erson from '@/interface-models/Client/ClientDetails/ClientPerson/ClientPerson';
import type { ClientRateExpirationSummaryUpdate } from '@/interface-models/Client/ClientDetails/ClientRateExpirationSummaryUpdate';
import type ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import type ClientSummary from '@/interface-models/Client/ClientDetails/ClientSummary';
import type ClientImportUserUpdate from '@/interface-models/Client/ClientImportUserUpdate';
import type { ClientOperationalStatusUpdate } from '@/interface-models/Client/ClientOperationalStatusUpdate';
import {
  getClientRelatedContactsByClientId,
  type ClientRelatedContact,
} from '@/interface-models/Client/ClientRelatedContact';
import type { NationalClientDetails } from '@/interface-models/Client/NationalClientDetails';
import type { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { UnassignedPudItemRequest } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItemRequest';
import { UnassignedPudItemStatus } from '@/interface-models/Jobs/PUD/UnassignedPudItem/UnassignedPudItemStatus';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useJobBookingStore } from '@/store/modules/JobBookingStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { sessionManager } from '@/store/session/SessionState';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import moment from 'moment-timezone';
import { defineStore } from 'pinia';
import { computed, ComputedRef, Ref, ref } from 'vue';

export const useClientDetailsStore = defineStore('clientDetailsStore', () => {
  const selectedClientId: Ref<string | null> = ref(null);
  const selectedClientDetailsView: Ref<string | null> = ref(null);
  const clientSummaryList: Ref<ClientSearchSummary[]> = ref([]);
  const clientPersons: Ref<ClientPerson[]> = ref([]);
  const lastBookingDates: Ref<KeyValue[]> = ref([]);
  const clientRelatedContacts: Ref<ClientRelatedContact[]> = ref([]);
  const clientCommonAddresses: Ref<ClientCommonAddress[]> = ref([]);

  /**
   * Resets all state variables back to their default values. Called when
   * logging out or changing divisions
   */
  function resetState() {
    selectedClientId.value = null;
    selectedClientDetailsView.value = null;
    clientSummaryList.value = [];
    clientPersons.value = [];
    lastBookingDates.value = [];
    clientRelatedContacts.value = [];
    clientCommonAddresses.value = [];
  }

  /**
   * Request and response for fetching the app's client summary list, to be
   * stored in state and used across the app. Called on app mount.
   * @returns ClientSummary[] | null
   */
  async function requestClientSummaryList(): Promise<ClientSummary[] | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/clientDetails/get/listAll', null, false),
        'selectClientList',
      );
      if (result) {
        setClientSummaryList(result);
      }
      return result;
    } catch (e) {
      logConsoleError(e);
      return null;
    }
  }

  /**
   * Set's the client summary list in the store. Called from
   * requestClientSummaryList as part of the initial data requests to populate
   * the store.
   * @param driverDetailsList - List of DriverDetailsSummary objects
   */
  function setClientSummaryList(value: ClientSummary[]) {
    if (!value) {
      return;
    }
    // Map the list to ClientSearchSummary format for convenience
    const summaryList = value
      .map((e: ClientSummary) => {
        return {
          ...e,
          clientDisplayName: `${e.clientId} - ${
            e.tradingName ? e.tradingName : e.clientName
          }`,
          clientSearchCriteria:
            e.clientName +
            '-' +
            e.clientId +
            '-' +
            (e.tradingName ? e.tradingName : ''),
        };
      })
      .sort((a, b) => {
        const compareA = a.clientDisplayName.toUpperCase();
        const compareB = b.clientDisplayName.toUpperCase();
        return compareA < compareB ? -1 : compareA > compareB ? 1 : 0;
      });
    // Sort results
    clientSummaryList.value = summaryList;
    useRootStore().operationsPortalLoadedData.CLIENTS = true;
  }

  /**
   * Returns a list of clients that are available for booking jobs. This list
   * excludes clients that are marked as inactive, retired, or closed, and has a
   * cash sale client added to the front of the list
   */
  const clientListForBooking: ComputedRef<ClientSearchSummary[]> = computed(
    () => {
      const activeClients: ClientSearchSummary[] =
        clientSummaryList.value.filter(
          (x: ClientSearchSummary) => !x.statusList.includes(13),
        );
      return [new CashSalesClientSelectItem()].concat(activeClients);
    },
  );

  /**
   * Returns a list of clients that are available for searching jobs. This list
   * has a cash sale client added to the front of the list and
   * include clients that are marked as inactive, retired, or closed.
   * and sets relevant color for client status
   */
  const clientListForSearching: ComputedRef<ClientListSummary[]> = computed(
    () => {
      const store = useClientDetailsStore();

      // Convert CashSalesClientSelectItem to ClientListSummary
      const cashSalesItem = new CashSalesClientSelectItem();
      const cashSales: ClientListSummary = {
        clientId: cashSalesItem.clientId,
        clientDisplayName: `${cashSalesItem.clientId} - ${
          cashSalesItem.tradingName || cashSalesItem.clientName
        }`,
        clientStatus: '',
        color: '',
        isActive: true,
      };

      const clients: ClientListSummary[] = store.clientSummaryList.map((x) => {
        const status = x.statusList.includes(13)
          ? 'RETIRED'
          : x.creditStatus === 2
            ? 'SEE ACCOUNTS'
            : x.statusList.includes(47)
              ? 'CLOSED'
              : '';

        const color =
          status === 'RETIRED'
            ? '#ff5252'
            : status === 'SEE ACCOUNTS' || status === 'CLOSED'
              ? '#fb8c00'
              : '';

        return {
          clientId: x.clientId,
          clientDisplayName: `${x.clientId} - ${x.tradingName || x.clientName}`,
          clientStatus: status,
          color,
          isActive: !x.statusList.includes(13) && !x.statusList.includes(47),
        };
      });

      const activeClients = clients.filter((c) => c.isActive);
      const inactiveClients = clients.filter((c) => !c.isActive);

      return [cashSales, ...activeClients, ...inactiveClients];
    },
  );

  /**
   * Request and response for saving a full ClientDetails document.
   * @param clientDetails the document to be saved
   * @returns ClientDetails if successful, null if not
   */
  async function saveClientDetails(clientDetails: ClientDetails) {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/clientDetails/save', clientDetails, true),
        'savedClientDetails',
      );
      return result;
    } catch (error) {
      logConsoleError('Error saving client details: ', error);
      return null;
    }
  }

  /**
   * Handles response to a saved client. Transform the response into
   * ClientSearchSummary, then add or replace in current summary list
   * @param clientDetails ClientDetails - the saved client details
   */
  function setIncomingClientDetails(clientDetails: ClientDetails | null) {
    if (!clientDetails?.clientId) {
      return;
    }
    const clientSummary: ClientSearchSummary = {
      clientId: clientDetails.clientId,
      clientName: clientDetails.clientName,
      tradingName: clientDetails.tradingName,
      statusList: clientDetails.statusList,
      clientType: clientDetails.clientType,
      transportCompany: clientDetails.transportCompany,
      creditStatus: clientDetails.accountsReceivable.creditStatus,
      importTransformationType: clientDetails.importTransformationType,
      clientDisplayName: `${
        clientDetails.tradingName || clientDetails.clientName
      } - ${clientDetails.clientId}`,
      clientSearchCriteria:
        clientDetails.clientName +
        '-' +
        clientDetails.clientId +
        '-' +
        (clientDetails.tradingName ?? ''),
      rateExpirationSummaries: clientDetails.rateExpirationSummaries
        ? clientDetails.rateExpirationSummaries
        : null,

      nationalClientId: clientDetails.nationalClientId,
      preAssignedVehicleDetails: clientDetails.preAssignedVehicleDetails,
      preAssignedDriverIds: clientDetails.preAssignedDriverIds,
    };
    // Add or replace in clientSummaryList
    const foundIndex = clientSummaryList.value.findIndex(
      (item) => item.clientId === clientSummary.clientId,
    );
    if (foundIndex === -1) {
      clientSummaryList.value.push(clientSummary);
    } else {
      clientSummaryList.value.splice(foundIndex, 1, clientSummary);
    }
  }

  /**
   * Handles response to division-level message sent after a ClientDetails
   * document has been updated, or some other change was made that would
   * re-calculate the client's expiration summaries (such as a service rate
   * table or fuel surcharge).
   * @param rateSummary ClientRateExpirationSummaryUpdate - used to update the
   * Client Summary object in state for all users.
   * @returns
   */
  function setUpdatedClientExpirationSummary(
    rateSummary: ClientRateExpirationSummaryUpdate | null,
  ) {
    // Return if we don't have sufficient information to find matched client
    if (!rateSummary?.clientId) {
      return;
    }
    // Find client to replace with
    const foundClient = clientSummaryList.value.find(
      (c) => c.clientId === rateSummary.clientId,
    );
    if (!foundClient) {
      return;
    }
    // Replace with incoming rateExpirationSummary if it exists, or set to
    // null if payload rateExpirationSummaries is null
    foundClient.rateExpirationSummaries = rateSummary.rateExpirationSummaries
      ? rateSummary.rateExpirationSummaries
      : null;
  }

  /**
   * Dispatch request to fetch a ClientDetails document using it's clientId
   * (ClientDetails.clientId). Backend matches company and division from the
   * user's token to find the matching document and return it.
   * @param clientId clientId of the client to fetch
   * @returns ClientDetails document, or null if not found
   */
  async function requestClientDetailsByClientId(clientId: string) {
    try {
      // Send request over websocket
      let result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/clientDetails/get/byId', clientId, false),
        'selectedClientDetails',
        {
          mapResponse: (response: ClientDetails | null) =>
            response?.clientId === clientId,
        },
      );
      if (result) {
        result = initialiseClientDetails(result);
      }
      return result;
    } catch (error) {
      logConsoleError('Error fetching client details by clientId: ', error);
      return null;
    }
  }

  /**
   * Sets the selected client id in the store, such that we can view it in the Client Administration screens
   */
  function setSelectedClientId(clientId: string | null) {
    selectedClientId.value = clientId;
  }

  // Set the tab that we should open up when we jump to the ClientDetailsIndex page
  function setSelectedClientDetailsView(id: string | null) {
    selectedClientDetailsView.value = id;
  }

  /**
   * Called when a list of ClientPerson documents is received from the backend.
   * Sets to store for use throughout the app
   * @param value List of ClientPerson documents
   */
  function setClientPersons(value: ClientPerson[]) {
    clientPersons.value = value;
  }

  function updateClientPersons(clientPerson: ClientPerson) {
    const existingClientPersonIndex = clientPersons.value.findIndex(
      (x: ClientPerson) => x._id === clientPerson._id,
    );
    if (existingClientPersonIndex !== -1) {
      clientPersons.value.splice(existingClientPersonIndex, 1, clientPerson);
    } else {
      clientPersons.value.push(clientPerson);
    }
  }

  /**
   * Requests and returns a list of all NationalClientDetails document for the
   * user's company. Called from NationalClientDetails components
   * @returns list of NationalClientDetails, or null if the request fails
   */
  async function requestNationalClientDetailsList(): Promise<
    NationalClientDetails[] | null
  > {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/nationalClientDetails/listAll', null, false),
        'nationalClientDetailsList',
      );
      return result;
    } catch (error) {
      logConsoleError('Error fetching national client details list', error);
      return null;
    }
  }

  /**
   * Requests and returns national client details by it's mongoId.
   *
   * @param id - The ID of the national client details to fetch.
   * @returns A Promise that resolves to the fetched national client details, or
   * null if an error occurs.
   */
  async function requestNationalClientDetailsById(
    id: string,
  ): Promise<NationalClientDetails | null> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/nationalClientDetails/getById', id, false),
        'nationalClientDetailsById',
      );
      return result;
    } catch (error) {
      logConsoleError(
        `Error fetching national client details by id (${id}): `,
        error,
      );
      return null;
    }
  }

  /**
   * Request and response for associating a ClientDetails with a National
   * Client. Called from NationalClientDetailsSelection component
   * @param request contains clientId and nationalClientId
   * @returns AddNationalClientIdToClientRequestResponse if successful, null if
   * not
   */
  async function addNationalClientIdToClient(
    request: AddNationalClientIdToClientRequestResponse,
  ) {
    try {
      // throw error if national clientId or nationalClientId missing from request
      if (!request.clientId || !request.nationalClientId) {
        throw new Error('Client id or national client id empty in request');
      }
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientDetails/addNationalClientIdToClient',
          request,
          true,
        ),
        'addedNationalClientIdToClient',
      );
      return result;
    } catch (error) {
      logConsoleError(
        `Error adding national client id ${request.nationalClientId} to client ${request.clientId}: `,
        error,
      );
      return null;
    }
  }

  /**
   * Handles division-level response when adding a national client id to a
   * client. Finds the associated Client Summary object in the store and updates
   * it with the new national client id.
   * @param response - AddNationalClientIdToClientRequestResponse object
   * containing a clientId and nationalClientId
   */
  function handleAddNationalClientIdToClientResponse(
    response: AddNationalClientIdToClientRequestResponse | null,
  ): void {
    if (!response?.clientId || !response?.nationalClientId) {
      return;
    }
    const clientSummary = clientSummaryList.value.find(
      (x: ClientSummary) => x.clientId === response.clientId,
    );
    if (!clientSummary) {
      return;
    }
    clientSummary.nationalClientId = response.nationalClientId;
  }

  /**
   * Request and response for saving a NationalClientDetails object
   * @param nationalClientDetails - NationalClientDetails object to save
   * @returns NationalClientDetails if successful, null if not
   */
  async function saveNationalClientDetails(
    nationalClientDetails: NationalClientDetails | null,
  ) {
    if (!nationalClientDetails) {
      logConsoleError('No national client details provided');
      return null;
    }
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/nationalClientDetails/save',
          nationalClientDetails,
          true,
        ),
        'savedNationalClientDetails',
      );
      return result;
    } catch (error) {
      logConsoleError('Error saving national client details: ', error);
      return null;
    }
  }

  /**
   * Request and response for fetching details about a client's pending jobs,
   * active jobs and recurring jobs. Used to determine if the Client is able to
   * be marked as inactive or not. If they have any active 'associations', we
   * don't allow the client to be marked as inactive. Called from the
   * ClientDetailsActiveAssociations component.
   * @param clientId - The ID of the client to fetch active associations for
   * @returns ClientActiveAssociations if successful, null if not
   */
  async function requestClientsActiveAssociations(
    clientId?: string,
  ): Promise<ClientActiveAssociations | null> {
    try {
      if (!clientId) {
        throw new Error('Invalid client id provided');
      }
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientDetails/getActiveAssociationsByClientId',
          clientId,
          false,
        ),
        'clientActiveAssociations',
        {
          mapResponse: (response) => response?.clientId === clientId,
        },
      );
      return result;
    } catch (error) {
      logConsoleError(
        `Error fetching client active associations for ${clientId}`,
        error,
      );
      return null;
    }
  }

  /**
   * Called from LastBookingDateCounter component, which requests the last
   * booking dates for a given entity type. This function sets the last booking
   * dates in the store if the entity type is CLIENT.
   * @param dates - list of KeyValue objects containing the last booking dates
   */
  function setLastBookingDates(value: KeyValue[]) {
    lastBookingDates.value = value;
  }

  /**
   * Sets the client related contacts in the store. Called from
   * getClientRelatedContactsByClientId after requesting the contacts.
   * @param value - list of ClientRelatedContact objects to set to state
   */
  function setClientRelatedContacts(value: ClientRelatedContact[]) {
    clientRelatedContacts.value = value;
  }

  /**
   * Updates the client common address list after updating them on the backend.
   * Called from the client admin screens.
   * @param updatedClientCommonAddresses - list of updated ClientCommonAddress
   */
  function setUpdatedClientCommonAddresses(
    updatedClientCommonAddresses: ClientCommonAddress[],
  ): void {
    // check if updates are for the selected client
    if (
      !updatedClientCommonAddresses ||
      updatedClientCommonAddresses.length === 0
    ) {
      return;
    }
    // iterate over updated client common addresses and set local state with updated common address
    for (const clientCommonAddress of updatedClientCommonAddresses) {
      const index: number = clientCommonAddresses.value.findIndex(
        (x: ClientCommonAddress) => x._id === clientCommonAddress._id,
      );

      if (index >= 0) {
        clientCommonAddresses.value.splice(index, 1, clientCommonAddress);
      } else {
        clientCommonAddresses.value.push(clientCommonAddress);
      }
    }
  }

  /**
   * Updates the client related contact list after updating a
   * ClientRelatedContact on the backend. Called from the client admin screens.
   * @param clientRelatedContact - updated contact to set
   */
  function setUpdatedRelatedContact(
    clientRelatedContact: ClientRelatedContact | null,
  ) {
    if (!clientRelatedContact) {
      return;
    }
    // Check that the incoming clientRelatedContract belongs to the same client
    // as the related contacts list currently in state
    if (
      clientRelatedContacts.value.length > 0 &&
      clientRelatedContacts.value[0].clientId !== clientRelatedContact.clientId
    ) {
      return;
    }
    const index: number = clientRelatedContacts.value.findIndex(
      (x: ClientRelatedContact) => x._id === clientRelatedContact._id,
    );
    if (index >= 0) {
      clientRelatedContacts.value.splice(index, 1, clientRelatedContact);
    } else {
      clientRelatedContacts.value.push(clientRelatedContact);
    }
  }

  /**
   * Request and response for updating a client's API user configurations.
   * Called from the client admin screens (from api_user_select component).
   * @param request contains clientId and the updated list of API usernames
   * @returns ClientApiUserUpdate if successful, null if not
   */
  async function updateClientApiUser(
    request: ClientApiUserUpdate,
  ): Promise<ClientApiUserUpdate | null> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/apiclient/save', request, true),
        'savedApiClient',
        { mapResponse: (response) => response?.clientId === request.clientId },
      );
      return result;
    } catch (error) {
      logConsoleError('Error updating client API user: ', error);
      return null;
    }
  }

  /**
   * Request and response for updating a client's import user configurations.
   * Called from the client admin screens (from import_user_select component).
   * @param request contains clientId and the import user
   * @returns ClientImportUserUpdate if successful, null if not
   */
  async function updateClientImportUser(
    request: ClientImportUserUpdate,
  ): Promise<ClientImportUserUpdate | null> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/clientDetails/setImportUser', request, true),
        'clientImportUserUpdate',
        { mapResponse: (response) => response?.clientId === request.clientId },
      );
      return result;
    } catch (error) {
      logConsoleError('Error updating client API user: ', error);
      return null;
    }
  }

  /**
   * Request and response for updating a client's importTransformationType.
   * Called from the client admin screens.
   * @param clientImportUserUpdate
   * @returns
   */
  function setClientImportUserUpdate(
    clientImportUserUpdate: ClientImportUserUpdate | null,
  ) {
    if (!clientImportUserUpdate) {
      return;
    }
    const clientDetailsSummary = clientSummaryList.value.find(
      (x: ClientSummary) => x.clientId === clientImportUserUpdate.clientId,
    );
    if (!clientDetailsSummary) {
      return;
    }
    clientDetailsSummary.importTransformationType =
      clientImportUserUpdate.importUser;
  }

  /**
   * Request and response for updating a client's operational status (statusList
   * and creditStatus).
   * @param request - ClientOperationalStatusUpdate object containing clientId,
   * and the updated statuses
   * @returns ClientOperationalStatusUpdate if successful, null if not
   */
  async function updateClientOperationalStatus(
    request: ClientOperationalStatusUpdate,
  ): Promise<ClientOperationalStatusUpdate | null> {
    try {
      if (!request?.clientId) {
        throw new Error('Client id missing from request');
      }
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientDetails/operationalStatusUpdate',
          request,
          true,
        ),
        'clientOperationalStatusUpdate',
        {
          mapResponse: (response) => response?.clientId === request.clientId,
        },
      );
      return result;
    } catch (error) {
      logConsoleError('Error updating client operational status: ', error);
      return null;
    }
  }

  /**
   * Get a list of client site contacts by clientId (ClientDetails.clientId). Returns the
   * existing list of clientCommonAddresses if it exists in the store, or
   * requests it from the backend if not.
   * TODO: Replace references to this requestCommonAddressesByClientId
   * @return {Promise<ClientCommonAddress[]>}
   */
  async function getClientCommonAddressesByClientId(
    clientId: string,
  ): Promise<ClientCommonAddress[] | null> {
    try {
      if (!clientId) {
        throw new Error(
          'Could not get common addresses for null/empty clientId',
        );
      }
      // Check against store to see if we already have this clients common
      // addresses
      if (
        clientCommonAddresses.value &&
        clientCommonAddresses.value.length > 0 &&
        clientCommonAddresses.value[0].clientId === clientId
      ) {
        return clientCommonAddresses.value;
      }
      // If not, request from backend
      clientCommonAddresses.value = [];

      // Conditionally set the endpoint based on the user's portal
      const endPoint = sessionManager.isOperationsPortal()
        ? '/clientDetails/commonAddress/getByClientId'
        : `/user/${sessionManager.getUserName()}/clientDetails/commonAddress/getByClientId`;

      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(endPoint, clientId, false),
        'commonAddressesByClientId',
        {
          mapResponse: (response: ClientCommonAddress[] | null) =>
            !response ||
            response.length === 0 ||
            response[0].clientId === clientId,
        },
      );

      // Set value to store for reuse
      clientCommonAddresses.value = result ?? [];
      return result;
    } catch (error) {
      logConsoleError(
        'Error fetching common addresses for clientId: ' + clientId,
        error,
      );
      return null;
    }
  }

  /**
   * Handles division-level response for updating a client's operational status.
   * Updates the client's operational status in the store.
   * @param response includes the clientId, updated status and credit status, as
   * well as the updated rate expiration summaries
   */

  function setClientOperationalStatusUpdate(
    response: ClientOperationalStatusUpdate | null,
  ): void {
    // If the response is null, return without making any changes
    if (!response) {
      return;
    }

    // Find the client in the client summary list
    const client = clientSummaryList.value.find(
      (client: ClientSummary) => client.clientId === response.clientId,
    );

    // If the client is not found, return without making any changes
    if (!client) {
      return;
    }

    // If the response contains a credit status, update the client's credit status
    if (response.creditStatus !== null) {
      client.creditStatus = response.creditStatus;

      // If the credit status is 2, add the retired status (7) to the status
      // list if it's not already there Otherwise, remove the retired status
      // from the status list if it's there
      const hasRetiredStatus = client.statusList.includes(7);
      if (response.creditStatus === 2 && !hasRetiredStatus) {
        client.statusList.push(7);
      } else if (hasRetiredStatus) {
        client.statusList = client.statusList.filter((status) => status !== 7);
      }
    }

    // If the response contains a retired status, update the client's retired status
    if (response.isRetired !== null) {
      // If the retired status is true, add the retired status (13) to the
      // status list if it's not already there Otherwise, remove the retired
      // status from the status list if it's there
      const hasRetiredStatus = client.statusList.includes(13);

      if (response.isRetired && !hasRetiredStatus) {
        client.statusList.push(13);
      } else if (!response.isRetired && hasRetiredStatus) {
        client.statusList = client.statusList.filter((status) => status !== 13);
      }
    }

    // Update the client's display name based on the updated operational
    // statuses
    const displayNameSuffix = client.statusList.includes(13)
      ? ' - RETIRED'
      : client.creditStatus === 2
        ? ' - SEE ACCOUNTS'
        : client.statusList.includes(47)
          ? ' - CLOSED'
          : '';

    client.clientDisplayName = `${
      client.tradingName ? client.tradingName : client.clientName
    } - ${client.clientId}${displayNameSuffix}`;

    // Update the client's rate expiration summaries based on the response
    client.rateExpirationSummaries = response.rateExpirationSummaries;
  }

  /**
   * Request and response for fetching a list of common addresses for a client
   * by clientId.
   * @param clientId - The ID of the client (from ClientDetails.clientId) to
   * fetch common addresses for
   * @returns list of ClientCommonAddress objects, or null if an error occurs
   */
  async function requestCommonAddressesByClientId(clientId?: string) {
    try {
      if (!clientId) {
        throw new Error(
          'Could not request common addresses for null/empty clientId',
        );
      }
      // Conditionally set the endpoint based on the user's portal
      const endPoint = sessionManager.isOperationsPortal()
        ? '/clientDetails/commonAddress/getByClientId'
        : `/user/${sessionManager.getUserName()}/clientDetails/commonAddress/getByClientId`;

      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(endPoint, clientId, false),
        'commonAddressesByClientId',
      );
      return result;
    } catch (error) {
      logConsoleError('Error fetching common addresses by clientId: ', error);
      return null;
    }
  }

  /**
   * Request and response for fetching a list of common addresses for all client
   * by commonAddressNickName.
   * @param commonAddressNickName - The nickName of the client common address to
   * fetch common addresses
   * @returns list of ClientCommonAddress objects, or null if an error occurs
   */
  async function requestClientCommonAddressByNickname(
    commonAddressNickName?: string,
  ) {
    try {
      if (!commonAddressNickName) {
        throw new Error(
          'Could not request common addresses for null/empty nickName',
        );
      }
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientDetails/findCommonAddressByNickname',
          commonAddressNickName,
          false,
        ),
        'foundCommonAddressesByNickNameList',
      );
      return result;
    } catch (error) {
      logConsoleError('Error fetching common addresses by nickName: ', error);
      return null;
    }
  }

  /**
   * Request and response for fetching a list of common addresses for all client
   * by AddressId.
   * @param clientAddressId - The AddressId of the address to
   * fetch common addresses
   * @returns list of ClientCommonAddress objects, or null if an error occurs
   */
  async function requestClientCommonAddressByAddressId(
    clientAddressId?: string,
  ) {
    try {
      if (!clientAddressId) {
        throw new Error(
          'Could not request common addresses for null/empty nickName',
        );
      }
      // Send request over websocket
      const result: ClientCommonAddress[] | null =
        await sendRequestAndListenForResponse(
          new WebSocketRequest(
            '/clientDetails/findCommonAddressByAddressId',
            clientAddressId,
            false,
          ),
          'foundCommonAddressesByAddressIdList',
        );
      return result;
    } catch (error) {
      logConsoleError('Error fetching common addresses by nickName: ', error);
      return null;
    }
  }

  /**
   * Fetches all required information for a job booking for the provided
   * ClientDetails. Returns a ClientJobBookingData object containing all all
   * details. For cash sale clients, only the current service rates and fuel
   * surcharge are returned.
   * @param client - The ClientDetails object to fetch booking data for
   */
  async function requestJobBookingDataForClient(
    client: ClientDetails,
  ): Promise<ClientJobBookingData | null> {
    try {
      // Validate that we have a clientId to work with
      if (!client.clientId) {
        throw new Error('Invalid client id provided');
      }
      const timeNow = moment().valueOf();

      // Return just rate information for a cash sale client
      if (client.isCashSale) {
        const serviceRateStore = useServiceRateStore();
        const jobBookingStore = useJobBookingStore();

        const { clientRates, clientFuelRates, serviceRateVariations } =
          await serviceRateStore.getCurrentRatesAndFuelForClientId(
            client.clientId,
            timeNow,
          );

        const quoteDetails = await jobBookingStore.searchQuotes({
          clientId: client.clientId,
        });

        return {
          clientId: client.clientId,
          commonAddresses: null,
          relatedContacts: null,
          clientPersons: null,
          quoteDetails: quoteDetails,
          unassignedPudItems: null,
          currentServiceRates: clientRates,
          currentFuelSurcharges: clientFuelRates,
          serviceRateVariations,
        };
      }
      // Validate that we have a mongo id to work with
      if (!client._id) {
        throw new Error('Invalid client mongo id provided');
      }
      // Construct request for unassigned pud items
      const unassignedPudRequest: UnassignedPudItemRequest = {
        createdStartEpoch: null,
        createdEndEpoch: null,
        pudStartEpoch: null,
        pudEndEpoch: null,
        clientId: client.clientId,
        assignedStatus: [
          UnassignedPudItemStatus.UNASSIGNED,
          UnassignedPudItemStatus.ASSIGNED,
        ],
        groupReferenceId: null,
        clientSuppliedId: null,
        jobId: null,
      };

      // Await all promises at once so we're not chaining awaits
      const [
        commonAddresses,
        relatedContacts,
        clientPersons,
        quoteDetails,
        unassignedPudItems,
        { clientRates, clientFuelRates, serviceRateVariations },
      ] = await Promise.all([
        getClientCommonAddressesByClientId(client.clientId),
        getClientRelatedContactsByClientId(client.clientId),
        useUserManagementStore().getClientPersonsWithAuthDetails(
          client._id,
          client.clientPersonDispatchers,
          true,
        ),
        useJobBookingStore().searchQuotes({ clientId: client.clientId }),
        useDataImportStore().requestUnassignedPudItemList(unassignedPudRequest),
        useServiceRateStore().getCurrentRatesAndFuelForClientId(
          client.clientId,
          timeNow,
        ),
      ]);

      return {
        clientId: client.clientId,
        commonAddresses,
        relatedContacts,
        clientPersons,
        quoteDetails,
        unassignedPudItems: unassignedPudItems?.unassignedPudItemList ?? null,
        currentServiceRates: clientRates,
        currentFuelSurcharges: clientFuelRates,
        serviceRateVariations,
      };
    } catch (error) {
      logConsoleError(
        'Error fetching client booking data by clientId: ',
        error,
      );
      return null;
    }
  }

  return {
    selectedClientId,
    selectedClientDetailsView,
    clientSummaryList,
    clientPersons,
    lastBookingDates,
    clientRelatedContacts,
    clientCommonAddresses,
    resetState,
    requestClientSummaryList,
    setClientSummaryList,
    clientListForBooking,
    saveClientDetails,
    setIncomingClientDetails,
    setUpdatedClientExpirationSummary,
    requestClientDetailsByClientId,
    setSelectedClientId,
    setSelectedClientDetailsView,
    setClientPersons,
    updateClientPersons,
    requestClientsActiveAssociations,
    requestNationalClientDetailsList,
    requestNationalClientDetailsById,
    addNationalClientIdToClient,
    handleAddNationalClientIdToClientResponse,
    saveNationalClientDetails,
    setClientOperationalStatusUpdate,
    setClientImportUserUpdate,
    setLastBookingDates,
    setClientRelatedContacts,
    setUpdatedClientCommonAddresses,
    setUpdatedRelatedContact,
    updateClientApiUser,
    updateClientImportUser,
    updateClientOperationalStatus,
    requestCommonAddressesByClientId,
    requestJobBookingDataForClient,
    requestClientCommonAddressByNickname,
    requestClientCommonAddressByAddressId,
    clientListForSearching,
    getClientCommonAddressesByClientId,
  };
});
