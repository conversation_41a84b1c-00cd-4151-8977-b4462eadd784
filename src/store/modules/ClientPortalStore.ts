import { initialiseClientDetails } from '@/helpers/classInitialisers/InitialiseClientDetails';
import { initialiseJobDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import { initialiseClientServiceRate } from '@/helpers/classInitialisers/InitialiseServiceRate';
import { millisecondsInOneDay } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { ClientJobBookingData } from '@/interface-models/Booking/ClientJobBookingData';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import { ClientPerson } from '@/interface-models/Client/ClientDetails/ClientPerson/ClientPerson';
import { getClientRelatedContactsByClientId } from '@/interface-models/Client/ClientRelatedContact';
import { CurrentClientServiceRateResponse } from '@/interface-models/Client/CurrentClientServiceRateResponse';
import JobBookingDataLoaded from '@/interface-models/ClientAccess/JobBookingDataLoaded';
import { DivisionDetails } from '@/interface-models/Company/DivisionDetails';
import DriverDetailsShort from '@/interface-models/Driver/DriverDetails/DriverDetailsShort';
import FleetAsset from '@/interface-models/FleetAsset/FleetAsset';
import { Portal } from '@/interface-models/Generic/Portal';
import GpsMarkerDetails from '@/interface-models/Generic/Position/GpsMarkerDetails';
import GpsPosition from '@/interface-models/Generic/Position/GpsPosition';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import type JobDetails from '@/interface-models/Jobs/JobDetails';
import SearchJobRequest from '@/interface-models/Jobs/SearchJob/SearchJobRequest';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useRecurringJobStore } from '@/store/modules/RecurringJobStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import moment from 'moment-timezone';
import { defineStore } from 'pinia';
import { ComputedRef, Ref, computed, ref } from 'vue';
import { useJobBookingStore } from './JobBookingStore';
import { useUserManagementStore } from './UserManagementStore';
import { useServiceRateVariationsStore } from '@/store/modules/ServiceRateVariationsStore';

export const useClientPortalStore = defineStore('clientPortalStore', () => {
  const clientDetails: Ref<ClientDetails | null> = ref(null);
  const clientJobList: Ref<JobDetails[]> = ref([]);
  const driverList: Ref<DriverDetailsShort[]> = ref([]);
  const fleetAssetList: Ref<FleetAsset[]> = ref([]);
  const gpsUpdateCounter: Ref<number> = ref(0);
  const allGpsPositions: Ref<Map<string, GpsMarkerDetails>> = ref(new Map());
  const clientServiceRate: Ref<ClientServiceRate | null> = ref(null);
  const currentNavigationId: Ref<number> = ref(0);
  const awaitingUpdatedJobDetails: Ref<number[]> = ref([]);
  const clientFuelSurchargeRate: Ref<ClientFuelSurchargeRate | null> =
    ref(null);
  const jobBookingDataLoaded: Ref<JobBookingDataLoaded> = ref(
    new JobBookingDataLoaded(),
  );
  const clientCommonAddresses: Ref<ClientCommonAddress[]> = ref([]);

  const currentLedgerJobList: Ref<JobDetails[]> = ref([]);
  const selectedJobId: Ref<number | null> = ref(null);

  /**
   * Resets all state variables back to their default values. Called when
   * logging out or changing divisions
   */
  function resetState() {
    clientDetails.value = null;
    clientJobList.value = [];
    driverList.value = [];
    fleetAssetList.value = [];
    allGpsPositions.value = new Map<string, GpsMarkerDetails>();
    clientServiceRate.value = null;
    currentNavigationId.value = 0;
    awaitingUpdatedJobDetails.value = [];
    clientFuelSurchargeRate.value = null;
    jobBookingDataLoaded.value = new JobBookingDataLoaded();
    clientCommonAddresses.value = [];
  }

  /**
   * Sets the id of the current tab being viewed
   * @param id - The id of the current tab
   */
  function setCurrentNavigationId(id: number) {
    currentNavigationId.value = id;
  }

  /**
   * Fetches key application data from various APIs, which will be stored in
   * pinia for use across the app.
   */
  async function getApplicationData() {
    initDivisionDetails();
    initClientDetails();
    useRootStore().requestStatusConfigList();
    useRootStore().requestRoleList();
    useCompanyDetailsStore().requestServiceTypesList(true);
  }

  /**
   * Request and response for fetching the ClientDetails associated with the
   * current logged in user (using values from token). Called the user
   * authenticates.
   * @returns ClientDetails object for the current session
   */
  async function initClientDetails(): Promise<ClientDetails | null> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientDetails/get/authenticatedClientDetails',
          null,
          false,
        ),
        'authenticatedClientDetails',
      );
      if (result) {
        handleClientDetailsResponse(result);
      }
      return result;
    } catch (error) {
      console.error('ClientPortalStore - initClientDetails - Error: ', error);
      return null;
    }
  }

  /**
   * Handles response for a ClientDetails object, called when the user first
   * authenticates on the client portal.
   * @param cd ClientDetails object for the current session
   */
  function handleClientDetailsResponse(cd: ClientDetails): void {
    jobBookingDataLoaded.value.clientDetails = true;
    useRootStore().clientPortalLoadedData.CLIENT_DETAILS = true;
    if (!cd) {
      return;
    }
    // NOTE: Is this ever going to be hit, considering this is the handler for a
    // request over the CLIENT subscription
    if (
      sessionManager.getPortalType() === Portal.OPERATIONS &&
      cd.clientPersonDispatchers.length > 0
    ) {
      requestClientPersonDispatchersByIdList(cd.clientPersonDispatchers);
    }

    clientDetails.value = initialiseClientDetails(cd);
  }

  /**
   * Request and response for fetching the Division Details from the client
   * portal, using details from the token. Called after the user authenticates
   * as part of the initial application data request.
   */
  async function initDivisionDetails(): Promise<DivisionDetails | null> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/user/' +
            sessionManager.getUserName() +
            '/companyDetails/retrieveDivisionDetails',
          null,
          false,
        ),
        'selectedDivisionDetails',
      );
      // Set response to store
      if (result) {
        useCompanyDetailsStore().setClientUserDivisionDetails(result);
      }
      return result;
    } catch (error) {
      console.error('ClientPortalStore - initDivisionDetails - Error: ', error);
      return null;
    }
  }

  /**
   * Request and response for fetching a list of ClientPerson documents by their
   * mongo ID. Called from client portal when the ClientDetails object arrives,
   * but endpoints exist on the backend for this API to work from the Operations
   * portal also.
   * @param idList list of ClientPerson mongo ids
   * @returns
   */
  async function requestClientPersonDispatchersByIdList(
    idList: string[],
  ): Promise<ClientPerson[] | null> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/user/' +
            sessionManager.getUserName() +
            '/clientDetails/clientPersonDispatchers/getByIdList',
          idList,
          true,
        ),
        'getClientPersonsByIdList',
      );
      // Set response to store
      useClientDetailsStore().setClientPersons(result ?? []);
      return result;
    } catch (error) {
      console.error(
        'ClientPortalStore - requestClientPersonDispatchersByIdList - Error: ',
        error,
      );
      return null;
    }
  }

  /**
   * Resets the job list state. Called when the user navigates away from the Job
   * Search component on the client portal
   */
  function resetJobListState() {
    awaitingUpdatedJobDetails.value = [];
    clientJobList.value = [];
    driverList.value = [];
    fleetAssetList.value = [];
    allGpsPositions.value = new Map<string, GpsMarkerDetails>();
  }

  /**
   * Request and response for fetching a summary of jobs for the client using a
   * set of search criteria. Called from the JobSearch component.
   * @param request SearchJobsPagination object containing search criteria
   */
  async function requestJobSummarySearchForClient(
    request: SearchJobRequest,
    page: number = 1,
    size: number = 20,
  ) {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          `/job/clientSearchJobsSummary/${page}/${size}`,
          request,
          true,
        ),
        'searchedJobSummaryList',
      );
      useJobStore().updateJobSummaryState(result);
      return result;
    } catch (error) {
      console.error(
        'ClientPortalStore - failed to search job summaries: ',
        error,
      );
      return null;
    }
  }

  /**
   * Request and response for fetching a list of jobs for the client. Called
   * from the ClientAccessIndex component to populate the dashboard.
   * @param request SearchJobRequest object containing search criteria
   */
  async function requestJobSearchForClient(request: SearchJobRequest) {
    console.log('ClientPortalStore - searchJobsForClient');
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/job/clientSearchJobs', request, true),
        'searchedJobList',
      );
      setJobsForClientResponse(result);
      return result;
    } catch (error) {
      console.error('ClientPortalStore - searchJobsForClient - Error: ', error);
      return null;
    }
  }

  /**
   * Sends request to fetch a list of full JobDetails documents using the given
   * SearchJobRequest.
   * @param request contains various properties to match JobDetails against.
   * @returns Promise<JobDetails[] | null> containing the list of JobDetails
   */
  async function getJobDetailsForJobIds(
    jobIds: number[],
  ): Promise<JobDetails[] | null> {
    try {
      // Validate that provided jobIds have length
      if (!jobIds || jobIds.length < 1) {
        throw new Error('At least one jobId is required to search for jobs');
      }
      // Construct request object
      const request: SearchJobRequest = {
        statusList: [],
        workStatus: null,
        workStatusMin: null,
        workStatusMax: null,
        jobSourceType: null,
        serviceFailure: false,
        startEpoch: 0,
        endEpoch: 0,
        clientId: '',
        jobId: '',
        jobReference: '',
        dispatcherName: '',
        siteContactName: '',
        customerDeliveryName: '',
        suburbName: '',
        fleetAssetId: '',
        driverId: '',
        jobIds,
        invoiceId: '',
        rctiId: '',
        sortByField: null,
        sortDirection: null,
      };

      // Send request over websocket
      let result = await sendRequestAndListenForResponse(
        new WebSocketRequest(`/job/clientSearchJobs`, request, true),
        'searchedJobList',
      );
      // If result is non-null, initialise JobDetails objects in response, add
      // additional data.
      if (result) {
        result = result.map((job: JobDetails) => {
          const newJob = initialiseJobDetails(job);
          newJob.addAdditionalJobData();
          if (newJob.pudItems.some((pud) => pud.createdByDriver)) {
            newJob.pudListFromPartialData();
          }
          return newJob;
        });
      }

      currentLedgerJobList.value = result ?? [];

      return result;
    } catch (error) {
      console.error('Error searching jobs', error);
      return [];
    }
  }

  /**
   * Handles response for the client job search request. If the response contains
   * only one job and that job is in the awaitingUpdatedJobDetails list, then
   * this is a job awaiting an updated JobDetails object. We should find and
   * replace the job in the list. If the response contains multiple jobs, we
   * replace the entire list.
   * @param jobs List of JobDetails objects
   */
  function setJobsForClientResponse(jobs: JobDetails[] | null) {
    if (!jobs) {
      return;
    }
    // If there is only one job in response and that job is included in the awaitingUpdatedJobDetails list
    // then this is a job awaiting an updated JobDetails. We should find and replace, then return;
    if (
      awaitingUpdatedJobDetails.value.length > 0 &&
      jobs.length === 1 &&
      awaitingUpdatedJobDetails.value.includes(
        jobs[0].jobId ? jobs[0].jobId : -1,
      )
    ) {
      const toReplaceWith = initialiseJobDetails(jobs[0]);
      const jobId = toReplaceWith.jobId ? toReplaceWith.jobId : 0;
      console.log(`Find and update single job: jobId --> ${jobId}`);

      if (jobId) {
        const foundIndex = clientJobList.value.findIndex(
          (j) => j.jobId === jobId,
        );
        if (foundIndex !== -1) {
          clientJobList.value.splice(foundIndex, 1, toReplaceWith);
        } else {
          // job update is a new job.
          clientJobList.value.push(toReplaceWith);
        }
        toReplaceWith.getRouteProgressMatrix();
      }
      // Check if we need to re-request driver list again
      const uniqueDriverIds = [
        ...new Set(
          clientJobList.value.map((j) => j.driverId).filter((id) => !!id),
        ),
      ];
      // See if there are any driverIds in the job list that aren't currently in
      // state
      if (
        uniqueDriverIds.some(
          (id) => !driverList.value.map((d) => d.driverId).includes(id),
        )
      ) {
        // If there is at least 1 that isn't in the list, then we should
        // re-request the driver list
        requestDriverDetailsFromJobIdList(
          clientJobList.value.map((j) => j.jobId ?? 0),
        );
      }
      const uniqueFleetAssetIds = [
        ...new Set(
          clientJobList.value.map((j) => j.fleetAssetId).filter((id) => !!id),
        ),
      ];
      // See if there are any fleetAssetIds in the job list that aren't
      // currently in state
      if (
        uniqueFleetAssetIds.some(
          (id) => !fleetAssetList.value.map((f) => f.fleetAssetId).includes(id),
        )
      ) {
        // If there is at least 1 that isn't in the list, then we should
        // re-request the fleet asset list

        useWebsocketStore().sendWebsocketRequest(
          new WebSocketRequest(
            '/user/' +
              sessionManager.getUserName() +
              '/fleetAssetDetails/get/byJobIds',
            clientJobList.value.map((j) => j.jobId),
            true,
          ),
        );
      }
      return;
    }
    // If the above is not true, then we replace the list as usual.
    const jobList: JobDetails[] = [];
    for (let i = 0; i < jobs.length; i++) {
      const job = jobs[i];
      const newJob = initialiseJobDetails(job);
      const routeProgressRequired: boolean =
        newJob.workStatus >= WorkStatus.BOOKED;
      if (routeProgressRequired) {
        newJob.getRouteProgressMatrix();
      }
      jobList.push(newJob);
    }

    clientJobList.value = jobList;
  }

  /**
   * Request and response for fetching a list of GPS positions for a job. This
   * is called from the ClientAccessJobReport screen such that we can display
   * the route taken.
   * @param jobId - The jobId to fetch GPS data for
   */
  async function retrieveClientGPSPositionListByJobId(jobId: number) {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/job/retrieveClientGPSPositionListByJobId',
          `${jobId}`,
          false,
        ),
        'selectedClientJobGPSData',
      );
      return result;
    } catch (error) {
      console.error('Error retrieving client GPS data: ', error);
      return null;
    }
  }

  /**
   * Request and response for fetching a list of Driver Details (summary
   * versions) based on a list of jobIds. This is called from ClientAccessIndex
   * when the job list arrives, and fetches details about the drivers who are
   * performing those jobs.
   * @param jobIdList - List of jobIds that we want to fetch driver information
   * for
   */
  async function requestDriverDetailsFromJobIdList(jobIdList: number[]) {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/driverDetails/get/listAllByJobIds',
          jobIdList,
          true,
        ),
        'selectClientDriverList',
      );
      // Set result to state
      if (result) {
        driverList.value = result;
      }
      return result;
    } catch (error) {
      console.error(
        'ClientPortalStore - requestDriverDetailsFromJobIdList - Error: ',
        error,
      );
      return null;
    }
  }
  /**
   * Request and response for fetching a list of FleetAsset documents based on a
   * list of jobIds. This is called from ClientAccessIndex when the job list
   * arrives, and fetches details about the vehicles who are performing those
   * jobs.
   * @param jobIdList - List of jobIds that we want to fetch fleet information
   * for
   */
  async function requestFleetAssetDetailsFromJobIdList(jobIdList: number[]) {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/user/' +
            sessionManager.getUserName() +
            '/fleetAssetDetails/get/byJobIds',
          jobIdList,
          true,
        ),
        'selectFleetAssetListByJobIdsForClient',
      );
      if (result) {
        fleetAssetList.value = result;
      }
      return result;
    } catch (error) {
      console.error(
        'ClientPortalStore - requestFleetAssetDetailsFromJobIdList - Error: ',
        error,
      );
      return null;
    }
  }

  /**
   * Request and response for fetching a client's service rates. Called from the
   * client portal booking screen
   * @param searchDate - The date to search for service rates
   * @returns ClientServiceRateResponse object
   */
  async function getClientServiceRate(
    searchDate: number,
  ): Promise<CurrentClientServiceRateResponse | null> {
    const request = { searchDate };
    const endPoint =
      '/user/' +
      sessionManager.getUserName() +
      '/clientServiceRate/getMergedServiceRatesForClientByClientId';

    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(endPoint, request, true),
        'selectedMergedServiceRatesForClient',
      );
      if (result?.clientServiceRate) {
        result.clientServiceRate = initialiseClientServiceRate(
          result.clientServiceRate,
        );
        clientServiceRate.value = result.clientServiceRate;
        jobBookingDataLoaded.value.serviceRate = true;
      }
      return result;
    } catch (error) {
      console.error(
        'ClientPortalStore - getClientServiceRate - Error: ',
        error,
      );
      return null;
    }
  }

  /**
   * Sends save request for a JobDetails object. Called from the
   * ClientAccessBookJob component. Listen for the 'clientJobStatusUpdate' mitt
   * event containing the jobId.
   * @param jobDetails - JobDetails object to save
   */
  async function saveClientJob(jobDetails: JobDetails) {
    try {
      const endPoint =
        '/user/' + sessionManager.getUserName() + '/job/saveJobDetails';
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(endPoint, jobDetails, true),
        'clientJobStatusUpdate',
      );
      return result;
    } catch (error) {
      console.error('ClientPortalStore - saveClientJob - Error: ', error);
      return null;
    }
  }

  /**
   * Request and response for fetching a client's fuel levy. Called from the
   * client portal booking screen
   */
  async function getClientFuelSurchargeByDate(
    date: number,
  ): Promise<ClientFuelSurchargeRate[] | null> {
    const endPoint =
      '/user/' +
      sessionManager.getUserName() +
      '/clientFuelSurchargeRate/getAuthenticatedClientFuelSurchargeRatesBySearchDate';

    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(endPoint, date, true),
        'authenticatedClientFuelSurchargeRates',
      );
      if (result) {
        clientFuelSurchargeRate.value = result;
        jobBookingDataLoaded.value.fuelSurcharge = true;
      }
      return result ? [result] : null;
    } catch (error) {
      console.error(
        'ClientPortalStore - getClientFuelSurchargeByDate - Error: ',
        error,
      );
      return null;
    }
  }

  /**
   * Request and response for fetching  a list of last known GPS locations for
   * the provided fleetAssetIds.
   * @param fleetAssetIds - List of fleetAssetIds to fetch GPS data for
   */
  async function requestLastKnownLocationsFromIdList(fleetAssetIds: string[]) {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/gpsData/get/listAllLatestGPSPositions',
          fleetAssetIds,
          true,
        ),
        'latestGPSPositions',
      );
      setLastKnownLocationsResponse(result);
      return result;
    } catch (error) {
      console.error('Error retrieving last known locations: ', error);
      return null;
    }
  }

  /**
   * Handles the response for a list of last known GPS locations. The response
   * contains a list of GpsPosition objects, which we convert to GpsMarkerDetails
   * objects and store in the allGpsPositions map.
   * @param payload - List of GpsPosition objects
   */
  function setLastKnownLocationsResponse(payload: GpsPosition[] | null) {
    if (!payload?.length) {
      return;
    }
    for (let i = 0; i < payload.length; i++) {
      const data: GpsPosition = payload[i];
      const markerDetails = new GpsMarkerDetails();
      markerDetails.fromGpsPosition(data);
      const lastKnown = allGpsPositions.value.get(markerDetails.fleetAssetId);
      if (lastKnown !== undefined) {
        markerDetails.csrAssignedId = lastKnown.csrAssignedId;
        markerDetails.driverName = lastKnown.driverName;
      }

      if (!markerDetails.csrAssignedId) {
        const foundFleet = fleetAssetList.value.find(
          (f) => f.fleetAssetId === markerDetails.fleetAssetId,
        );
        markerDetails.csrAssignedId = foundFleet
          ? foundFleet.csrAssignedId
          : '';
      }
      if (!markerDetails.driverName) {
        const foundDriver = driverList.value.find(
          (d) => d.driverId === markerDetails.driverId,
        );
        markerDetails.driverName = foundDriver ? foundDriver.name : '';
      }
      // Set jobDisplayId in marker. If we can find a recurringJobId in
      // recurringJobIdMap then use that, otherwise use the jobId
      if (markerDetails.jobId) {
        const recurringJobId = useRecurringJobStore().recurringJobIdMap.get(
          markerDetails.jobId,
        );
        markerDetails.jobDisplayId = recurringJobId
          ? recurringJobId
          : `${markerDetails.jobId}`;
      }
      allGpsPositions.value.set(data.fleetAssetId, markerDetails);
    }
    gpsUpdateCounter.value++;
  }

  /**
   * Response handler for incoming GPS coordinates sent from a driver's mobile
   * device. These are converted to GpsMarkerDetails objects and added to the
   * allGpsPositions map.
   * @param gpsData - List of GpsPosition objects
   */
  function setLiveGpsPositionData(gpsData: GpsPosition[] | null) {
    if (!gpsData?.length) {
      return;
    }
    const mostRecent: GpsPosition = gpsData[gpsData.length - 1];
    const markerDetails = new GpsMarkerDetails();
    markerDetails.fromGpsPosition(mostRecent);
    const lastKnown = allGpsPositions.value.get(markerDetails.fleetAssetId);
    // Add details from previous marker for performance, otherwise find their
    // values.
    if (lastKnown !== undefined) {
      markerDetails.csrAssignedId = lastKnown.csrAssignedId;
      markerDetails.driverName = lastKnown.driverName;
    }
    // Set csrAssignedI
    if (!markerDetails.csrAssignedId) {
      const foundFleet = fleetAssetList.value.find(
        (f) => f.fleetAssetId === markerDetails.fleetAssetId,
      );
      markerDetails.csrAssignedId = foundFleet ? foundFleet.csrAssignedId : '';
    }
    // Set driverName
    if (!markerDetails.driverName) {
      const foundDriver = driverList.value.find(
        (d) => d.driverId === markerDetails.driverId,
      );
      markerDetails.driverName = foundDriver ? foundDriver.name : '';
    }
    // Set jobDisplayId in marker. If we can find a recurringJobId in
    // recurringJobIdMap then use that, otherwise use the jobId
    if (markerDetails.jobId) {
      const recurringJobId = useRecurringJobStore().recurringJobIdMap.get(
        markerDetails.jobId,
      );
      markerDetails.jobDisplayId = recurringJobId
        ? recurringJobId
        : `${markerDetails.jobId}`;
    }
    allGpsPositions.value.set(markerDetails.fleetAssetId, markerDetails);
    gpsUpdateCounter.value++;
  }

  /**
   * Handles the response for a jobStatusUpdate event. If the jobId is in the
   * current list, then we should request the updated version. If the job is not
   * existing we will attempt to fetch the job along with the selected date
   * ranges in client_access_dashboard component.
   * @param jobId
   * @returns
   */
  function receivedUpdatedJobDetails(jobId: number) {
    // If the current tab is the Home or Live Driver Map, we should check if the incoming jobId is in list
    if (currentNavigationId.value === 0 || currentNavigationId.value === 1) {
      const foundJob = clientJobList.value.find((j) => j.jobId === jobId);
      // If index is found, then this job is in the current list and we should
      // request the updated version. If the job is not existing we will attempt
      // to fetch the job along with the selected date ranges in
      // client_access_dashboard component
      if (foundJob && foundJob.jobId) {
        console.log(
          `Job found in current list. Request updated JobDetails for jobId: ${foundJob.jobId}`,
        );
        awaitingUpdatedJobDetails.value.push(foundJob.jobId);
        const startEpoch = foundJob.pudItems[0]
          ? foundJob.pudItems[0].epochTime - millisecondsInOneDay
          : 0;
        const endEpoch = foundJob.pudItems[0]
          ? foundJob.pudItems[0].epochTime + millisecondsInOneDay
          : 0;

        const request: SearchJobRequest = {
          statusList: [],
          workStatus: null,
          workStatusMin: WorkStatus.BOOKED,
          workStatusMax: WorkStatus.FINALISED,
          jobSourceType: null,
          serviceFailure: false,
          startEpoch,
          endEpoch,
          clientId: '',
          jobId: `${foundJob.jobId}`,
          jobReference: '',
          dispatcherName: '',
          siteContactName: '',
          customerDeliveryName: '',
          suburbName: '',
          fleetAssetId: '',
          driverId: '',
          jobIds: [],
          invoiceId: '',
          rctiId: '',
          sortByField: null,
          sortDirection: null,
        };
        requestJobSearchForClient(request);
        return;
      }
    }
  }

  /**
   * Reset the booking screen data. Called when the Client Access Book Job
   * component unmounts
   */
  function unloadJobBookingData() {
    clientServiceRate.value = null;
    clientFuelSurchargeRate.value = null;
    jobBookingDataLoaded.value.serviceRate = false;
    jobBookingDataLoaded.value.fuelSurcharge = false;
  }

  /**
   * Used in booking screen template to only display the component when all
   * required data has arrived.
   */
  const isJobBookingDataLoaded: ComputedRef<boolean> = computed(() => {
    const data: JobBookingDataLoaded | any = jobBookingDataLoaded.value;
    return Object.keys(data).every((k) => data[k]);
  });

  /**
   * Fetches all required information for a job booking for the provided
   * ClientDetails. Returns a ClientJobBookingData object containing all all
   * details. For cash sale clients, only the current service rates and fuel
   * surcharge are returned.
   * @param client - The ClientDetails object to fetch booking data for
   */
  async function requestJobBookingDataForClient(
    client: ClientDetails,
  ): Promise<ClientJobBookingData | null> {
    try {
      // Validate that we have a clientId to work with
      if (!client.clientId) {
        throw new Error('Invalid client id provided');
      }
      const timeNow = moment().valueOf();

      // Validate that we have a mongo id to work with
      if (!client._id) {
        throw new Error('Invalid client mongo id provided');
      }

      // Await all promises at once so we're not chaining awaits
      const [
        commonAddresses,
        relatedContacts,
        clientPersons,
        currentFuelSurcharges,
        currentServiceRates,
        quoteDetails,
        serviceRateVariations,
      ] = await Promise.all([
        useClientDetailsStore().getClientCommonAddressesByClientId(
          client.clientId,
        ),
        getClientRelatedContactsByClientId(client.clientId),
        useUserManagementStore().getClientPersonsWithAuthDetails(
          client._id,
          client.clientPersonDispatchers,
          true,
        ),
        getClientFuelSurchargeByDate(timeNow),
        getClientServiceRate(timeNow),
        useJobBookingStore().searchQuotes({ clientId: client.clientId }),
        useServiceRateVariationsStore().getServiceRateVariationsByClient(
          client.clientId,
          timeNow,
        ),
      ]);

      return {
        clientId: client.clientId,
        unassignedPudItems: [],
        serviceRateVariations,
        commonAddresses,
        relatedContacts,
        clientPersons,
        currentFuelSurcharges,
        currentServiceRates,
        quoteDetails,
      };
    } catch (error) {
      console.error('Error fetching client booking data by clientId: ', error);
      return null;
    }
  }

  function setSelectedJobId(jobId: number | null) {
    selectedJobId.value = jobId;
  }

  function setCurrentLedgerJobList(value: JobDetails[]) {
    currentLedgerJobList.value = value;
  }

  return {
    clientDetails,
    clientJobList,
    driverList,
    fleetAssetList,
    gpsUpdateCounter,
    allGpsPositions,
    clientServiceRate,
    currentNavigationId,
    awaitingUpdatedJobDetails,
    clientFuelSurchargeRate,
    jobBookingDataLoaded,
    clientCommonAddresses,
    resetState,
    setCurrentNavigationId,
    initClientDetails,
    handleClientDetailsResponse,
    resetJobListState,
    requestJobSearchForClient,
    requestJobSummarySearchForClient,
    setJobsForClientResponse,
    retrieveClientGPSPositionListByJobId,
    requestDriverDetailsFromJobIdList,
    requestFleetAssetDetailsFromJobIdList,
    getClientServiceRate,
    saveClientJob,
    getClientFuelSurchargeByDate,
    requestLastKnownLocationsFromIdList,
    setLastKnownLocationsResponse,
    setLiveGpsPositionData,
    receivedUpdatedJobDetails,
    unloadJobBookingData,
    isJobBookingDataLoaded,
    getApplicationData,
    requestJobBookingDataForClient,
    getJobDetailsForJobIds,
    setSelectedJobId,
    selectedJobId,
    currentLedgerJobList,
    setCurrentLedgerJobList,
  };
});
