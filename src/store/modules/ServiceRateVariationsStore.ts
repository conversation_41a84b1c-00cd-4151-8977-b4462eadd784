import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  returnRateTypeLongNameFromId,
  returnServiceTypeLongNameFromId,
} from '@/helpers/StaticDataHelpers/StaticDataHelpers';
import { ClientRateExpirationSummaries } from '@/interface-models/Client/ClientDetails/ClientRateExpirationSummary';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import { ClientServiceRateVariationsRequest } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariationsRequest';
import { ClientServiceRateVariationsResponse } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariationsResponse';
import { ClientServiceRateVariationsStatus } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariationsStatus';
import {
  ExpiringDocumentType,
  RateExpirationSummary,
} from '@/interface-models/ServiceRates/RateExpirationSummary';
import { sessionManager } from '@/store/session/SessionState';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import { defineStore } from 'pinia';
import { useClientDetailsStore } from './ClientDetailsStore';

// TODO: This store has no state, getters, or actions that require reactivity.
// It could be converted to a simple module or utility function instead of a Pinia store.
export const useServiceRateVariationsStore = defineStore(
  'serviceRateVariationsStore',
  () => {
    /**
     * Saves a full ClientServiceRateVariations document over websocket and listens for the
     * response. Returns the saved ClientServiceRate if successful, otherwise null.
     * @param rateVariations - The ClientServiceRate to save
     * @returns ClientServiceRate | null
     */
    async function saveClientServiceRateVariations(
      rateVariations: ClientServiceRateVariations,
    ): Promise<ClientServiceRateVariations | null> {
      try {
        // Set company and division if not already set
        rateVariations.company ||= sessionManager.getCompanyId();
        rateVariations.division ||= sessionManager.getDivisionId();

        const result: ClientServiceRateVariations | string | null =
          await sendRequestAndListenForResponse(
            new WebSocketRequest(
              '/clientServiceRateVariations/save',
              rateVariations,
              true,
            ),
            'savedClientServiceRateVariations',
          );
        // If result is a string, it indicates an error message
        if (typeof result === 'string') {
          throw new Error(result);
        }
        return result;
      } catch (error) {
        logConsoleError('Error saving Client Service Rate Variations', error);
        return null;
      }
    }

    /**
     * Saves or updates a list of ClientServiceRateVariations documents over
     * websocket and listens for the response. Returns the saved
     * ClientServiceRateVariations[] if successful, otherwise null.
     * @param rateVariationsList - The list of ClientServiceRateVariations to
     * save
     * @returns ClientServiceRateVariations[] | null
     */
    async function saveAllClientServiceRateVariations(
      rateVariationsList: ClientServiceRateVariations[],
    ): Promise<ClientServiceRateVariations[] | null> {
      try {
        // Set company and division for each item if not already set
        const companyId = sessionManager.getCompanyId();
        const divisionId = sessionManager.getDivisionId();
        rateVariationsList.forEach((item) => {
          item.company ||= companyId;
          item.division ||= divisionId;
        });

        const result: ClientServiceRateVariations[] | string | null =
          await sendRequestAndListenForResponse(
            new WebSocketRequest(
              '/clientServiceRateVariations/saveAll',
              rateVariationsList,
              true,
            ),
            'savedClientServiceRateVariationsList',
          );
        // If result is a string, it indicates an error message
        if (typeof result === 'string') {
          throw new Error(result);
        }
        return result;
      } catch (error) {
        logConsoleError(
          'Error saving all Client Service Rate Variations',
          error,
        );
        return null;
      }
    }

    /**
     * Requests the service rate variations for a specific client and date.
     * @param clientId - The ID of the client for which to fetch service rate variations
     * @param searchDate - The date in epoch time for which to fetch service rate variations
     * @returns ClientServiceRateVariations[] | null
     */
    async function getServiceRateVariationsByClient(
      clientId: string | undefined,
      searchDate: number,
    ): Promise<ClientServiceRateVariations[] | null> {
      try {
        if (!clientId) {
          throw new Error(
            'Client ID is required to fetch Service Rate Variations',
          );
        }

        // Conditionally set the endpoint based on whether the user is in the
        // client portal
        const endpointPath =
          '/clientServiceRateVariations/getServiceRateVariationsByClient';
        const endpoint = sessionManager.isClientPortal()
          ? `/user/${sessionManager.getUserName()}${endpointPath}`
          : endpointPath;

        // Construct the request payload
        const request: ClientServiceRateVariationsRequest = {
          clientId,
          searchDate,
        };
        const result: ClientServiceRateVariationsResponse | null =
          await sendRequestAndListenForResponse(
            new WebSocketRequest(endpoint, request, true),
            'serviceRateVariationsByClient',
            {
              mapResponse: (
                response: ClientServiceRateVariationsResponse | null,
              ) => {
                // TODO: Do we need to also handle the response clientId '0'?
                return !response || response.clientId === request.clientId;
              },
            },
          );
        // // TODO: Debugging only - remove in production
        // if (result?.clientServiceRateVariations) {
        //   console.table(
        //     result.clientServiceRateVariations.map((x) => ({
        //       rateTypeName: returnRateTypeLongNameFromId(x.rateTypeId),
        //       serviceTypeName: returnServiceTypeLongNameFromId(x.serviceTypeId),
        //       ...x,
        //     })),
        //   );
        // }
        return result?.clientServiceRateVariations ?? null;
      } catch (error) {
        logConsoleError(
          'Error fetching Service Rate Variations by Client',
          error,
        );
        return null;
      }
    }

    /**
     * Requests the service rate variations for the division of the current user, based on the provided status
     * @param status - The status filter for rate variations
     * @returns ClientServiceRateVariations[] | null
     */
    async function getDivisionRateVariationsByStatus(
      status: ClientServiceRateVariationsStatus,
    ): Promise<ClientServiceRateVariations[] | null> {
      try {
        if (!status) {
          throw new Error(
            'Status is required to fetch Division Rate Variations',
          );
        }
        const result: ClientServiceRateVariations[] | null =
          await sendRequestAndListenForResponse(
            new WebSocketRequest(
              '/clientServiceRateVariations/getClientServiceRateVariationsByDivision',
              status,
              true,
            ),
            'serviceRateVariationsByDivision',
          );

        // Update client expiration summaries when rate variations are loaded
        if (result && result.length > 0) {
          refreshAllClientRateVariationsExpirationSummaries(result);
        }

        return result;
      } catch (error) {
        logConsoleError(
          'Error fetching Division Rate Variations by Status',
          error,
        );
        return null;
      }
    }

    /**
     * Updates a client's rate expiration summaries with the latest rate variations data
     * @param clientId - The client ID to update
     * @param allRateVariations - Array of all rate variations to check against
     * @returns Updated ClientRateExpirationSummaries or null if client not found
     */
    function updateClientRateVariationsExpirationSummary(
      clientId: string,
      allRateVariations: ClientServiceRateVariations[],
    ): ClientRateExpirationSummaries | null {
      const clientDetailsStore = useClientDetailsStore();

      // Find the client in the store
      const client = clientDetailsStore.clientSummaryList.find(
        (c) => c.clientId === clientId,
      );

      if (!client) {
        return null;
      }

      // Get current expiration summaries or create new ones
      const currentSummaries =
        client.rateExpirationSummaries || new ClientRateExpirationSummaries();

      // Generate the new rate variations summary
      const rateVariationsSummary = generateServiceRateVariationsSummary(
        allRateVariations,
        clientId,
      );

      // Update the summaries with the new rate variations data
      const updatedSummaries = new ClientRateExpirationSummaries(
        currentSummaries.serviceRateSummary,
        currentSummaries.fuelSurchargeSummary,
        currentSummaries.defaultsConfigurationSummary,
        rateVariationsSummary,
      );

      return updatedSummaries;
    }

    /**
     * Gets all client IDs that are affected by the given rate variations
     * @param rateVariations - Array of rate variations to check
     * @returns Array of unique client IDs that are affected
     */
    function getAffectedClientIds(
      rateVariations: ClientServiceRateVariations[],
    ): string[] {
      const clientIds = new Set<string>();

      rateVariations.forEach((variation) => {
        variation.applyToIds.forEach((clientId) => {
          clientIds.add(clientId);
        });
      });

      return Array.from(clientIds);
    }

    /**
     * Updates all affected clients' expiration summaries when rate variations change
     * This function should be called whenever rate variations are added, updated, or deleted
     * @param allRateVariations - Array of all current rate variations
     */
    function refreshAllClientRateVariationsExpirationSummaries(
      allRateVariations: ClientServiceRateVariations[],
    ): void {
      const affectedClientIds = getAffectedClientIds(allRateVariations);
      const clientDetailsStore = useClientDetailsStore();

      affectedClientIds.forEach((clientId) => {
        const updatedSummaries = updateClientRateVariationsExpirationSummary(
          clientId,
          allRateVariations,
        );

        if (updatedSummaries) {
          // Find and update the client in the store
          const client = clientDetailsStore.clientSummaryList.find(
            (c) => c.clientId === clientId,
          );

          if (client) {
            client.rateExpirationSummaries = updatedSummaries;
          }
        }
      });
    }

    /**
     * Generates RateExpirationSummary objects from ClientServiceRateVariations for a specific client
     * @param rateVariations - Array of rate variations to process
     * @param clientId - The client ID to filter variations for
     * @returns Array of RateExpirationSummary objects for expired or expiring variations
     */
    function generateRateVariationsExpirationSummary(
      rateVariations: ClientServiceRateVariations[],
      clientId: string,
    ): RateExpirationSummary[] {
      if (!rateVariations || rateVariations.length === 0) {
        return [];
      }

      const now = new Date().getTime();
      const sevenDaysFromNow = now + 7 * 24 * 60 * 60 * 1000;

      // Filter variations that apply to this client
      const clientVariations = rateVariations.filter((variation) =>
        variation.applyToIds.includes(clientId),
      );

      // Process each variation to create expiration summaries
      const expirationSummaries: RateExpirationSummary[] = [];

      clientVariations.forEach((variation) => {
        const validTo = variation.validToDate || Number.MAX_SAFE_INTEGER;

        // Only include variations that are expired or expiring soon
        const isExpired = now > validTo;
        const isExpiringSoon = !isExpired && validTo < sevenDaysFromNow;

        if (isExpired || isExpiringSoon) {
          // Generate a descriptive name for the variation
          const serviceTypeName = variation.serviceTypeId
            ? returnServiceTypeLongNameFromId(variation.serviceTypeId)
            : 'All Service Types';

          const rateTypeName = variation.rateTypeId
            ? returnRateTypeLongNameFromId(variation.rateTypeId)
            : 'All Rate Types';

          // Create adjustment description
          const adjustments: string[] = [];
          if (variation.clientAdjustmentPercentage !== null) {
            adjustments.push(
              `Client: ${variation.clientAdjustmentPercentage > 0 ? '+' : ''}${
                variation.clientAdjustmentPercentage
              }%`,
            );
          }
          if (variation.fleetAssetAdjustmentPercentage !== null) {
            adjustments.push(
              `Fleet: ${
                variation.fleetAssetAdjustmentPercentage > 0 ? '+' : ''
              }${variation.fleetAssetAdjustmentPercentage}%`,
            );
          }

          const adjustmentText =
            adjustments.length > 0 ? ` (${adjustments.join(', ')})` : '';
          const name = `Rate Variation: ${serviceTypeName} - ${rateTypeName}${adjustmentText}`;

          expirationSummaries.push({
            attentionRequired: true,
            name,
            validFromDate: variation.validFromDate || undefined,
            validToDate: variation.validToDate || undefined,
            priority: isExpired ? 1 : isExpiringSoon ? 2 : 3,
            entityId: clientId,
            expirationType: ExpiringDocumentType.SERVICE_RATE_VARIATIONS,
          });
        }
      });

      // Sort by priority (urgent first), then by expiration date
      return expirationSummaries.sort((a, b) => {
        if (a.priority !== b.priority) {
          return (a.priority || 3) - (b.priority || 3);
        }
        // Sort by expiration date (earliest first)
        const aDate = a.validToDate || 0;
        const bDate = b.validToDate || 0;
        return aDate - bDate;
      });
    }

    /**
     * Generates a single RateExpirationSummary for the serviceRateVariationsSummary field
     * This represents the most urgent rate variation expiration for a client
     * @param rateVariations - Array of rate variations to process
     * @param clientId - The client ID to generate summary for
     * @returns RateExpirationSummary for the most urgent expiration or null if none found
     */
    function generateServiceRateVariationsSummary(
      rateVariations: ClientServiceRateVariations[],
      clientId: string,
    ): RateExpirationSummary | null {
      const summaries = generateRateVariationsExpirationSummary(
        rateVariations,
        clientId,
      );

      if (summaries.length === 0) {
        return null;
      }

      // If there are multiple expired/expiring variations, create a summary that represents them all
      const mostUrgent = summaries[0];
      const expiredCount = summaries.filter((s) => s.priority === 1).length;
      const expiringSoonCount = summaries.filter(
        (s) => s.priority === 2,
      ).length;

      let summaryName = '';
      if (expiredCount > 0 && expiringSoonCount > 0) {
        summaryName = `Rate Variations: ${expiredCount} expired, ${expiringSoonCount} expiring soon`;
      } else if (expiredCount > 0) {
        summaryName =
          expiredCount === 1
            ? 'Rate Variation: 1 expired'
            : `Rate Variations: ${expiredCount} expired`;
      } else if (expiringSoonCount > 0) {
        summaryName =
          expiringSoonCount === 1
            ? 'Rate Variation: 1 expiring soon'
            : `Rate Variations: ${expiringSoonCount} expiring soon`;
      }

      return {
        attentionRequired: true,
        name: summaryName,
        validFromDate: mostUrgent.validFromDate,
        validToDate: mostUrgent.validToDate,
        priority: mostUrgent.priority,
        entityId: clientId,
        expirationType: ExpiringDocumentType.SERVICE_RATE_VARIATIONS,
      };
    }

    return {
      saveClientServiceRateVariations,
      saveAllClientServiceRateVariations,
      getServiceRateVariationsByClient,
      getDivisionRateVariationsByStatus,
      refreshAllClientRateVariationsExpirationSummaries,
    };
  },
);
