import { downloadReport } from '@/helpers/AttachmentHelpers/AttachmentHelpers';
import { initialiseAdditionalChargeItem } from '@/helpers/classInitialisers/InitialiseAdditionalChargeItem';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { CurrentInvoiceRunTypeResponse } from '@/interface-models/Accounting/CurrentInvoiceRunTypeResponse';
import { AdditionalChargeItem } from '@/interface-models/AdditionalCharges/AdditionalChargeItem';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import { AuditRequest } from '@/interface-models/Audit/AuditRequest';
import { DriverDeviceQuery } from '@/interface-models/Driver/Device/DriverDeviceQuery';
import { DriverDeviceSnapshot } from '@/interface-models/Driver/Device/DriverDeviceSnapshot';
import LedgerItemsResponse from '@/interface-models/Generic/Accounting/Ledgers/LedgerItemsResponse';
import SearchLedgerRequest from '@/interface-models/Generic/Accounting/Ledgers/SearchLedgerRequest';
import { EntityType } from '@/interface-models/Generic/EntityType';
import HolidayDetails from '@/interface-models/Generic/HolidayDetails/HolidayDetails';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import ClientPortalLoadedApplication from '@/interface-models/Generic/LoadedApplication/ClientPortalLoadedApplication';
import OperationsPortalLoadedApplication from '@/interface-models/Generic/LoadedApplication/OperationsPortalLoadedApplication';
import UserPortalLoadedApplication from '@/interface-models/Generic/LoadedApplication/UserPortalLoadedApplication';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { Portal } from '@/interface-models/Generic/Portal';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import { GenerateReportRequest } from '@/interface-models/Reporting/GenerateReportRequest';
import GenerateReportResponse from '@/interface-models/Reporting/GenerateReportResponse';
import UserRole from '@/interface-models/Roles/UserRoles';
import { ClientServiceRateVariationsStatus } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariationsStatus';
import StatusConfig from '@/interface-models/Status/StatusConfig';
import useAccountRecoveryStore from '@/store/modules/AccountRecoveryStore';
import { useAddressingStore } from '@/store/modules/AddressingStore';
import { useAdjustmentChargeStore } from '@/store/modules/AdjustmentChargeStore';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useClientDetailsStore } from '@/store/modules/ClientDetailsStore';
import { useClientInvoiceStore } from '@/store/modules/ClientInvoiceStore';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { useDataImportStore } from '@/store/modules/DataImportStore';
import { useDriverAppStore } from '@/store/modules/DriverAppStore';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useDriverMessageStore } from '@/store/modules/DriverMessageStore';
import { useEquipmentHireInvoiceStore } from '@/store/modules/EquipmentHireInvoiceStore';
import { useFleetAssetOwnerInvoiceStore } from '@/store/modules/FleetAssetOwnerInvoiceStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { useGpsStore } from '@/store/modules/GpsStore';
import { useInvoiceAdjustmentStore } from '@/store/modules/InvoiceAdjustmentStore';
import { useJobStatisticsStore } from '@/store/modules/JobStatisticsStore';
import { useJobStore } from '@/store/modules/JobStore';
import { useOperationsStore } from '@/store/modules/OperationsStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { useServiceRateVariationsStore } from '@/store/modules/ServiceRateVariationsStore';
import { useSupportTicketStore } from '@/store/modules/SupportTicketStore';
import { useUserManagementStore } from '@/store/modules/UserManagementStore';
import { useUserPortalStore } from '@/store/modules/UserPortalStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import moment from 'moment-timezone';
import { defineStore } from 'pinia';
import { ComputedRef, Ref, computed, ref } from 'vue';

export const useRootStore = defineStore('rootStore', () => {
  const isLoading: Ref<boolean> = ref(false);
  const persistentMessage: Ref<string> = ref('');
  const driverListUpdateTrigger: Ref<number> = ref(0);
  const additionalChargeItemList: Ref<AdditionalChargeItem[]> = ref([]);
  const additionalChargeTypeList: Ref<AdditionalChargeType[]> = ref([]);
  const roleList: Ref<UserRole[]> = ref([]);
  const statusTypeList: Ref<StatusConfig[]> = ref([]);
  const operationsPortalLoadedData: Ref<OperationsPortalLoadedApplication> =
    ref(new OperationsPortalLoadedApplication());
  const clientPortalLoadedData: Ref<ClientPortalLoadedApplication> = ref(
    new ClientPortalLoadedApplication(),
  );
  const userPortalLoadedData: Ref<UserPortalLoadedApplication> = ref(
    new UserPortalLoadedApplication(),
  );
  const holidayDetailsList: Ref<HolidayDetails[]> = ref([]);
  const dialogNotification: Ref<string[]> = ref([]);
  const isCompanyWideNotification: Ref<boolean> = ref(false);

  /**
   * Resets all state variables back to their default values. Called when
   * logging out or changing divisions
   */
  function resetState() {
    isLoading.value = false;
    persistentMessage.value = '';
    driverListUpdateTrigger.value = 0;
    additionalChargeItemList.value = [];
    roleList.value = [];
    additionalChargeTypeList.value = [];
    statusTypeList.value = [];
    operationsPortalLoadedData.value = new OperationsPortalLoadedApplication();
    clientPortalLoadedData.value = new ClientPortalLoadedApplication();
    userPortalLoadedData.value = new UserPortalLoadedApplication();
    holidayDetailsList.value = [];
    dialogNotification.value = [];
    isCompanyWideNotification.value = false;
  }

  /**
   * Computed property that returns whether all key data has been loaded for the
   * current portal. This is used to determine whether we should show the app
   * content or a splash screen.
   */
  const applicationHasLoaded: ComputedRef<boolean> = computed(() => {
    const portal: Portal = sessionManager.getPortalType();
    const loadedData:
      | OperationsPortalLoadedApplication
      | ClientPortalLoadedApplication
      | UserPortalLoadedApplication =
      portal === Portal.OPERATIONS
        ? operationsPortalLoadedData.value
        : portal === Portal.CLIENT
          ? clientPortalLoadedData.value
          : userPortalLoadedData.value;
    return Object.keys(loadedData).every((k: string) => (loadedData as any)[k]);
  });

  const startOfDayTomorrow: ComputedRef<number> = computed(() => {
    return moment()
      .tz(useCompanyDetailsStore().userLocale)
      .add(1, 'day')
      .startOf('day')
      .valueOf();
  });
  const endOfDayTomorrow: ComputedRef<number> = computed(() => {
    return moment()
      .tz(useCompanyDetailsStore().userLocale)
      .add(1, 'day')
      .endOf('day')
      .valueOf();
  });

  function setDataLoadedFalse() {
    const data: OperationsPortalLoadedApplication | any =
      operationsPortalLoadedData.value;
    Object.keys(data).forEach((v) => (data[v] = false));
  }

  /**
   * Resets all pinia stores to remove any data that is specific to the users
   * current division. Called when the user is switching divisions, logs out or
   * loses websocket connection.
   * @param resetCompanyDetails - whether to clear the CompanyDetails object in
   * the CompanyDetailsStore
   */
  function resetDivisionSpecificData(resetCompanyDetails: boolean = true) {
    useAccountRecoveryStore().resetState();
    useAddressingStore().resetState();
    useAdjustmentChargeStore().resetState();
    useAppNavigationStore().resetState();
    useClientDetailsStore().resetState();
    useClientInvoiceStore().resetState();
    useCompanyDetailsStore().resetState(resetCompanyDetails);
    useDataImportStore().resetState();
    useDriverAppStore().resetState();
    useDriverDetailsStore().resetState();
    useDriverMessageStore().resetState();
    useEquipmentHireInvoiceStore().resetState();
    useFleetAssetOwnerInvoiceStore().resetState();
    useFleetAssetOwnerStore().resetState();
    useFleetAssetStore().resetState();
    useGpsStore().resetState();
    useInvoiceAdjustmentStore().resetState();
    useJobStatisticsStore().resetState();
    useJobStore().resetState();
    useOperationsStore().resetState();
    useServiceRateStore().resetState();
    useSupportTicketStore().resetState();
    useUserManagementStore().resetState();
    useUserPortalStore().resetState();
    useWebsocketStore().resetState();
    sessionManager.clearSessionDetails();

    additionalChargeItemList.value = [];
    additionalChargeTypeList.value = [];
  }

  function setGlobalLoader(payload: boolean) {
    isLoading.value = payload;
  }

  function setDialogNotification(payload: string[]) {
    isCompanyWideNotification.value = false;
    dialogNotification.value = payload;
  }

  /**
   * Handle for company-level message. Used to display a notification for all
   * users within a company.
   * @param payload message to display
   */
  function handleCompanyWideMessage(payload: string | null) {
    if (!payload) {
      return;
    }
    isCompanyWideNotification.value = true;
    dialogNotification.value = [payload];
  }

  function setPersistentMessage(payload) {
    persistentMessage.value = payload;
  }

  /**
   * Request and response for getting a list of all AdditionalChargeItems as part
   * of the initial set of key data requests, and sets the result to the store.
   * Called on app mount.
   */
  async function requestAdditionalChargeItemList() {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/additionalChargeItem/get/listAll', null, false),
        'selectAdditionalChargeItemList',
      );
      if (result) {
        additionalChargeItemList.value = result.map((item) =>
          initialiseAdditionalChargeItem(item),
        );
        operationsPortalLoadedData.value.ADDITIONAL_CHARGES = true;
      }
      return result;
    } catch (error) {
      logConsoleError('Error requesting additional charge item list:', error);
      return null;
    }
  }

  /**
   * Request and response for getting a list of all AdditionalChargeTypes as part
   * of the initial set of key data requests, and sets the result to the store.
   * Called on app mount.
   */
  async function requestAdditionalChargeTypeList() {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/additionalChargeType/get/listAll', null, false),
        'selectAdditionalChargeTypeList',
      );
      if (result) {
        result.forEach((element: any) => {
          if (element.shortName === 'TOL') {
            element.allowCustomAmount = true;
          } else {
            element.allowCustomAmount = false;
          }
        });
        additionalChargeTypeList.value = result;
        operationsPortalLoadedData.value.ADDITIONAL_CHARGE_TYPES = true;
      }
      return result;
    } catch (error) {
      logConsoleError('Error requesting additional charge type list:', error);
      return null;
    }
  }

  /**
   * Sends request and handles response for the StatusConfig list. Sends to a
   * different endpoint depending on the value of isClientPortal. If the request
   * is successful, the statusTypeList is updated with the response.
   * @param isClientPortal boolean indicating whether the current user is in the
   * client portal
   * @returns the list of StatusConfig objects, or null if the request fails
   */
  async function requestStatusConfigList(): Promise<StatusConfig[] | null> {
    try {
      const isClientPortal = sessionManager.isClientPortal();
      // Set endpoint and responseId based on portal
      const endpoint = isClientPortal
        ? `/user/${sessionManager.getUserName()}/statusConfig/get/listAllForClient`
        : '/statusConfig/get/listAll';
      const responseId = isClientPortal
        ? 'selectStatusConfigListForClient'
        : 'selectStatusConfigList';
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(endpoint, null, false),
        responseId,
      );
      updateStatusConfigListState(result);
      return result;
    } catch (error) {
      logConsoleError('Error requesting status config list:', error);
      return null;
    }
  }

  /**
   * Handles the response when requesting the statusConfig list. If the response
   * is not null, the statusTypeList is updated with the response and the
   * loading variables are set to true.
   * @param statusConfigList response to statusConfig list request
   */
  function updateStatusConfigListState(
    statusConfigList: StatusConfig[] | null,
  ) {
    if (statusConfigList) {
      statusTypeList.value = statusConfigList;
      operationsPortalLoadedData.value.STATUS_LIST = true;
      clientPortalLoadedData.value.STATUS_LIST = true;
    }
  }

  /**
   * Request and response for getting all roles, called on app mount. Sets the
   * result to the roleList state.
   */
  async function requestRoleList() {
    try {
      // Set endpoint based on whether the request is originating from the
      // client portal or not
      const endpoint = sessionManager.isClientPortal()
        ? `/user/${sessionManager.getUserName()}/roleDetails/get/listAll`
        : '/roleDetails/get/listAll';
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(endpoint, null, false),
        'allRoleDetailList',
      );
      if (result) {
        roleList.value = result;
        operationsPortalLoadedData.value.USER_ROLES = true;
        clientPortalLoadedData.value.USER_ROLES = true;
      }
      return result;
    } catch (error) {
      logConsoleError('Error requesting role list:', error);
      return null;
    }
  }

  /**
   * Request and response for getting a list of all HolidayDetails documents as
   * part of the initial set of key data requests, and sets the result to the
   * store. Called on app mount.
   */
  async function requestHolidayDetailsList() {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/holidayDetails/get/listAll', null, false),
        'selectedHolidayList',
      );
      if (result) {
        holidayDetailsList.value = result;
      }
      return result;
    } catch (error) {
      logConsoleError('Error requesting holiday details list:', error);
      return null;
    }
  }

  /**
   * Saves holiday details.
   *
   * This function sends a request over a WebSocket to save holiday details.
   * If the save operation is successful, return result
   * else cath error
   *
   * @param {HolidayDetails} payload - The holiday details to save.
   * @returns {Promise<HolidayDetails | null>} The saved holiday details,
   * or null if the save operation fails.
   */
  async function saveHolidayDetails(
    payload: HolidayDetails,
  ): Promise<HolidayDetails | null> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/holidayDetails/save', payload, true),
        'savedHolidayDetails',
      );
      return result;
    } catch (error) {
      logConsoleError('Error saving holiday:', error);
      return null;
    }
  }

  /**
   * Handles the response for saving holiday details.
   * This function is only called in `globalEventListener` when the `savedHolidayDetails` event is received.
   *
   * It updates the `holidayDetailsList` by:
   * - Replacing an existing holiday if found (matching `_id`).
   * - Adding a new holiday if it doesn't already exist.
   *
   * @param {HolidayDetails} result - The saved holiday details received from the WebSocket event.
   */
  function handleSavedHolidayDetailsResponse(result: HolidayDetails | null) {
    if (result) {
      // Find existing holiday by _id
      const index = holidayDetailsList.value.findIndex(
        (holiday) => holiday._id === result._id,
      );

      if (index !== -1) {
        // If holiday exists, update it
        holidayDetailsList.value.splice(index, 1, result);
      } else {
        // If holiday does not exist, add it as a new entry
        holidayDetailsList.value.push(result);
      }
    }
  }

  /**
   * Handles a generic error message sent from the backend when an error occurs.
   * Display a notification with the message as the body.
   * @param error error message to display
   */
  function handleErrorResponse(error: string | null) {
    if (!error) {
      return;
    }
    // Check if the error message starts with 'Warning' (case insensitive)
    if (error.match(/^warning/i)) {
      showNotification(error, { type: HealthLevel.WARNING });
    } else {
      showNotification(error, { title: 'Error Occurred', duration: -1 });
    }
  }

  /**
   * Handles response to 'encodedReport' request. If the response is not null,
   * the report is downloaded.
   * @param report GenerateReportResponse object containing base64 encoded
   * report data
   */
  function decodeBase64ToPdf(report: GenerateReportResponse | null) {
    if (report) {
      if (report.encodedReport) {
        downloadReport(report);
      }
    } else {
      logConsoleError(
        'Error decoding base64 report response to pdf - payload was null',
      );
    }
  }

  /**
   * Sends request to generate a report. Called from ReportIndex.
   * @param request - contains report parameters and user-selected options
   * @param endpoint - the websocket endpoint to send the request to
   */
  async function generateReport(
    request: GenerateReportRequest,
    endpoint: string,
  ) {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/reporting/' + endpoint, request, true),
        'encodedReport',
      );
      return result;
    } catch (error) {
      logConsoleError('Error generating report:', error);
      return null;
    }
  }

  async function generateClientDailyJobReportsAndSendEmail(
    request: GenerateReportRequest,
  ) {
    try {
      // Send request over websocket
      const reportResponse = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/reporting/generateAndEmailDailyJobReports',
          request,
          true,
        ),
        'hasMultiClientsDailyJobsSubmitted',
        { timeout: 60000 },
      );
      if (reportResponse) {
        const healthLevel = !reportResponse.success
          ? HealthLevel.ERROR
          : reportResponse.failedClientIds.length > 0
            ? HealthLevel.WARNING
            : HealthLevel.INFO;
        showNotification(reportResponse.message, {
          type: healthLevel,
          title: 'Bulk Daily Jobs Email',
        });
      }
      return reportResponse;
    } catch (error) {
      logConsoleError('Error generating and sending daily job reports:', error);
      return null;
    }
  }

  function setCurrentInvoiceRunTypes(
    currentInvoiceRunTypeResponse: CurrentInvoiceRunTypeResponse | null,
  ) {
    if (!currentInvoiceRunTypeResponse) {
      return;
    }
    // Client Invoice Setters
    const clientInvoiceStore = useClientInvoiceStore();
    clientInvoiceStore.setInvoiceRunStatus(
      currentInvoiceRunTypeResponse.clientInvoice,
    );
    clientInvoiceStore.setWeekEndingDate(
      currentInvoiceRunTypeResponse.clientInvoiceWeekEndingDate,
    );
    clientInvoiceStore.setProcessingDate(
      currentInvoiceRunTypeResponse.clientInvoiceProcessingDate,
    );
    clientInvoiceStore.setBillingCycleIds(
      currentInvoiceRunTypeResponse.clientBillingCycleIds,
    );

    // fleet asset owner RCTI setters
    const fleetAssetOwnerInvoiceStore = useFleetAssetOwnerInvoiceStore();
    fleetAssetOwnerInvoiceStore.setInvoiceRunStatus(
      currentInvoiceRunTypeResponse.rcti,
    );
    fleetAssetOwnerInvoiceStore.setWeekEndingDate(
      currentInvoiceRunTypeResponse.rctiWeekEndingDate,
    );
    fleetAssetOwnerInvoiceStore.setProcessingDate(
      currentInvoiceRunTypeResponse.rctiProcessingDate,
    );
    fleetAssetOwnerInvoiceStore.setIncludeAdjustments(
      currentInvoiceRunTypeResponse.rctiIncludeAdjustments,
    );
    fleetAssetOwnerInvoiceStore.setBillingCycleIds(
      currentInvoiceRunTypeResponse.rctiBillingCycleIds,
    );

    // // equipment hire invoice setters
    const equipmentHireInvoiceStore = useEquipmentHireInvoiceStore();
    equipmentHireInvoiceStore.setInvoiceRunStatus(
      currentInvoiceRunTypeResponse.equipmentHire,
    );
    equipmentHireInvoiceStore.setWeekEndingDate(
      currentInvoiceRunTypeResponse.equipmentHireWeekEndingDate,
    );
    equipmentHireInvoiceStore.setProcessingDate(
      currentInvoiceRunTypeResponse.equipmentHireProcessingDate,
    );
    equipmentHireInvoiceStore.setIncludeAdjustments(
      currentInvoiceRunTypeResponse.equipmentHireIncludeAdjustments,
    );
    equipmentHireInvoiceStore.setBillingCycleIds(
      currentInvoiceRunTypeResponse.equipmentHireBillingCycleIds,
    );
  }

  /**
   * Requests the last booking dates for a given entity type and validates the
   * response based on the entity type. The validation is used for entityType
   * DRIVER and FLEET ASSET, where both components are rendered at the same time
   * and we don't know which response should be handled each component.
   * NOTE: This should be improved in future by changing the response payload
   *
   * @param request - contains the entityType we're requesting for, as well as
   * the dates to filter by
   * @returns the last booking dates for the given entity type, or null if the
   * request fails
   */
  async function requestLastBookingDates(request: {
    entityType: EntityType;
    startDate: number;
    endDate: number;
  }) {
    try {
      const validator = (
        entityType: EntityType,
        data: KeyValue[] | null,
      ): boolean => {
        if (!data) {
          return false;
        }
        const fleetAssetStore = useFleetAssetStore();
        const driverDetailsStore = useDriverDetailsStore();
        if (entityType === EntityType.FLEET_ASSET) {
          // Check that all non-empty results are in our fleet asset list
          return data
            .filter((d) => !!d.key)
            .every((x) => fleetAssetStore.fleetAssetSummaryMap.has(x.key));
        } else if (entityType === EntityType.DRIVER) {
          // Check that all non-empty results are in our driver list
          return data
            .filter((d) => !!d.key)
            .every((x) => driverDetailsStore.driverSummaryMap.has(x.key));
        } else {
          // Just return true if we're requesting for CLIENT, as there is no
          // race condition
          return true;
        }
      };
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/job/getLastBookingDates', request, true),
        'lastBookingDates',
        { mapResponse: (response) => validator(request.entityType, response) },
      );
      return result;
    } catch (error) {
      logConsoleError(
        `Error requesting last booking dates for ${request.entityType}: `,
        error,
      );
      return null;
    }
  }

  /**
   * Fetches key application data from various APIs, which will be stored in
   * pinia for use across the app.
   *
   * This function performs two types of requests: independent and dependent.
   *
   * Independent requests are those that do not depend on the result of any
   * other request. These requests are fired off all at once, in no particular
   * order.
   *
   * Dependent requests are those that must be performed in a specific sequence.
   * These requests are performed in the following order:
   * 1. Request client, driver, fleet asset, and service rate information.
   * 2. Request job-related requests that depend on the client, driver, fleet
   *    asset, and service rate information.
   * 3. Request the latest GPS positions, such that we can set information to
   *    the GPS data from results from steps 1 and 2.
   *
   * @async
   * @function getApplicationData
   * @throws {Error} If any of the requests fail, an error will be thrown.
   */
  async function getApplicationData() {
    // All API requests that can be performed at the same time, and don't have
    // any dependencies for other data
    const independentRequests = async () =>
      Promise.all([
        useFleetAssetOwnerStore().requestFleetAssetOwnerSummaryList(),
        requestRoleList(),
        requestAdditionalChargeItemList(),
        requestAdditionalChargeTypeList(),
        requestStatusConfigList(),
        useAdjustmentChargeStore().requestAdjustmentChargeList(),
        useAdjustmentChargeStore().requestAdjustmentChargeCategoryList(),
        requestHolidayDetailsList(),
        useDataImportStore().requestImportUserList(),
        useDataImportStore().requestApiUsersForCompany(),
        useDriverAppStore().requestLatestDriverOnlineStatuses(),
        useFleetAssetStore().initActiveHireContracts(),
        useCompanyDetailsStore().requestInsideMetroSuburbs(),
        useDriverMessageStore().requestChatMessageList(),
        useJobStatisticsStore().requestDivisionJobsSummary(),
        useCompanyDetailsStore().initActiveDefaultServiceRate(),
        useCompanyDetailsStore().initActiveDivisionFuelSurchargeRate(),
        useDriverAppStore().initDriverAppRestartMap(),
      ]);

    // All requests that must be performed in some specific sequence.
    // 1. Request client, driver, fleet asset and service rate information
    // 2. Request job related requests that depend on the client, driver, fleet
    //      asset and service rate information
    // 3. Request the latest GPS positions, such that we can set information to
    //      the GPS data from results from steps 1 and 2
    const dependentRequests = async () => {
      // Step 1.
      await Promise.all([
        useClientDetailsStore().requestClientSummaryList(),
        useFleetAssetStore().initialiseFleetAssetSummaryMap(),
        useDriverDetailsStore().requestDriverSummaryList(),
        useCompanyDetailsStore().requestServiceTypesList(),
      ]);
      // Step 2.
      await Promise.all([
        useDataImportStore().initUnassignedPudItemList(),
        useAccountRecoveryStore().getPendingAccountRecoveryDetails(),
        useJobStore().initialiseOperationsJobList(),
      ]);
      // Step 3.
      await useGpsStore().requestLatestGpsPositions();
      await useServiceRateVariationsStore().getDivisionRateVariationsByStatus(
        ClientServiceRateVariationsStatus.ALL,
      );
    };

    // Execute independent requests in parallel, and dependent requests in
    // sequence.
    await Promise.all([dependentRequests(), independentRequests()]);
  }

  /**
   * Saves an additional charge item. This function takes an additional charge
   * item as input and sends a save request over a WebSocket. If the company or
   * division ID is not provided in the payload, it defaults to the current
   * user's company and division ID. If the save is successful, it updates the
   * state of the additional charge item list with the result.
   *
   * @async
   * @param {AdditionalChargeItem} payload - The additional charge item to save.
   * @returns {Promise<AdditionalChargeItem|null>} The saved additional charge
   * item, or null if the save failed.
   * @throws {Error} If the WebSocket request fails, an error will be thrown and
   * logged to the console.
   */
  async function saveAdditionalChargeItem(
    payload: AdditionalChargeItem,
  ): Promise<AdditionalChargeItem | null> {
    try {
      payload.company ||= sessionManager.getCompanyId();
      payload.division ||= sessionManager.getDivisionId();
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/additionalChargeItem/save', payload, true),
        'savedAdditionalChargeItem',
      );
      return result;
    } catch (error) {
      logConsoleError('Error saving additional charge item:', error);
      return null;
    }
  }

  /**
   * Updates the state of the additional charge item list.
   *
   * This function takes an additional charge item as input and updates the
   * state of the additional charge item list. If the item already exists in the
   * list (determined by matching `_id`), it replaces the existing item with the
   * new one. If the item does not exist in the list, it adds the new item to
   * the end of the list.
   *
   * @param {AdditionalChargeItem} payload - The additional charge item to
   * update or add.
   */
  function updateAdditionalChargeItemListState(
    payload: AdditionalChargeItem | null,
  ): void {
    if (payload === null) {
      logConsoleError(
        'Saved additional charge item returned with null payload',
      );
      return;
    }
    const updatedChargeItem = initialiseAdditionalChargeItem(payload);
    const foundUpdatedItemIndex = additionalChargeItemList.value.findIndex(
      (chargeItem: AdditionalChargeItem) => chargeItem._id === payload._id,
    );
    if (foundUpdatedItemIndex !== -1) {
      additionalChargeItemList.value.splice(
        foundUpdatedItemIndex,
        1,
        updatedChargeItem,
      );
    } else {
      additionalChargeItemList.value.push(updatedChargeItem);
    }
  }

  /**
   * Sends request and handles response for fetching a paginated list of ledger
   * items. Used in the SearchLedger component, which is used in the ledger
   * screens, plus the client and owner administration screens
   * @param payload request object containing parameters which will construct
   * the mongo query
   * @returns
   */
  async function getLedgerSearchQuery(
    payload: SearchLedgerRequest,
  ): Promise<LedgerItemsResponse | null> {
    try {
      const endpoint = sessionManager.isClientPortal()
        ? `/user/${sessionManager.getUserName()}/ledgerDetails/query`
        : '/ledgerDetails/query';

      const responseId = sessionManager.isClientPortal()
        ? 'resultLedgerDetailsForClientPortal'
        : 'resultLedgerDetailsQuery';

      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(endpoint, payload, true),
        responseId,
      );

      return result;
    } catch (error) {
      logConsoleError('Error fetching ledger search query:', error);
      return null;
    }
  }

  /**
   * Saves an additional charge type.
   *
   * This function takes an additional charge type as input and sends a save
   * request over a WebSocket. If the company or division ID is not provided in
   * the payload, it defaults to the current user's company and division ID. If
   * the save is successful, it updates the state of the additional charge type
   * list with the result.
   *
   * @param {AdditionalChargeType} payload - The additional charge type to save.
   * @returns {Promise<AdditionalChargeType|null>} The saved additional charge
   * type, or null if the save failed.
   */
  async function saveAdditionalChargeType(
    payload: AdditionalChargeType,
  ): Promise<AdditionalChargeType | null> {
    try {
      payload.company ||= sessionManager.getCompanyId();
      payload.division ||= sessionManager.getDivisionId();
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/additionalChargeType/save', payload, true),
        'savedAdditionalChargeType',
      );
      return result;
    } catch (error) {
      logConsoleError('Error saving additional charge type:', error);
      return null;
    }
  }
  /**
   * Updates the state of the additional charge type list.
   *
   * This function takes an additional charge type as input and updates the
   * state of the additional charge type list. If the type already exists in the
   * list (determined by matching `_id`), it replaces the existing type with the
   * new one. If the type does not exist in the list, it adds the new type to
   * the end of the list.
   *
   * @function updateAdditionalChargeTypeListState
   * @param {AdditionalChargeType} payload - The additional charge type to
   * update or add.
   */
  function updateAdditionalChargeTypeListState(
    payload: AdditionalChargeType | null,
  ) {
    if (payload === null) {
      logConsoleError(
        'Saved additional charge type returned with null payload',
      );
      return;
    }
    const foundUpdatedItemIndex = additionalChargeTypeList.value.findIndex(
      (chargeItem: AdditionalChargeType) => chargeItem._id === payload._id,
    );
    if (foundUpdatedItemIndex !== -1) {
      additionalChargeTypeList.value.splice(foundUpdatedItemIndex, 1, payload);
    } else {
      additionalChargeTypeList.value.push(payload);
    }
  }

  /**
   * Sends request and handles response for fetching a DriverDeviceSnapshot
   * document from a driver's mobile device.
   * @param payload query object containing the driverId and query type
   * @returns the DriverDeviceSnapshot object containing information about the
   * driver's device, or null if the request fails
   */
  async function requestDriverDeviceInfo(
    payload: DriverDeviceQuery,
  ): Promise<DriverDeviceSnapshot | null> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/driverDeviceInfo/get', payload, true),
        'selectedDriverDeviceStatusSummary',
        { timeout: 30000 },
      );
      return result;
    } catch (error) {
      logConsoleError('Error requesting driver device info:', error);
      return null;
    }
  }

  /**
   * Sends request for pushing a DriverDeviceQuery message to a specified
   * driver's mobile app
   * @param payload  object containing the driverId and query type
   */
  function pushDriverDeviceEvent(payload: DriverDeviceQuery): void {
    try {
      // Send request over websocket
      useWebsocketStore().sendWebsocketRequest(
        new WebSocketRequest('/driverDeviceInfo/get', payload, true),
      );
    } catch (error) {
      logConsoleError('Error pushing driver device event:', error);
    }
  }

  /**
   * Fetches the audit history for all modules.
   * @param request request object containing query parameters
   * @returns object containing audit history for various modules. Returns null
   * if the request fails.
   */
  async function getFullAuditHistory(request: AuditRequest) {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/audit/get/fullAuditHistoryList', request, true),
        'fullAuditHistoryList',
      );
      return result;
    } catch (error) {
      logConsoleError('Error fetching full audit history:');
      return null;
    }
  }
  /**
   * Fetches the audit history for a single module and returns it.
   * @param request request object containing query parameters
   * @returns object containing lists of changes for the requested module.
   * Returns null if the request fails.
   */
  async function getAuditHistoryByAuditClassType(
    request: AuditRequest,
  ): Promise<any> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/audit/get/auditHistoryByAuditClassType',
          request,
          true,
        ),
        'auditHistoryListByAuditClassType',
      );
      return result;
    } catch (error) {
      logConsoleError(
        'Error fetching audit history by audit class type:',
        error,
      );
      return null;
    }
  }

  /**
   * Returns the mongo id of the toll admin and handling charge item. Used in
   * accounting calculations to identify the AdditionalChargeItem document for
   * Toll Admin & Handling.
   */
  const tollAdminAndHandlingId: ComputedRef<string | undefined> = computed(
    () => {
      const userDivision = sessionManager.getDivisionId();
      const foundTollAdminChargeItem = additionalChargeItemList.value.find(
        (x: AdditionalChargeItem) =>
          x.longName === 'Toll Admin & Handling' && x.division === userDivision,
      );
      return foundTollAdminChargeItem?._id;
    },
  );

  /**
   * Returns the mongo id of the toll charge type item. Used in accounting
   * calculations to identify the AdditionalChargeType document for tolls.
   */
  const tollChargeTypeId: ComputedRef<string | undefined> = computed(() => {
    const userDivision = sessionManager.getDivisionId();
    return additionalChargeTypeList.value.find(
      (x: AdditionalChargeType) =>
        x.longName === 'Tolls' && x.division === userDivision,
    )?._id;
  });

  return {
    isLoading,
    persistentMessage,
    driverListUpdateTrigger,
    additionalChargeItemList,
    roleList,
    additionalChargeTypeList,
    statusTypeList,
    operationsPortalLoadedData,
    clientPortalLoadedData,
    userPortalLoadedData,
    holidayDetailsList,
    dialogNotification,
    isCompanyWideNotification,
    applicationHasLoaded,
    startOfDayTomorrow,
    endOfDayTomorrow,
    tollAdminAndHandlingId,
    tollChargeTypeId,
    resetState,
    setDataLoadedFalse,
    resetDivisionSpecificData,
    saveHolidayDetails,
    setGlobalLoader,
    setDialogNotification,
    handleCompanyWideMessage,
    setPersistentMessage,
    generateReport,
    generateClientDailyJobReportsAndSendEmail,
    updateAdditionalChargeItemListState,
    updateAdditionalChargeTypeListState,
    requestStatusConfigList,
    updateStatusConfigListState,
    handleErrorResponse,
    decodeBase64ToPdf,
    setCurrentInvoiceRunTypes,
    requestLastBookingDates,
    getApplicationData,
    saveAdditionalChargeItem,
    getLedgerSearchQuery,
    saveAdditionalChargeType,
    requestDriverDeviceInfo,
    pushDriverDeviceEvent,
    getFullAuditHistory,
    getAuditHistoryByAuditClassType,
    requestRoleList,
    handleSavedHolidayDetailsResponse,
  };
});
