import {
  initialiseClientServiceRate,
  initialiseFleetAssetServiceRate,
} from '@/helpers/classInitialisers/InitialiseServiceRate';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  showNotification,
  showNotificationList,
} from '@/helpers/NotificationHelpers/NotificationHelpers';
import { CurrentClientServiceRateResponse } from '@/interface-models/Client/CurrentClientServiceRateResponse';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { ClientServiceRateVariations } from '@/interface-models/ServiceRates/Client/ServiceRateVariations/ClientServiceRateVariations';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import { FleetAssetServiceRateResponse } from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRateResponse';
import RateRequest from '@/interface-models/ServiceRates/RateRequest';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import { useServiceRateVariationsStore } from '@/store/modules/ServiceRateVariationsStore';
import { sessionManager } from '@/store/session/SessionState';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import { defineStore } from 'pinia';
import { Ref, ref } from 'vue';

export const useServiceRateStore = defineStore('serviceRateStore', () => {
  // Reactive state
  const activeDivisionServiceRate: Ref<ClientServiceRate | null> = ref(null);

  /**
   * Resets all state variables back to their default values. Called when
   * logging out or changing divisions
   */
  function resetState() {
    activeDivisionServiceRate.value = null;
  }

  /**
   * Saves a full ClientServiceRate document over websocket and listens for the
   * response. Returns the saved ClientServiceRate if successful, otherwise null.
   * @param clientServiceRate - The ClientServiceRate to save
   * @returns ClientServiceRate | null
   */
  async function saveClientServiceRates(
    clientServiceRate: ClientServiceRate,
  ): Promise<ClientServiceRate | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientServiceRate/save',
          clientServiceRate,
          true,
        ),
        'savedClientServiceRates',
      );
      return result;
    } catch (error) {
      logConsoleError('Error saving Client Service Rate', error);
      return null;
    }
  }

  /**
   * Requests a list of all ClientServiceRate for a given clientId.
   * Backend uses COMPANY and DIVISION from the token to query for a list of all
   * matching ClientServiceRate documents.
   * @param clientId clientId to match against.
   * @returns A promise that resolves to a list of ClientServiceRate, or
   * null if an error occurs.
   */
  async function getAllServiceRatesForClientId(
    clientId: string,
  ): Promise<ClientServiceRate[] | null> {
    try {
      let result: ClientServiceRate[] | null =
        await sendRequestAndListenForResponse(
          new WebSocketRequest('/clientServiceRate/getAll', clientId, false),
          'selectedAllClientServiceRatesList',
          {
            mapResponse: (response: ClientServiceRate[] | null) =>
              !response?.length || response?.[0]?.clientId === clientId,
          },
        );
      if (result) {
        result = result.map((rate) => initialiseClientServiceRate(rate));
      }
      return result;
    } catch (error) {
      logConsoleError('Error getting All Service Rates for Client Id', error);
      return null;
    }
  }

  /**
   * Requests all of the division default rate cards. Division rate cards are
   * denoted by a clientId of '0'.
   * @returns A promise that resolves to a list of ClientServiceRate, or
   * null if an error occurs.
   */
  async function getAllDivisionDefaultServiceRates(): Promise<
    ClientServiceRate[] | null
  > {
    return await getAllServiceRatesForClientId('0');
  }
  /**
   * Request all of the cash sale rate cards. Cash sale rate cards are denoted
   * by a clientId of 'CS'.
   * @returns A promise that resolves to a list of ClientServiceRate, or
   * null if an error occurs.
   */
  async function getAllCashSaleServiceRates(): Promise<
    ClientServiceRate[] | null
  > {
    return await getAllServiceRatesForClientId('CS');
  }

  /**
   *
   * @param clientId
   * @returns
   */
  async function getCurrentClientServiceRates(
    clientId: string,
  ): Promise<CurrentClientServiceRateResponse | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientServiceRate/getCurrentServiceRatesByClientId',
          clientId,
          false,
        ),
        'selectedCurrentClientServiceRates',
        {
          mapResponse: (response: CurrentClientServiceRateResponse | null) =>
            response?.clientId === clientId,
        },
      );
      if (result?.clientServiceRate) {
        // Init ClientServiceRate if result is not null
        result.clientServiceRate = initialiseClientServiceRate(
          result.clientServiceRate,
        );
      }
      return result;
    } catch (error) {
      logConsoleError('Error getting Current Client Service Rates', error);
      return null;
    }
  }

  async function getCurrentDivisionDefaultRates(): Promise<CurrentClientServiceRateResponse | null> {
    return await getCurrentClientServiceRates('0');
  }

  async function getCurrentCashSaleRates(): Promise<CurrentClientServiceRateResponse | null> {
    return await getCurrentClientServiceRates('CS');
  }

  // Replaces dispatch('getClientServiceRatesByTableId', payload);
  async function getClientServiceRatesByTableId(
    clientId: string,
    tableId: string,
  ): Promise<ClientServiceRate | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientServiceRate/getByTableId',
          [clientId, tableId],
          true,
        ),
        'selectedClientServiceRatesByTableId',
      );
      return result ? initialiseClientServiceRate(result) : null;
    } catch (error) {
      logConsoleError('Error getting Client Service Rates by Table Id', error);
      return null;
    }
  }

  async function getMergedClientServiceRates(
    clientId: string | undefined,
    searchDate: number,
  ): Promise<CurrentClientServiceRateResponse | null> {
    try {
      if (!clientId) {
        logConsoleError(
          'Client Id is required to get Merged Client Service Rate',
          clientId,
        );
        return null;
      }
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/clientServiceRate/getMergedServiceRatesByClientId',
          { clientId, searchDate },
          true,
        ),
        'selectedMergedClientServiceRates',
      );
      if (result?.clientServiceRate) {
        // Init ClientServiceRate if result is not null
        result.clientServiceRate = initialiseClientServiceRate(
          result.clientServiceRate,
        );
      }
      // Display error messages if any are present
      if (result?.recordedErrorMessages?.length) {
        showNotificationList(result.recordedErrorMessages, {
          title: 'Client Rate Card',
          type: HealthLevel.WARNING,
          duration: 2500 * result.recordedErrorMessages.length,
        });
      }
      return result;
    } catch (error) {
      logConsoleError('Error getting Merged Client Service Rate', error);
      return null;
    }
  }

  async function getMergedClientRatesOrDefault(
    clientId: string | undefined,
    searchDate: number,
  ): Promise<CurrentClientServiceRateResponse | null> {
    const mergedRates = await getMergedClientServiceRates(clientId, searchDate);
    if (mergedRates) {
      return mergedRates;
    }
    const defaultRates = getCurrentDivisionDefaultRates();
    if (!defaultRates) {
      showNotification(
        'There are no currently active division rates for this division. Please contact your administrator.',
        {
          title: 'No Active Division Rates',
        },
      );
    }
    return defaultRates;
  }

  /**
   * Retrieves the current service rates, fuel surcharge rates, and service rate
   * variations for a given client and date.
   *
   * Attempts to fetch the client's custom merged rate card; if unavailable,
   * falls back to division defaults. Also fetches the current fuel surcharge
   * rates and any service rate variations for the specified client and date.
   * Displays notifications if no applicable rate card or fuel surcharge is
   * found.
   *
   * @param clientId - The unique identifier of the client. Can be `undefined`.
   * @param searchDate - The date (as a timestamp) for which to retrieve the
   * rates.
   * @returns An object containing:
   *   - `clientRates`: The current client service rate response, or `null` if
   *     not found.
   *   - `clientFuelRates`: The current client fuel surcharge rates, or `null`
   *     if not found.
   *   - `serviceRateVariations`: The service rate variations for the client, or
   *     `null` if not found.
   */
  async function getCurrentRatesAndFuelForClientId(
    clientId: string | undefined,
    searchDate: number,
  ): Promise<{
    clientRates: CurrentClientServiceRateResponse | null;
    clientFuelRates: ClientFuelSurchargeRate[] | null;
    serviceRateVariations: ClientServiceRateVariations[] | null;
  }> {
    // Request client service rates. Try first to find client's custom merged
    // rate card. If we can't find that, use division defaults
    const [clientRates, clientFuelRates, serviceRateVariations] =
      await Promise.all([
        getMergedClientRatesOrDefault(clientId, searchDate),
        useFuelLevyStore().getCurrentClientFuelSurcharges(clientId, searchDate),
        useServiceRateVariationsStore().getServiceRateVariationsByClient(
          clientId,
          searchDate,
        ),
      ]);
    if (!clientRates) {
      showNotification('No applicable Rate Card found.');
    }
    if (!clientFuelRates) {
      showNotification('No applicable Fuel Surcharge found.');
    }
    return { clientRates, clientFuelRates, serviceRateVariations };
  }

  /**
   * Saves a full FleetAssetServiceRate document over websocket and listens for the
   * response. Returns the saved FleetAssetServiceRate if successful, otherwise null.
   * @param fleetAssetServiceRate - The FleetAssetServiceRate to save
   * @returns FleetAssetServiceRate | null
   */
  async function saveFleetAssetServiceRate(
    fleetAssetServiceRate: FleetAssetServiceRate,
  ): Promise<FleetAssetServiceRate | null> {
    try {
      fleetAssetServiceRate.company ||= sessionManager.getCompanyId();
      fleetAssetServiceRate.division ||= sessionManager.getDivisionId();
      fleetAssetServiceRate.rateTableSettings = [];
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/fleetAssetServiceRate/save',
          fleetAssetServiceRate,
          true,
        ),
        'savedFleetAssetServiceRates',
      );
      return result ? initialiseFleetAssetServiceRate(result) : null;
    } catch (error) {
      logConsoleError('Error saving Fleet Asset Service Rate', error);
      return null;
    }
  }

  async function getAllServiceRatesForFleetAssetId(
    fleetAssetId: string,
  ): Promise<FleetAssetServiceRate[] | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/fleetAssetServiceRate/getAll',
          fleetAssetId,
          false,
        ),
        'selectedAllFleetAssetServiceRatesList',
      );
      return result;
    } catch (error) {
      logConsoleError('Error getting Fleet Asset Service Rates', error);
      return null;
    }
  }

  // Replaces dispatch('getFleetAssetServiceRateByTableId', payload);
  async function getFleetAssetServiceRateByTableId(
    fleetAssetId: string,
    tableId: string,
  ): Promise<FleetAssetServiceRate | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/fleetAssetServiceRate/getByTableId',
          [fleetAssetId, tableId],
          true,
        ),
        'selectedFleetAssetServiceRatesByTableId',
      );
      return result ? initialiseFleetAssetServiceRate(result) : null;
    } catch (error) {
      logConsoleError(
        'Error getting Fleet Asset Service Rate by Table Id',
        error,
      );
      return null;
    }
  }

  /**
   * Fetches the active FleetAssetServiceRate for a given fleetAssetId and
   * searchDate. Called when we need to fetch the active service rates, such as
   * in the pricing screens.
   * @param fleetAssetId - The fleetAssetId to fetch the rates for
   * @param searchDate - The date to search for active rates (job date)
   * @returns - The active FleetAssetServiceRate, or null if an error occurs
   * or no active rates are found.
   */
  async function getActiveFleetAssetServiceRates(
    fleetAssetId: string,
    searchDate: number,
  ): Promise<FleetAssetServiceRate | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/fleetAssetServiceRate/getCurrentServiceRatesByFleetAssetId',
          new RateRequest(fleetAssetId, searchDate),
          true,
        ),
        'selectedCurrentFleetAssetServiceRates',
        {
          mapResponse: (response: FleetAssetServiceRate | null) =>
            !response || response?.fleetAssetId === fleetAssetId,
        },
      );
      return result ? initialiseFleetAssetServiceRate(result) : null;
    } catch (error) {
      logConsoleError('Error getting Active Fleet Asset Service Rates', error);
      return null;
    }
  }

  async function getAllDivisionDefaultFleetAssetServiceRates(): Promise<
    FleetAssetServiceRate[] | null
  > {
    return await getAllServiceRatesForFleetAssetId('0');
  }

  /**
   * Fetches the merged FleetAssetServiceRate for a given fleetAssetId and
   * searchDate. This is used in locations where, in addition to the fleet
   * asset's own rates
   * @param fleetAssetId - The fleetAssetId to fetch the rates for
   * @param searchDate - The date to search for active rates (job date)
   * @returns - FleetAssetServiceRateResponse containing the merged
   * FleetAssetServiceRate, or null if an error occurs or no active rates are
   * found.
   */
  async function getMergedFleetAssetServiceRates(
    fleetAssetId: string,
    searchDate: number,
  ): Promise<FleetAssetServiceRateResponse | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/fleetAssetServiceRate/getMergedServiceRatesByFleetAssetId',
          { fleetAssetId, searchDate },
          true,
        ),
        'selectedMergedFleetAssetServiceRates',
        {
          mapResponse: (response: FleetAssetServiceRateResponse | null) =>
            !response || response?.fleetAssetId === fleetAssetId,
        },
      );
      // Display error messages if any are present in the response
      if (result?.recordedErrorMessages?.length) {
        showNotificationList(result.recordedErrorMessages, {
          title: 'Fleet Asset Rate Card',
          type: HealthLevel.WARNING,
          duration: 2500 * result.recordedErrorMessages.length,
        });
      }

      if (result?.fleetAssetServiceRate) {
        result.fleetAssetServiceRate = initialiseFleetAssetServiceRate(
          result.fleetAssetServiceRate,
        );
      }
      return result;
    } catch (error) {
      logConsoleError('Error getting Merged Fleet Asset Service Rates', error);
      return null;
    }
  }

  async function updateClientServiceRateSummary(
    payload: ClientServiceRate,
  ): Promise<ClientServiceRate | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/clientServiceRate/updateSummary', payload, true),
        'updatedClientServiceRateSummary',
      );
      return result;
    } catch (error) {
      logConsoleError('Error updating Client Service Rate Summary', error);
      return null;
    }
  }

  async function updateFleetAssetServiceRateSummary(
    payload: FleetAssetServiceRate,
  ): Promise<FleetAssetServiceRate | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/fleetAssetServiceRate/updateSummary',
          payload,
          true,
        ),
        'updatedFleetAssetServiceRateSummary',
      );
      return result;
    } catch (error) {
      logConsoleError('Error updating Fleet Asset Service Rate Summary', error);
      return null;
    }
  }

  return {
    activeDivisionServiceRate,
    resetState,
    saveClientServiceRates,
    getAllServiceRatesForClientId,
    getCurrentClientServiceRates,
    getClientServiceRatesByTableId,
    getMergedClientServiceRates,
    saveFleetAssetServiceRate,
    getAllServiceRatesForFleetAssetId,
    getFleetAssetServiceRateByTableId,
    getActiveFleetAssetServiceRates,
    updateClientServiceRateSummary,
    updateFleetAssetServiceRateSummary,
    getAllDivisionDefaultServiceRates,
    getAllDivisionDefaultFleetAssetServiceRates,
    getAllCashSaleServiceRates,
    getCurrentDivisionDefaultRates,
    getCurrentCashSaleRates,
    getMergedClientRatesOrDefault,
    getCurrentRatesAndFuelForClientId,
    getMergedFleetAssetServiceRates,
  };
});
