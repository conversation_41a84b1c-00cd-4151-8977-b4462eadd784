import { DisplayCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import {
  returnFormattedDate,
  returnTimeNow,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { showNotification } from '@/helpers/NotificationHelpers/NotificationHelpers';
import { LedgerDetailsByInvoiceIdRequest } from '@/interface-models/Accounting/LedgerDetailsByInvoiceIdRequest';
import { LedgerPaginationRequest } from '@/interface-models/Accounting/LedgerPaginationRequest';
import { CompanyDetails } from '@/interface-models/Company/CompanyDetails';
import { DivisionCustomConfig } from '@/interface-models/Company/DivisionCustomConfig/DivisionCustomConfig';
import { DivisionOperationDetails } from '@/interface-models/Company/DivisionCustomConfig/Operations/DivisionOperationDetails';
import DivisionDetails from '@/interface-models/Company/DivisionDetails';
import LedgerItemsResponse from '@/interface-models/Generic/Accounting/Ledgers/LedgerItemsResponse';
import { KeyValue } from '@/interface-models/Generic/KeyValue/KeyValue';
import { HealthLevel } from '@/interface-models/Generic/NotificationMessage/HealthLevel';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import { ServiceTypeDisplayOrderMapping } from '@/interface-models/Generic/ServiceTypes/ServiceTypesDisplayOrder/ServiceTypeDisplayOrderMapping';
import { UpdateServiceTypesDisplayOrderRequest } from '@/interface-models/Generic/ServiceTypes/ServiceTypesDisplayOrder/UpdateServiceTypesDisplayOrderRequest';
import WebSocketRequest from '@/interface-models/Generic/WebSocketRequest/WebSocketRequest';
import InsideMetroSuburb from '@/interface-models/OutsideMetro/InsideMetroSuburb';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import { useAppNavigationStore } from '@/store/modules/AppNavigationStore';
import { useFuelLevyStore } from '@/store/modules/FuelLevyStore';
import { useRootStore } from '@/store/modules/RootStore';
import { useServiceRateStore } from '@/store/modules/ServiceRateStore';
import { useWebsocketStore } from '@/store/modules/WebsocketStore';
import { sessionManager } from '@/store/session/SessionState';
import { sendRequestAndListenForResponse } from '@/utils/api_sender';
import Mitt from '@/utils/mitt';
import moment from 'moment-timezone';
import { defineStore } from 'pinia';
import { v4 as uuidv4 } from 'uuid';
import { ComputedRef, Ref, computed, ref } from 'vue';

export const useCompanyDetailsStore = defineStore('companyDetailsStore', () => {
  const companyDetails: Ref<CompanyDetails | null> = ref(null);
  const companyDivisionNamesList: Ref<KeyValue[] | null> = ref(null);
  const userLocale: Ref<string> = ref('');
  const activeDepotState: Ref<string | null> = ref(null);
  const divisionDetails: Ref<DivisionDetails | null> = ref(null);
  const activeDivisionServiceRate: Ref<ClientServiceRate | null> = ref(null);
  const activeDivisionFuelSurchargeRate: Ref<
    (ClientFuelSurchargeRate & { isValidBaseDivisionRate: true }) | null
  > = ref(null);
  const insideMetroSuburbs: Ref<InsideMetroSuburb | null> = ref(null);
  const serviceTypesMap: Ref<Map<number, ServiceTypes>> = ref(new Map());

  const divisionDepotCoordinates: ComputedRef<[number, number]> = computed(
    () => {
      if (!companyDetails.value || !companyDetails.value.divisions) {
        return [0, 0];
      }
      const divisionInfo = companyDetails.value.divisions.find(
        (division: any) =>
          division.divisionId === sessionManager.getDivisionId(),
      );
      if (!divisionInfo) {
        return [0, 0];
      }
      return [
        divisionInfo.depotAddress.geoLocation[0],
        divisionInfo.depotAddress.geoLocation[1],
      ];
    },
  );

  const divisionCustomConfig: ComputedRef<DivisionCustomConfig | null> =
    computed(() => {
      const d1 = divisionDetails.value;
      let division: DivisionDetails | null = null;
      if (d1) {
        division = d1;
      } else {
        division =
          companyDetails.value &&
          companyDetails.value.divisions &&
          companyDetails.value.divisions[0]
            ? companyDetails.value.divisions[0]
            : null;
      }
      return division && division.customConfig ? division.customConfig : null;
    });

  /**
   * Resets all state variables back to their default values. Called when
   * logging out or changing divisions
   */
  function resetState(resetCompanyDetails: boolean) {
    // Reset CompanyDetailsModule values
    if (resetCompanyDetails) {
      companyDetails.value = null;
      companyDivisionNamesList.value = null;
      userLocale.value = '';
      divisionDetails.value = null;
    }
    activeDepotState.value = null;
    activeDivisionServiceRate.value = null;
    activeDivisionFuelSurchargeRate.value = null;
    insideMetroSuburbs.value = null;
    serviceTypesMap.value = new Map();
  }

  /**
   * Response for request to fetch the CompanyDetails object for the user's
   * current session, using values from the token. Called when the user authenticates.
   */
  async function getCompanyDetails(): Promise<CompanyDetails | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/companyDetails/retrieveDetails', null, false),
        'selectedCompanyDetails',
      );
      if (result) {
        setCompanyDetails(result);
      }
      return result;
    } catch (e) {
      console.error('Error retrieving Company Details', e);
      return null;
    }
  }

  /**
   * Handles response to initial request for CompanyDetails. Sets the full
   * object and key properties to the store, and requests the application data.
   * @param company - The CompanyDetails object to set
   */
  function setCompanyDetails(company: CompanyDetails) {
    if (company) {
      companyDetails.value = company;
      if (company.divisions && company.divisions[0]) {
        useRootStore().operationsPortalLoadedData.COMPANY_DETAILS = true;
        setDivisionDetails(company.divisions[0]);
        const depotState = company.divisions[0].depotAddress.state;
        if (depotState) {
          activeDepotState.value = depotState;
        }
        useRootStore().getApplicationData();
      }
    }
  }

  /**
   * Sets the divisionDetails object
   */
  function setDivisionDetails(division: DivisionDetails) {
    divisionDetails.value = division;
    userLocale.value = division.timezone || moment.tz.guess();

    // Set document title
    if (divisionDetails.value.divisionShortName) {
      const title = `${divisionDetails.value.divisionShortName} (powered by GoDesta)`;
      useAppNavigationStore().setPageTitle(title);
    }
  }

  /**
   * Fetches the active default service rate for the division, and sets it to
   * activeDivisionServiceRate. If no active default service rate is found, a
   * notification is shown.
   */
  async function initActiveDefaultServiceRate() {
    const currentDefaults =
      await useServiceRateStore().getCurrentDivisionDefaultRates();
    if (currentDefaults?.clientServiceRate) {
      activeDivisionServiceRate.value = currentDefaults.clientServiceRate;
    } else {
      showNotification(
        'There are no currently active division rates for this division. Please contact your administrator.',
        {
          title: 'No Active Division Rates',
        },
      );
    }
    useRootStore().operationsPortalLoadedData.SERVICE_RATE = true;
  }

  /**
   * Handler for division-level 'savedClientServiceRates' websocket
   * message in GlobalEventListener. Backend sends this message when a
   * division-level service rate is updated.
   *
   * Update activeDivisionServiceRate based on the validity and whether
   * it matches the current activeDivisionServiceRate.
   * @param serviceRate - The ClientServiceRate object to handle
   */
  function handleUpdatedDivisionServiceRate(
    serviceRate: ClientServiceRate | null,
  ) {
    const currentTime = returnTimeNow();

    // If the service rates are not division-level, return.
    if (!serviceRate?.clientId || serviceRate.clientId !== '0') {
      return;
    }

    // Case 1: Incoming serviceRate is the same as the activeDivisionServiceRate
    if (serviceRate._id === activeDivisionServiceRate.value?._id) {
      if (
        (serviceRate.validToDate ?? 0) < currentTime ||
        (serviceRate.validFromDate ?? 0) > currentTime
      ) {
        // If no longer valid, notify and set activeDivisionServiceRate to null
        showNotification(
          'The current Division Rate Card is no longer valid. Please contact your administrator.',
          {
            title: 'Division Rate Card Invalid',
            type: HealthLevel.WARNING,
          },
        );
        activeDivisionServiceRate.value = null;
      } else {
        // If still valid, notify of an update
        showNotification('The current Division Rate Card has been updated.', {
          title: 'Division Rate Card Updated',
          type: HealthLevel.INFO,
        });
        activeDivisionServiceRate.value = serviceRate;
      }
      return;
    }

    // Case 2: Incoming serviceRate is different from the activeDivisionServiceRate
    if (
      (serviceRate.validFromDate ?? 0) <= currentTime &&
      (serviceRate.validToDate ?? 0) >= currentTime
    ) {
      // If the incoming serviceRate is current, set it as active and notify
      activeDivisionServiceRate.value = serviceRate;
      showNotification(
        `The active Division Rate Card has changed to ${serviceRate.name}.`,
        {
          title: 'Division Rate Card Updated',
          type: HealthLevel.INFO,
        },
      );
    } else {
      // If the incoming serviceRate is not current, do nothing
      return;
    }
  }

  /**
   * Fetches the active default fuel surcharge rate for the division, and sets
   * it to activeDivisionFuelSurchargeRate. If no active default fuel surcharge
   * rate is found, a notification is shown.
   */
  async function initActiveDivisionFuelSurchargeRate() {
    const fuelSurchargeList =
      await useFuelLevyStore().getCurrentClientFuelSurcharges(
        '0',
        moment().valueOf(),
      );
    const baseDivisionFuelLevy = fuelSurchargeList?.find(
      (item) => item.isValidBaseDivisionRate === true,
    ) as
      | (ClientFuelSurchargeRate & { isValidBaseDivisionRate: true })
      | undefined;
    if (baseDivisionFuelLevy) {
      activeDivisionFuelSurchargeRate.value = baseDivisionFuelLevy;
    } else {
      showNotification(
        'There are no currently active division fuel surcharge rates for this division. Please contact your administrator.',
        {
          title: 'No Active Division Fuel Surcharge Rates',
        },
      );
    }
    useRootStore().operationsPortalLoadedData.FUEL_SURCHARGE = true;
  }

  /**
   * Handler for division-level 'savedClientFuelSurchargeRates' websocket
   * message in GlobalEventListener. Backend sends this message when a
   * division-level fuel surcharge is updated.
   *
   * Update activeDivisionFuelSurchargeRate based on the validity and whether
   * it matches the current activeDivisionFuelSurchargeRate.
   * @param fuelSurcharge - The ClientFuelSurchargeRate object to handle
   */
  function handleUpdatedDivisionFuelSurcharge(
    fuelSurcharge: ClientFuelSurchargeRate | null,
  ) {
    const currentTime = returnTimeNow();

    // If the fuelSurcharge is not a division-level surcharge, return.
    if (!fuelSurcharge?.clientIds.includes('0')) {
      return;
    }
    // Case 1: Incoming fuelSurcharge is the same as the activeDivisionFuelSurchargeRate
    if (fuelSurcharge.id === activeDivisionFuelSurchargeRate.value?.id) {
      // Check if the dates have been changed to be invalid, or if any settings
      // have changed to make the surcharge invalid as the base rate
      const isInvalidDate =
        (fuelSurcharge.validToDate ?? 0) < currentTime ||
        (fuelSurcharge.validFromDate ?? 0) > currentTime;
      const isValidBaseDivisionRate = fuelSurcharge.isValidBaseDivisionRate;
      if (isInvalidDate || !isValidBaseDivisionRate) {
        // If no longer valid, notify and set activeDivisionFuelSurchargeRate to null
        showNotification(
          'The current division fuel surcharge rate is no longer valid. Please contact your administrator.',
          {
            title: 'Division Fuel Surcharge Rate Invalid',
            type: HealthLevel.WARNING,
          },
        );
        activeDivisionFuelSurchargeRate.value = null;
      } else {
        // If still valid, notify of an update
        showNotification(
          'The current division fuel surcharge rate has been updated.',
          {
            title: 'Division Fuel Surcharge Rate Updated',
            type: HealthLevel.INFO,
          },
        );
        activeDivisionFuelSurchargeRate.value =
          fuelSurcharge as ClientFuelSurchargeRate & {
            isValidBaseDivisionRate: true;
          };
      }
      return;
    }

    // Case 2: Incoming fuelSurcharge is different from the activeDivisionFuelSurchargeRate
    const isValidForToday =
      (fuelSurcharge.validFromDate ?? 0) <= currentTime &&
      (fuelSurcharge.validToDate ?? 0) >= currentTime;
    const isValidBaseDivisionRate = fuelSurcharge.isValidBaseDivisionRate;
    if (isValidForToday && isValidBaseDivisionRate) {
      // If the incoming fuelSurcharge is current, set it as active and notify
      activeDivisionFuelSurchargeRate.value =
        fuelSurcharge as ClientFuelSurchargeRate & {
          isValidBaseDivisionRate: true;
        };
      showNotification(
        `The active Division Fuel Surcharge Rate has been updated to: ${
          fuelSurcharge.rateDescription
        } (${
          fuelSurcharge.validFromDate
            ? returnFormattedDate(fuelSurcharge.validFromDate)
            : 'N/A'
        } - ${
          fuelSurcharge.validToDate
            ? returnFormattedDate(fuelSurcharge.validToDate)
            : 'N/A'
        }}.`,
        {
          title: 'Division Fuel Surcharge Rate Updated',
          type: HealthLevel.INFO,
        },
      );
    } else {
      // If the incoming fuelSurcharge is not current, do nothing
      return;
    }
  }

  /**
   * Requests a list of the divisions within the users company. Sets the
   * response to the store.
   * @param forceRequest - If true, the request is made even if the list is
   * already populated.
   */
  function getCompanyDivisionNamesList(forceRequest: boolean = false) {
    // If company division names list i already populated and forceRequest is
    // not provided we aren't don't require to make another request for this
    // data.
    if (companyDivisionNamesList.value !== null && !forceRequest) {
      return;
    }
    companyDivisionNamesList.value = null;
    useWebsocketStore().sendWebsocketRequest(
      new WebSocketRequest('/companyDetails/listDivisions', null, false),
    );
    Mitt.on('divisionListForCompany', (divisionList: KeyValue[]) => {
      companyDivisionNamesList.value = divisionList;
      Mitt.off('divisionListForCompany');
    });
  }

  /**
   * Handles response for fetching the Division Details from the client portal,
   * using details from the token. Sets to the store for use on the client
   * portal screens.
   * @param division - The DivisionDetails object to set
   */
  function setClientUserDivisionDetails(division: DivisionDetails) {
    divisionDetails.value = division;
    useRootStore().clientPortalLoadedData.DIVISION_DETAILS = true;
    if (!companyDetails.value) {
      return;
    }
    companyDetails.value.divisions = [];
    companyDetails.value.divisions.push(division);

    // Set user locale from the division object
    userLocale.value = division.timezone;
  }

  /**
   * Sets the user's locale to the given timezone. Called when we're working in
   * a popout window and the timezone gets sent over BroadcastChannel
   */
  function setUserLocale(timezone: string) {
    userLocale.value = timezone;
  }

  /**
   * Fetches the inside metro suburbs data from the server. Called when the app
   * loads, and updates state with the result. Used when pricing jobs.
   * @returns {Promise<InsideMetroSuburb | null>} A promise that resolves to the
   * inside metro suburbs data, or null if an error occurs.
   */
  async function requestInsideMetroSuburbs(): Promise<InsideMetroSuburb | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/insideMetroSuburb/get', null, false),
        'selectedInsideMetroSuburb',
      );
      insideMetroSuburbs.value = result;
      useRootStore().operationsPortalLoadedData.INSIDE_METRO_SUBURBS = true;
      return result;
    } catch (e) {
      console.error('Error retrieving Inside Metro Suburbs', e);
      return null;
    }
  }

  /**
   * Saves the given InsideMetroSuburb document. Called when the user adds or
   * edits the suburb list. Updates state with the result.
   * @param {InsideMetroSuburb} insideMetroSuburb - The inside metro suburbs
   * data to save.
   * @returns {Promise<InsideMetroSuburb | undefined>} A promise that resolves
   * to the saved inside metro suburbs data, or undefined if an error occurs.
   */
  async function saveInsideMetroSuburbs(
    insideMetroSuburb: InsideMetroSuburb,
  ): Promise<InsideMetroSuburb | null | undefined> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/insideMetroSuburb/save',
          insideMetroSuburb,
          true,
        ),
        'savedInsideMetroSuburb',
      );
      if (result) {
        insideMetroSuburbs.value = result;
      }
      return result;
    } catch (e) {
      console.error('Error saving Inside Metro Suburbs', e);
    }
  }

  /**
   * Request and response for requesting a list of LedgerItems using a set of
   * query parameters within the LedgerPaginationRequest. Called on the ledger
   * screens
   * @param ledgerItemsRequest LedgerPaginationRequest different parameters for
   * ledgerType depending on which tab the user is requesting from.
   * @returns LedgerItemsResponse containing the LedgerItems and the total count
   * of matches, which will be used for pagination
   */
  async function requestLedgerItems(
    ledgerItemsRequest: LedgerPaginationRequest,
  ): Promise<LedgerItemsResponse | null> {
    try {
      // Send request over websocket
      return await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/ledgerDetails/get/listAllByPagination',
          ledgerItemsRequest,
          true,
        ),
        'selectedLedgerDetailsList',
      );
    } catch (error) {
      console.error('Error retrieving Ledger Items');
      return null;
    }
  }

  /**
   * Request and response for fetching a list of LedgerDetails using a list of
   * invoiceIds. Called from the Invoice Adjustment process where we want to
   * display a list of LedgerDetails associated with a particular invoice or
   * adjustments
   * @param request contains list of invoiceIds and also ledgerType
   * @returns list of LedgerDetails associated with the invoiceIds
   */
  async function requestLedgerItemsByInvoiceIds(
    request: LedgerDetailsByInvoiceIdRequest,
  ) {
    try {
      // Send request over websocket
      return await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/ledgerDetails/getLedgerDetailsByInvoiceIds',
          request,
          true,
        ),
        'selectedLedgerDetailsByInvoiceIdsList',
      );
    } catch (error) {
      console.error('Error retrieving Ledger Items by Invoice Ids');
      return null;
    }
  }

  /**
   * Requests the list of ServiceTypes for the users. Called on app mount. The
   * payload is empty for the request as the backend will determine the company
   * and division based on the user's token.
   * @returns {Promise<ServiceTypes[] | null | undefined>} A promise that
   * resolves to the list of ServiceTypes for the users company and division, or
   * null if an error occurs.
   */
  async function requestServiceTypesList(
    isClientPortal: boolean = false,
  ): Promise<ServiceTypes[] | null | undefined> {
    try {
      // Set endpoint and responseId based on portal
      const endpoint = isClientPortal
        ? `/user/${sessionManager.getUserName()}/serviceTypes/get/listAll`
        : '/serviceTypes/get/listAll';
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(endpoint, null, false),
        'getServiceTypeListResponse',
      );
      // Update the results in state
      if (result) {
        setServiceTypeList(result);
      }
      return result;
    } catch (e) {
      console.error('Error retrieving Service Types', e);
      return null;
    }
  }

  /**
   * Saves the provided service types list by sending a request to the server.
   * @param {ServiceTypes} serviceTypes - The service types object to be saved.
   * @returns {Promise<ServiceTypes>} A promise that returns
   * the saved serviceTypes if successful, return if an error occurs.
   */
  async function saveServiceTypesItem(
    serviceTypes: ServiceTypes,
  ): Promise<ServiceTypes | null> {
    try {
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(`/serviceTypes/save`, serviceTypes, true),
        'savedServiceType',
      );
      return result;
    } catch (error) {
      console.error('Failed to save service type:', error);
      return null;
    }
  }
  /**
   * Handles the response for saving a service type item.
   * This function is only called in `globalEventListener` when the `savedServiceType` event is received.
   *
   * It updates the `serviceTypesMap` by:
   * - Creating a new `Map` instance to maintain reactivity.
   * - Updating or adding the service type using `serviceTypeId` as the key.
   *
   * @param {ServiceTypes} result - The saved service type details received from the WebSocket event.
   */
  function handleSaveServiceTypesItemResponse(result: ServiceTypes | null) {
    if (result) {
      const newMap = new Map(serviceTypesMap.value);
      newMap.set(result.serviceTypeId, result);
      serviceTypesMap.value = newMap;
    }
  }

  /**
   * Processes response to websocket message 'getServiceTypeListResponse'
   * containing the list of ServiceTypes for the users company and division.
   * Used for both operations portal and client portal.
   * @param serviceTypesList incoming list of ServiceType documents to be saved
   */
  function setServiceTypeList(serviceTypesList: ServiceTypes[]) {
    const typesMap: Map<number, ServiceTypes> = new Map();
    serviceTypesList.forEach((s) => {
      typesMap.set(s.serviceTypeId, s);
    });
    serviceTypesMap.value = typesMap;
    useRootStore().operationsPortalLoadedData.SERVICE_TYPES = true;
    useRootStore().clientPortalLoadedData.SERVICE_TYPES = true;
  }

  /**
   * Returns the list of Service Types, sorted in ascending order based on
   * displayOrder.
   */
  const getServiceTypesList: ComputedRef<ServiceTypes[]> = computed(() => {
    const listItems = Array.from(serviceTypesMap.value.values());

    // Return listItems sorted in ascending order based on displayOrder
    return listItems.sort((a, b) => a.displayOrder - b.displayOrder);
  });

  /**
   * Request and response for updating the displayOrder of the ServiceTypes
   * documents for this company/division. Called from the
   * ServiceTypesAdministrationIndex component based on the emit from the
   * ServiceTypesOrderDialog component.
   *
   * @param mappings - The list of ServiceTypeDisplayOrderMapping objects
   * containing pairs of mongoIds and their new displayOrder values
   * @returns The updated list of ServiceTypes, or null if an error occurs
   */
  async function updateServiceTypesDisplayOrder(
    mappings: ServiceTypeDisplayOrderMapping[],
  ): Promise<UpdateServiceTypesDisplayOrderRequest | null> {
    try {
      const request: UpdateServiceTypesDisplayOrderRequest = {
        resId: uuidv4(),
        displayOrderMappings: mappings,
      };

      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest('/serviceTypes/updateDisplayOrder', request, true),
        'updatedServiceTypesDisplayOrder',
        { mapResponse: (response) => response?.resId === request.resId },
      );

      return result;
    } catch (error) {
      console.error('Error updating Service Types Display Order', error);
      return null;
    }
  }

  /**
   * Handles response to division-level message
   * 'updatedServiceTypesDisplayOrder', containing updated mappings for
   * ServiceTypes display order. Updates the ServiceTypesMap in the store.
   * @param response - payload containing a list of mappings between ServiceType
   * mongoIds and their new displayOrder values
   */
  function updateServiceTypesDisplayOrderResponse(
    response: UpdateServiceTypesDisplayOrderRequest | null,
  ) {
    if (!response?.displayOrderMappings) {
      return;
    }
    const currentList = getServiceTypesList.value;
    response.displayOrderMappings.forEach((mapping) => {
      const serviceType = currentList.find((s) => s._id === mapping._id);
      if (serviceType) {
        serviceType.displayOrder = mapping.displayOrder;
        serviceTypesMap.value.set(serviceType.serviceTypeId, serviceType);
      }
    });
    serviceTypesMap.value = new Map(serviceTypesMap.value);
  }

  /**
   * Request and response for updating the division's Operation Details, which
   * include default values for load and dropoff durations. Response is sent to
   * DIVISION subscription to update local state for all users.
   * @param request DivisionOperationDetails object containing the updated
   * operation details
   * @returns DivisionOperationDetails object containing the updated operation
   * details, or null if an error occurs
   */
  async function saveDivisionOperationDetails(
    request: DivisionOperationDetails,
  ): Promise<DivisionOperationDetails | null> {
    try {
      // Send request over websocket
      const result = await sendRequestAndListenForResponse(
        new WebSocketRequest(
          '/companyDetails/saveDivisionOperationDetails',
          request,
          true,
        ),
        'divisionOperationDetails',
      );
      return result;
    } catch (error) {
      console.error('Error saving Division Operation Details', error);
      return null;
    }
  }

  /**
   * Sets the operation details for the division in the company and division
   * details. Handles division-level response to update operation called in
   * saveDivisionOperationDetails
   *
   * @param  operationDetails - The operation
   * details to set. If this is null, the function returns without making any
   * changes.
   */
  function setDivisionOperationDetails(
    operationDetails: DivisionOperationDetails | null,
  ): void {
    if (!operationDetails) {
      return;
    }
    if (companyDetails.value?.divisions[0]?.customConfig) {
      companyDetails.value.divisions[0].customConfig.operations =
        operationDetails;
    }
    if (divisionDetails.value?.customConfig) {
      divisionDetails.value.customConfig.operations = operationDetails;
    }
  }

  /**
   * Request and response for sending a message which is displayed to all users
   * within a company. Only available to admin users.
   * @param message - The message to send to all users in the company
   * @returns A promise that resolves with the result of the message send, or
   * null if an error occurs
   */
  async function sendCompanyWideMessage(
    message: string,
  ): Promise<string | null> {
    try {
      if (!message) {
        throw new Error('Message cannot be empty');
      }
      // Send request over websocket
      return sendRequestAndListenForResponse(
        new WebSocketRequest('/message/sendCompanyWideMessage', message, false),
        'companyMessage',
      );
    } catch (error) {
      console.error('Error sending company wide message', error);
      return null;
    }
  }

  return {
    companyDetails,
    companyDivisionNamesList,
    userLocale,
    activeDepotState,
    divisionDetails,
    activeDivisionServiceRate,
    activeDivisionFuelSurchargeRate,
    insideMetroSuburbs,
    serviceTypesMap,
    divisionDepotCoordinates,
    divisionCustomConfig,
    resetState,
    getCompanyDetails,
    setCompanyDetails,
    setDivisionDetails,
    initActiveDefaultServiceRate,
    handleUpdatedDivisionServiceRate,
    initActiveDivisionFuelSurchargeRate,
    handleUpdatedDivisionFuelSurcharge,
    getCompanyDivisionNamesList,
    setDivisionOperationDetails,
    setClientUserDivisionDetails,
    setUserLocale,
    saveInsideMetroSuburbs,
    requestInsideMetroSuburbs,
    requestLedgerItems,
    requestLedgerItemsByInvoiceIds,
    setServiceTypeList,
    requestServiceTypesList,
    saveServiceTypesItem,
    getServiceTypesList,
    saveDivisionOperationDetails,
    sendCompanyWideMessage,
    updateServiceTypesDisplayOrder,
    updateServiceTypesDisplayOrderResponse,
    handleSaveServiceTypesItemResponse,
  };
});
