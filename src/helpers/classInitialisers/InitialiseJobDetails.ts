import { initialiseAdditionalChargeItem } from '@/helpers/classInitialisers/InitialiseAdditionalChargeItem';
import { initialisePudItem } from '@/helpers/classInitialisers/InitialisePudItem';
import { initialiseRateTableItems } from '@/helpers/classInitialisers/InitialiseRateTableItems';
import { deepCopy } from '@/helpers/JsonHelpers/JsonHelpers';
import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import { AdditionalChargeList } from '@/interface-models/Generic/Accounting/AdditionalChargeList';
import BreakDuration from '@/interface-models/Generic/Accounting/Break/BreakDuration';
import FinishedJobData from '@/interface-models/Generic/Accounting/FinishedJobDetails/FinishedJobData';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import { JobAccountingTotals } from '@/interface-models/Generic/Accounting/JobAccountingTotals/JobAccountingTotals';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { TimeTypeJobRateData } from '@/interface-models/Generic/Accounting/JobRateData/TimeTypeJobRateData';
import { StandbyDuration } from '@/interface-models/Generic/Accounting/Standby/StandbyDuration';
import Communication from '@/interface-models/Generic/Communication/Communication';
import ORSRoute from '@/interface-models/Generic/Route/ORSRoute';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';

export function initialiseJobDetails(jobDetails: JobDetails): JobDetails {
  const initialisedJob: JobDetails = Object.assign(
    new JobDetails(),
    deepCopy(jobDetails),
  );
  initialisedJob.accounting = initialiseJobAccountingDetails(
    initialisedJob.accounting,
  );
  if (initialisedJob.notes) {
    initialisedJob.notes.forEach(
      (note: Communication, index: number, noteList: Communication[]) => {
        noteList[index] = Object.assign(new Communication(), note);
      },
    );
  }
  if (initialisedJob.eventList) {
    initialisedJob.eventList.forEach(
      (event: JobStatusUpdate, index: number, eventList: JobStatusUpdate[]) => {
        eventList[index] = Object.assign(new JobStatusUpdate(), event);
      },
    );
  }

  initialisedJob.pudItems.forEach(
    (pud: PUDItem, index: number, pudArray: PUDItem[]) => {
      pudArray[index] = initialisePudItem(pud);
    },
  );
  initialisedJob.additionalAssets = initialisedJob.additionalAssets
    ? initialisedJob.additionalAssets
    : [];

  if (initialisedJob.plannedRoute) {
    initialisedJob.plannedRoute = Object.assign(
      new ORSRoute(),
      initialisedJob.plannedRoute,
    );
  }
  return initialisedJob;
}

export function initialiseJobAccountingDetails(
  accounting: JobAccountingDetails | null,
): JobAccountingDetails {
  // Because accounting can be set to null or have null values returned we
  // initialise the empty accounting details.
  if (
    !accounting ||
    Object.values(accounting).every((value) => value === null)
  ) {
    accounting = new JobAccountingDetails();
  }

  const initAccounting: JobAccountingDetails = Object.assign(
    new JobAccountingDetails(),
    deepCopy(accounting),
  );

  // If any fields are null, initialise them to their default values. This
  // occurs for jobs booked by imports or the scheduler.
  if (initAccounting.additionalCharges === null) {
    initAccounting.additionalCharges = new AdditionalChargeList();
  }

  if (initAccounting.additionalCharges.clientFuelSurcharge) {
    initAccounting.additionalCharges.clientFuelSurcharge =
      new ClientFuelSurchargeRate(
        initAccounting.additionalCharges.clientFuelSurcharge,
      );
  }
  if (initAccounting.additionalCharges.chargeList.length) {
    initAccounting.additionalCharges.chargeList.forEach(
      (charge, index, chargeList) => {
        chargeList[index] = initialiseAdditionalChargeItem(charge);
      },
    );
  }
  if (initAccounting.finishedJobData === null) {
    initAccounting.finishedJobData = new FinishedJobData();
  }
  if (initAccounting.totals === null) {
    initAccounting.totals = new JobAccountingTotals();
  }
  initAccounting.invoiceId ??= '';
  initAccounting.rctiId ??= '';

  // Init properties in clientRates
  if (initAccounting.clientRates && initAccounting.clientRates[0]) {
    initAccounting.clientRates[0] = Object.assign(
      new JobPrimaryRate(),
      initAccounting.clientRates[0],
    );
    initAccounting.clientRates[0].rate = initialiseRateTableItems(
      initAccounting.clientRates[0].rate,
    );
    initAccounting.clientRates[0].breakDuration = initialiseBreakDuration(
      initAccounting.clientRates[0].breakDuration,
    );
    initAccounting.clientRates[0].standbyDuration ??= new StandbyDuration();
  }
  // Init properties in fleetAssetRates
  if (initAccounting.fleetAssetRates && initAccounting.fleetAssetRates[0]) {
    initAccounting.fleetAssetRates[0] = Object.assign(
      new JobPrimaryRate(),
      initAccounting.fleetAssetRates[0],
    );
    initAccounting.fleetAssetRates[0].rate = initialiseRateTableItems(
      initAccounting.fleetAssetRates[0].rate,
    );
    initAccounting.fleetAssetRates[0].breakDuration = initialiseBreakDuration(
      initAccounting.fleetAssetRates[0].breakDuration,
    );
    initAccounting.fleetAssetRates[0].rateData = initRateData(
      initAccounting.fleetAssetRates[0].rateData,
    );
  }

  return initAccounting;
}

export function initialiseBreakDuration(
  breakDuration: BreakDuration,
): BreakDuration {
  const initBreakDuration = Object.assign(
    new BreakDuration(),
    JSON.parse(JSON.stringify(breakDuration)),
  );
  if (
    initBreakDuration.breakSummaryList &&
    initBreakDuration.breakSummaryList.length
  ) {
    initBreakDuration.breakSummaryList.forEach(
      (
        summary: WorkDiarySummary,
        index: number,
        summaryList: WorkDiarySummary[],
      ) => {
        summaryList[index] = Object.assign(new WorkDiarySummary(), summary);
      },
    );
  }
  return initBreakDuration;
}
export function initRateData(
  rateData: TimeTypeJobRateData[],
): TimeTypeJobRateData[] {
  if (rateData === null || rateData === undefined) {
    rateData = [];
  }
  rateData.forEach(
    (item: TimeTypeJobRateData, index: number, list: TimeTypeJobRateData[]) => {
      list[index] = Object.assign(new TimeTypeJobRateData(), item);
    },
  );
  return rateData;
}
