import {
  getDriverFromDriverId,
  getFleetAssetFromFleetAssetId,
  getOwnerFromOwnerId,
} from '@/helpers/JobDataHelpers/JobDataHelpers';
import { returnReadableClientStatus } from '@/helpers/StatusHelpers/StatusHelpers';
import { ClientRateExpirationSummaries } from '@/interface-models/Client/ClientDetails/ClientRateExpirationSummary';
import ClientSearchSummary from '@/interface-models/Client/ClientDetails/ClientSearchSummary';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import DriverExpirationSummaries from '@/interface-models/Driver/DriverDetails/Summary/DriverExpirationSummaries';
import { FleetAssetExpirationSummaries } from '@/interface-models/FleetAsset/Summary/FleetAssetExpirationSummaries';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import { FleetAssetOwnerExpirationSummaries } from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerExpirationSummaries';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import { EntityType } from '@/interface-models/Generic/EntityType';
import {
  ExpiringDocumentType,
  RateExpirationSummary,
  RateExpirationSummaryGroup,
} from '@/interface-models/ServiceRates/RateExpirationSummary';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';

export interface DashboardTableData {
  entityType: EntityType;
  entityId: string;
  name: string;
  secondaryName?: string;
  contactNumber: string;
  status: string;
}
export interface DashboardJobTableData {
  jobId: number;
  displayId: string | number;
  clientId: string;
  clientName: string;
  driverId: string;
  driverName: string;
  driverMob: string;
  fleetAssetId: string;
  csrAssignedId: string;
  owner: string;
  ownerMob: string;
  ownerId: string;
}

/**
 * Combines the expiration summaries based on the given summary details.
 *
 * @param summaryDetails - The expiration summary details.
 * @returns An array of combined rate expiration summaries.
 */
export function combineExpirationSummaries(
  summaryDetails:
    | {
        type: EntityType.FLEET_ASSET;
        expirationSummary?: FleetAssetExpirationSummaries | null;
        entityId: string;
      }
    | {
        type: EntityType.DRIVER;
        expirationSummary?: DriverExpirationSummaries | null;
        entityId: string;
      }
    | {
        type: EntityType.FLEET_ASSET_OWNER;
        expirationSummary?: FleetAssetOwnerExpirationSummaries | null;
        entityId: string;
      }
    | {
        type: EntityType.CLIENT;
        expirationSummary?: ClientRateExpirationSummaries | null;
        entityId: string;
      },
): RateExpirationSummary[] {
  if (!summaryDetails.expirationSummary) {
    return [];
  }
  // Get current time in ms since epoch
  const now = new Date().getTime();
  const sevenDaysFromNow = now + 7 * 24 * 60 * 60 * 1000;

  if (summaryDetails.type === EntityType.FLEET_ASSET) {
    const es = summaryDetails.expirationSummary;
    return [
      es.registrationSummary
        ? {
            ...es.registrationSummary,
            priority:
              !es.registrationSummary.validToDate ||
              es.registrationSummary.validToDate < now
                ? 1
                : es.registrationSummary.validToDate < sevenDaysFromNow
                  ? 2
                  : 3,
            entityId: summaryDetails.entityId,
            expirationType: ExpiringDocumentType.REGISTRATION,
          }
        : null,

      ...(es.insuranceSummary
        ? es.insuranceSummary.map((i) => {
            return {
              ...i,
              name:
                (i.validToDate
                  ? `Insurance ${
                      i.validToDate < now ? 'Expired' : 'Expiring Soon'
                    }: `
                  : 'No Valid Insurance: ') + i.name,
              priority:
                !i.validToDate || i.validToDate < now
                  ? 1
                  : i.validToDate < sevenDaysFromNow
                    ? 2
                    : 3,

              entityId: summaryDetails.entityId,
              expirationType: ExpiringDocumentType.INSURANCE,
            };
          })
        : []),
      ...(es.additionalEquipmentSummary
        ? es.additionalEquipmentSummary.map((a) => {
            return {
              ...a,
              priority:
                !a.validToDate || a.validToDate < now
                  ? 1
                  : a.validToDate < sevenDaysFromNow
                    ? 2
                    : 3,
              entityId: summaryDetails.entityId,
              expirationType: ExpiringDocumentType.ADDITIONAL_EQUIPMENT,
            };
          })
        : []),
      es.hireContractSummary
        ? {
            ...es.hireContractSummary,
            priority:
              !es.hireContractSummary.validToDate ||
              es.hireContractSummary.validToDate < now
                ? 1
                : es.hireContractSummary.validToDate < sevenDaysFromNow
                  ? 2
                  : 3,
            entityId: summaryDetails.entityId,
            expirationType: ExpiringDocumentType.HIRE_CONTRACT,
          }
        : null,
      es.serviceRateSummary
        ? {
            ...es.serviceRateSummary,
            name:
              'Rate Card: ' +
              (es.serviceRateSummary.name
                ? es.serviceRateSummary.name
                : 'None Active'),
            priority:
              !es.serviceRateSummary.validToDate ||
              es.serviceRateSummary.validToDate < now
                ? 1
                : es.serviceRateSummary.validToDate < sevenDaysFromNow
                  ? 2
                  : 3,
            entityId: summaryDetails.entityId,
            expirationType: ExpiringDocumentType.SERVICE_RATE,
          }
        : null,
      es.fuelSurchargeSummary
        ? {
            ...es.fuelSurchargeSummary,
            name:
              'Fuel Surcharge: ' +
              (es.fuelSurchargeSummary.name
                ? es.fuelSurchargeSummary.name
                : 'None Active'),
            priority:
              !es.fuelSurchargeSummary.validToDate ||
              es.fuelSurchargeSummary.validToDate < now
                ? 1
                : es.fuelSurchargeSummary.validToDate < sevenDaysFromNow
                  ? 2
                  : 3,
            entityId: summaryDetails.entityId,
            expirationType: ExpiringDocumentType.FUEL_SURCHARGE,
          }
        : null,
      es.divisionRateTableReferencesSummary
        ? {
            ...es.divisionRateTableReferencesSummary,
            name: es.divisionRateTableReferencesSummary.validToDate
              ? `Division Rate Card Configuration ${
                  es.divisionRateTableReferencesSummary.validToDate < now
                    ? 'Expired'
                    : 'Expiring Soon'
                }`
              : 'No Active Division Rate Card Configured',
            priority:
              !es.divisionRateTableReferencesSummary.validToDate ||
              es.divisionRateTableReferencesSummary.validToDate < now
                ? 1
                : es.divisionRateTableReferencesSummary.validToDate <
                    sevenDaysFromNow
                  ? 2
                  : 3,
            entityId: summaryDetails.entityId,
            expirationType: ExpiringDocumentType.DEFAULTS_CONFIGURATION,
          }
        : null,
    ]
      .filter((x) => !!x)
      .sort((a, b) => {
        const validToDateA = a!.validToDate || 0;
        const validToDateB = b!.validToDate || 0;
        return validToDateA - validToDateB;
      }) as RateExpirationSummary[];
  } else if (summaryDetails.type === EntityType.DRIVER) {
    const es = summaryDetails.expirationSummary;
    // Combine to single list, adding prefixes to names to be more descriptive
    // in the table
    return [
      ...(es.licenceSummary
        ? es.licenceSummary
            .filter((i) => !!i)
            .map((i) => {
              return {
                ...i,
                name:
                  (i.validToDate
                    ? `Licence ${
                        i.validToDate < now ? 'Expired' : 'Expiring Soon'
                      }: `
                    : '') + i.name,
                priority:
                  !i.validToDate || i.validToDate < now
                    ? 1
                    : i.validToDate < sevenDaysFromNow
                      ? 2
                      : 3,
                entityId: summaryDetails.entityId,
                expirationType: ExpiringDocumentType.LICENCE,
              };
            })
        : []),
      ...(es.inductionSummary
        ? es.inductionSummary
            .filter((i) => !!i)
            .map((i) => {
              return {
                ...i,
                name:
                  (i.validToDate
                    ? `Induction ${
                        i.validToDate < now ? 'Expired' : 'Expiring Soon'
                      }: `
                    : 'No Valid Induction: ') + i.name,
                priority:
                  !i.validToDate || i.validToDate < now
                    ? 1
                    : i.validToDate < sevenDaysFromNow
                      ? 2
                      : 3,
                entityId: summaryDetails.entityId,
                expirationType: ExpiringDocumentType.INDUCTION,
              };
            })
        : []),
    ].sort((a, b) => {
      const validToDateA = a!.validToDate || 0;
      const validToDateB = b!.validToDate || 0;
      return validToDateA - validToDateB;
    });
  } else if (summaryDetails.type === EntityType.FLEET_ASSET_OWNER) {
    const es = summaryDetails.expirationSummary;
    // Combine to single list, adding prefixes to names to be more descriptive
    // in the table
    return es.insuranceSummary
      ? es.insuranceSummary
          .filter((i) => !!i)
          .map((i) => {
            return {
              ...i,
              name:
                (i.validToDate
                  ? 'Insurance Expiring Soon: '
                  : 'No Valid Insurance: ') + i.name,
              priority:
                !i.validToDate || i.validToDate < now
                  ? 1
                  : i.validToDate < sevenDaysFromNow
                    ? 2
                    : 3,
              entityId: summaryDetails.entityId,
              expirationType: ExpiringDocumentType.INSURANCE,
            };
          })
          .sort((a, b) => {
            const validToDateA = a!.validToDate || 0;
            const validToDateB = b!.validToDate || 0;
            return validToDateA - validToDateB;
          })
      : [];
  } else if (summaryDetails.type === EntityType.CLIENT) {
    // Combine to single list, adding prefixes to names to be more descriptive
    // in the table
    const es = summaryDetails.expirationSummary;
    return (
      [
        es.serviceRateSummary
          ? {
              ...es.serviceRateSummary,
              // name:
              //   (es.serviceRateSummary.validToDate
              //     ? `Rate Card ${
              //         es.serviceRateSummary.validToDate < now
              //           ? 'Expired'
              //           : 'Expiring Soon'
              //       }: `
              //     : 'No Valid Rate Card: ') + es.serviceRateSummary.name,
              name:
                'Rate Card: ' +
                (es.serviceRateSummary.name
                  ? es.serviceRateSummary.name
                  : 'None Active'),
              priority:
                !es.serviceRateSummary.validToDate ||
                es.serviceRateSummary.validToDate < now
                  ? 1
                  : es.serviceRateSummary.validToDate < sevenDaysFromNow
                    ? 2
                    : 3,
              entityId: summaryDetails.entityId,
              expirationType: ExpiringDocumentType.SERVICE_RATE,
            }
          : null,
        es.fuelSurchargeSummary
          ? {
              ...es.fuelSurchargeSummary,
              // name:
              //   (es.fuelSurchargeSummary.validToDate
              //     ? `Fuel Surcharge ${
              //         es.fuelSurchargeSummary.validToDate < now
              //           ? 'Expired'
              //           : 'Expiring Soon'
              //       }: `
              //     : 'No Valid Fuel Surcharge: ') + es.fuelSurchargeSummary.name,
              name:
                'Fuel Surcharge: ' +
                (es.fuelSurchargeSummary.name
                  ? es.fuelSurchargeSummary.name
                  : 'None Active'),
              priority:
                !es.fuelSurchargeSummary.validToDate ||
                es.fuelSurchargeSummary.validToDate < now
                  ? 1
                  : es.fuelSurchargeSummary.validToDate < sevenDaysFromNow
                    ? 2
                    : 3,
              entityId: summaryDetails.entityId,
              expirationType: ExpiringDocumentType.FUEL_SURCHARGE,
            }
          : null,
        es.defaultsConfigurationSummary
          ? {
              ...es.defaultsConfigurationSummary,
              name: es.defaultsConfigurationSummary.validToDate
                ? `Division Rate Card Configuration ${
                    es.defaultsConfigurationSummary.validToDate < now
                      ? 'Expired'
                      : 'Expiring Soon'
                  }`
                : 'No Active Division Rate Card Configured',
              // name:
              //   'Defaults Configuration: ' +
              //   (es.defaultsConfigurationSummary.name
              //     ? es.defaultsConfigurationSummary.name
              //     : 'None Active'),
              priority:
                !es.defaultsConfigurationSummary.validToDate ||
                es.defaultsConfigurationSummary.validToDate < now
                  ? 1
                  : es.defaultsConfigurationSummary.validToDate <
                      sevenDaysFromNow
                    ? 2
                    : 3,
              entityId: summaryDetails.entityId,
              expirationType: ExpiringDocumentType.DEFAULTS_CONFIGURATION,
            }
          : null,
        es.serviceRateVariationsSummary
          ? {
              ...es.serviceRateVariationsSummary,
              priority:
                !es.serviceRateVariationsSummary.validToDate ||
                es.serviceRateVariationsSummary.validToDate < now
                  ? 1
                  : es.serviceRateVariationsSummary.validToDate <
                      sevenDaysFromNow
                    ? 2
                    : 3,
              entityId: summaryDetails.entityId,
              expirationType: ExpiringDocumentType.SERVICE_RATE_VARIATIONS,
            }
          : null,
      ].filter((x) => !!x) as RateExpirationSummary[]
    ).sort((a, b) => {
      const validToDateA = a!.validToDate || 0;
      const validToDateB = b!.validToDate || 0;
      return validToDateA - validToDateB;
    });
  }

  return [];
}

/**
 * Retrieves the related expiration summaries for the given driver IDs and fleet
 * asset IDs.
 *
 * @param driverIds - An array of driver IDs.
 * @param fleetAssetIds - An array of fleet asset IDs.
 * @returns An array of objects representing the related expiration summaries.
 */
export function getRelatedExpirationSummariesForOwner(
  fleetAssetIds: string[],
  driverIds: string[],
) {
  const fleetAssets: RateExpirationSummaryGroup[] = (
    fleetAssetIds
      .map((fid) => getFleetAssetFromFleetAssetId(fid))
      .filter((fa) => fa !== undefined) as FleetAssetSummary[]
  )
    .map((fa) => {
      return {
        id: fa.fleetAssetId,
        name: `${fa.csrAssignedId} - ${fa.registrationNumber} (${
          fa.fleetAssetTypeId === 1 ? 'Truck' : 'Trailer'
        })`,
        summaries: combineExpirationSummaries({
          type: EntityType.FLEET_ASSET,
          expirationSummary: fa.expirationSummaries,
          entityId: fa.fleetAssetId,
        }),
        entityType: EntityType.FLEET_ASSET,
        entityId: fa.fleetAssetId,
      };
    })
    .filter((fa) => fa.summaries.length > 0);
  const drivers = (
    driverIds
      .map((did) => getDriverFromDriverId(did))
      .filter((d) => d !== undefined) as DriverDetailsSummary[]
  )
    .map((d) => {
      return {
        id: d.driverId,
        name: d.displayName,
        summaries: combineExpirationSummaries({
          type: EntityType.DRIVER,
          expirationSummary: d.expirationSummaries,
          entityId: d.driverId,
        }),
        entityType: EntityType.DRIVER,
        entityId: d.driverId,
      };
    })
    .filter((d) => d.summaries.length > 0);
  return [...fleetAssets, ...drivers];
}

/**
 * For the provided entity details, map the entity from their original states to
 * a common table data format. Used in the Subcontractor Administration
 * Dashboard component.
 * @param entityDetails The entity details to be mapped.
 * @returns The mapped dashboard table data.
 */
export function mapEntityToTableData(
  entityDetails:
    | {
        type: EntityType.CLIENT;
        entity?: ClientSearchSummary;
      }
    | {
        type: EntityType.DRIVER;
        entity?: DriverDetailsSummary;
      }
    | {
        type: EntityType.FLEET_ASSET;
        entity?: FleetAssetSummary;
      }
    | {
        type: EntityType.FLEET_ASSET_OWNER;
        entity?: FleetAssetOwnerSummary;
      },
): DashboardTableData | undefined {
  if (!entityDetails.entity) {
    return;
  }
  if (entityDetails.type === EntityType.CLIENT) {
    return {
      entityType: EntityType.CLIENT,
      entityId: entityDetails.entity.clientId,
      name: entityDetails.entity.tradingName
        ? entityDetails.entity.tradingName
        : entityDetails.entity.clientName,
      contactNumber: '-',
      status: returnReadableClientStatus(entityDetails.entity.statusList),
    };
  } else if (entityDetails.type === EntityType.DRIVER) {
    const fleetAssetStore = useFleetAssetStore();
    const associatedFleetAssets: string = Array.from<FleetAssetSummary>(
      fleetAssetStore.fleetAssetSummaryMap.values(),
    )
      .filter((fa: FleetAssetSummary) =>
        fa.associatedDrivers.includes(entityDetails.entity!.driverId),
      )
      .map((fam: FleetAssetSummary) => fam.csrAssignedId)
      .join(', ');
    return {
      entityType: EntityType.DRIVER,
      entityId: entityDetails.entity.driverId,
      name: entityDetails.entity.displayName,
      secondaryName: associatedFleetAssets,
      contactNumber: entityDetails.entity.mobile
        ? entityDetails.entity.mobile
        : '-',
      status: entityDetails.entity.isActive ? 'Active' : 'Inactive',
    };
  } else if (entityDetails.type === EntityType.FLEET_ASSET) {
    const owner = getOwnerFromOwnerId(entityDetails.entity.fleetAssetOwnerId);
    return {
      entityType: EntityType.FLEET_ASSET,
      entityId: entityDetails.entity.fleetAssetId,
      name: `${entityDetails.entity.csrAssignedId} - ${
        entityDetails.entity.registrationNumber
          ? entityDetails.entity.registrationNumber
          : !!owner && owner.isOutsideHire
            ? 'Outside Hire'
            : 'No Registration'
      }`,
      secondaryName: owner ? owner.name : '',
      contactNumber: owner && owner.mobile ? owner.mobile : 'Unknown',
      status: entityDetails.entity.isActive ? 'Active' : 'Inactive',
    };
  } else if (entityDetails.type === EntityType.FLEET_ASSET_OWNER) {
    return {
      entityType: EntityType.FLEET_ASSET_OWNER,
      entityId: entityDetails.entity.ownerId,
      name: entityDetails.entity.name,
      contactNumber: entityDetails.entity.mobile
        ? entityDetails.entity.mobile
        : '-',
      status: entityDetails.entity.isActive ? 'Active' : 'Inactive',
    };
  }
}
