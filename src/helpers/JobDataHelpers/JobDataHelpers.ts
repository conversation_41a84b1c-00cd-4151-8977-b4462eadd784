import { initialisePudItem } from '@/helpers/classInitialisers/InitialisePudItem';
import {
  returnEarliestBookingTime,
  returnHoursAfterStartOfDay,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { PickUpDropOffSuburb } from '@/interface-models/Accounting/PickUpDropOffSuburb';
import ClientDetails from '@/interface-models/Client/ClientDetails/ClientDetails';
import ClientReferenceDetails from '@/interface-models/Client/ClientDetails/References/ClientReferenceDetails';
import DriverDetailsSummary from '@/interface-models/Driver/DriverDetails/Summary/DriverDetailsSummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import FleetAssetOwnerSummary from '@/interface-models/FleetAssetOwner/Summary/FleetAssetOwnerSummary';
import ChargeBreakdown from '@/interface-models/Generic/Accounting/JobAccountingTotals/ChargeBreakdown';
import ChargeTypeSubtotal from '@/interface-models/Generic/Accounting/JobAccountingTotals/ChargeTypeSubtotal';
import JobAccountingMargin from '@/interface-models/Generic/Accounting/JobAccountingTotals/JobAccountingMargin';
import countries from '@/interface-models/Generic/Addressing/Country';
import AddressAU from '@/interface-models/Generic/Addressing/CountryAddressTypes/AU/AddressAU';
import AttachmentTypes from '@/interface-models/Generic/Attachment/AttachmentTypes';
import ChatMessage from '@/interface-models/Generic/ChatConversation/ChatMessage';
import Communication from '@/interface-models/Generic/Communication/Communication';
import { ClientInstructions } from '@/interface-models/Generic/Communication/CommunicationTypes/ClientInstructions';
import GenericCommunication from '@/interface-models/Generic/Communication/CommunicationTypes/GenericCommunication';
import JobCommunication from '@/interface-models/Generic/Communication/CommunicationTypes/JobCommunication';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import {
  AccountingJobSummary,
  IAccountingJobSummary,
} from '@/interface-models/Jobs/AccountingJobSummary';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import JobReferenceDetails from '@/interface-models/Jobs/JobReferenceDetails';
import { Manifest } from '@/interface-models/Jobs/Manifest';
import { OperationJobSummary } from '@/interface-models/Jobs/OperationJobSummary';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { PUDItemShort } from '@/interface-models/Jobs/PUD/PUDItemShort';
import { WeightRequirement } from '@/interface-models/Jobs/WeightRequirement/WeightRequirement';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { useDriverDetailsStore } from '@/store/modules/DriverDetailsStore';
import { useFleetAssetOwnerStore } from '@/store/modules/FleetAssetOwnerStore';
import { useFleetAssetStore } from '@/store/modules/FleetAssetStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
import { v4 as uuidv4 } from 'uuid';
import { getPudLoadDuration } from '../BookingHelpers/BookingHelpers';
import {
  routingPossibleForCountryName,
  validateBoundingBoxLat,
  validateBoundingBoxLong,
} from '../DistanceHelpers/DistanceHelpers';

export interface PudPropertyChecker {
  isVisible: boolean;
  tooltipType: PropertyHealthLevel;
  tooltipColor: string;
  items: PropertyChecks[];
}
export interface PropertyChecks {
  key: string;
  description: string;
  color: string;
  value: PropertyHealthLevel;
}

export enum PropertyHealthLevel {
  INFO = 'fas fa-info-circle',
  WARN = 'fas fa-exclamation-circle',
  ERROR = 'fas fa-exclamation-triangle',
  SUCCESS = 'fas fa-check-circle',
}

export function returnAddressErrorSummary(address: AddressAU): string {
  if (
    address.geoLocation === null ||
    address.geoLocation.length !== 2 ||
    address.geoLocation[0] === 0 ||
    address.geoLocation[1] === 0 ||
    address.geoLocation[0] === null ||
    address.geoLocation[1] === null
  ) {
    return 'Missing GPS Coordinates - Please confirm address.';
  }
  if (address.addressId !== '') {
    return '';
  }
  return '';
}

// Iterate over PUD Items and return summaries for each. Check if the error
// level of any of these is ERROR. IF so, return false
export function pudDataIsValid(
  jobDetails: JobDetails,
  weightRequirement: WeightRequirement | null,
): boolean {
  const propertyChecks: PudPropertyChecker[] = [];
  const rateTypeId = jobDetails.serviceTypeObject.rateTypeId;
  const firstPickupIndex = jobDetails.pudItems.findIndex(
    (pud: PUDItem) => pud.legTypeFlag === 'P',
  );
  jobDetails.pudItems.forEach((pud: PUDItem, index: number) => {
    propertyChecks.push(
      returnPudValidationSummary(
        pud,
        rateTypeId,
        index === firstPickupIndex,
        weightRequirement,
      ),
    );
  });
  if (
    propertyChecks
      .map((c) => c.tooltipType)
      .some((c) => c === PropertyHealthLevel.ERROR)
  ) {
    return false;
  }
  return true;
}

export function returnPudValidationSummary(
  pudItem: PUDItem,
  rateTypeId: number,
  isFirstPickup: boolean,
  weightRequirement: WeightRequirement | null,
): PudPropertyChecker {
  const propertyCheck: PudPropertyChecker = {
    isVisible: false,
    tooltipType: PropertyHealthLevel.INFO,
    tooltipColor: 'blue',
    items: [],
  };
  const isZoneRate = rateTypeId === JobRateType.ZONE;
  const isUnitRate = rateTypeId === JobRateType.UNIT;
  const isPickup: boolean = pudItem.legTypeFlag === 'P';

  const returnColor = (healthLevel: PropertyHealthLevel) =>
    healthLevel === PropertyHealthLevel.INFO
      ? 'blue'
      : healthLevel === PropertyHealthLevel.WARN
        ? 'amber'
        : healthLevel === PropertyHealthLevel.ERROR
          ? 'red'
          : 'white';

  const pudHasUnits: boolean =
    pudItem.rateDetails.unitPickUps > 0 || pudItem.rateDetails.unitDropOffs > 0;

  // Check if Point Manager item
  if (
    pudItem.unassignedPudItemReference !== null &&
    pudItem.unassignedPudItemReference.length
  ) {
    const level = PropertyHealthLevel.INFO;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);

    propertyCheck.items.unshift({
      key: 'pointmanager',
      description: 'Added from Point Manager',
      color: returnColor(level),
      value: level,
    });
  }
  // Check if standby rate exists
  if (pudItem.isStandbyRate) {
    const level = PropertyHealthLevel.INFO;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);

    propertyCheck.items.unshift({
      key: 'standby',
      description: 'Standby Rate Applies',
      color: returnColor(level),
      value: level,
    });
  }
  // Check if selected ZONE is missing for ZONE rateTypeId
  if (!(isUnitRate || isZoneRate) && pudHasUnits) {
    const level = PropertyHealthLevel.WARN;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);
    propertyCheck.items.unshift({
      key: 'discarding-info',
      description: 'Zone/Unit Point information not required for this job.',
      color: returnColor(level),
      value: level,
    });
  }
  // Check if selected ZONE is missing for ZONE rateTypeId
  if (isUnitRate && !pudHasUnits) {
    const level = PropertyHealthLevel.WARN;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);
    propertyCheck.items.unshift({
      key: 'units-missing',
      description: 'Unit Rate - No unit count provided for this stop.',
      color: returnColor(level),
      value: level,
    });
  }

  // Check if address is outside metro
  if (pudItem.isOutsideMetro) {
    const level = PropertyHealthLevel.WARN;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);
    propertyCheck.items.unshift({
      key: 'outsideMetro',
      description: 'Outside Metro Rate Applies',
      color: returnColor(level),
      value: level,
    });
  }

  // Check if pudItem was created by driver
  if (pudItem.createdByDriver) {
    const level = PropertyHealthLevel.WARN;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);

    propertyCheck.items.unshift({
      key: 'createdbydriver',
      description: 'This stop was Driver Created. Please verify details.',
      color: returnColor(level),
      value: level,
    });
  }

  const zoneReference = pudItem.rateDetails.zoneReference;
  // Check if selected ZONE is missing for ZONE rateTypeId
  if (isZoneRate && (zoneReference === -1 || zoneReference === null)) {
    const level = PropertyHealthLevel.ERROR;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);
    propertyCheck.items.unshift({
      key: 'zone1',
      description: 'Zone Rate - No ZONE selected.',
      color: returnColor(level),
      value: level,
    });
  }

  // Check if selected zone id is missing for ZONE TO ZONE rate
  if (
    rateTypeId === JobRateType.ZONE_TO_ZONE &&
    pudItem.legTypeFlag === 'D' &&
    (zoneReference === -1 || zoneReference === null)
  ) {
    const level = PropertyHealthLevel.ERROR;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);
    propertyCheck.items.unshift({
      key: 'zoneToZone1',
      description: 'Zone to Zone Rate - No rate found for this address.',
      color: returnColor(level),
      value: level,
    });
  }

  // Check if selected ZONE is missing for ZONE rateTypeId
  if (isUnitRate && (zoneReference === -1 || zoneReference === null)) {
    const level = PropertyHealthLevel.ERROR;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);
    propertyCheck.items.unshift({
      key: 'zone2',
      description: 'Unit Rate - No ZONE selected.',
      color: returnColor(level),
      value: level,
    });
  }
  const addressErrors = returnAddressErrorSummary(pudItem.address);

  // Check if epochTime is zero
  if (pudItem.epochTime < returnEarliestBookingTime()) {
    const level = PropertyHealthLevel.ERROR;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);
    propertyCheck.items.unshift({
      key: 'bookingTooEarly',
      description: 'Invalid date entered. Please update and try again.',
      color: returnColor(level),
      value: level,
    });
  }

  // Check for errors in the address validation
  if (addressErrors) {
    const level = PropertyHealthLevel.ERROR;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);
    propertyCheck.items.unshift({
      key: 'address',
      description: addressErrors,
      color: returnColor(level),
      value: level,
    });
  } else {
    // If country is defined in the address, we should check that the country
    // that the selected country has routing capabilities in our system.
    // Currently only Australia has routing capabilities.
    if (!!pudItem.address.country) {
      const routingForCountry = routingPossibleForCountryName(
        pudItem.address.country,
      );
      // Add error message
      if (!routingForCountry) {
        const level = PropertyHealthLevel.ERROR;
        propertyCheck.tooltipType = level;
        propertyCheck.tooltipColor = returnColor(level);
        propertyCheck.items.unshift({
          key: 'noRoutingInCountry',
          description: `No routing available for ${pudItem.address.country}`,
          color: returnColor(level),
          value: level,
        });
      }
    }
    // Check that the latitude and longitude are valid for the selected country
    const country = countries.find(
      (c) =>
        c.name.toLowerCase() ===
        (pudItem.address.country ? pudItem.address.country : '').toLowerCase(),
    );
    // If no country is found then validateBoundingBoxLat and
    // validateBoundingBoxLong will return true
    const latTest = validateBoundingBoxLat(
      pudItem.address.geoLocation[1],
      country,
    );
    if (latTest !== true) {
      const level = PropertyHealthLevel.ERROR;
      propertyCheck.tooltipType = level;
      propertyCheck.tooltipColor = returnColor(level);
      propertyCheck.items.unshift({
        key: 'boundingBoxLat',
        description: `${latTest}`,
        color: returnColor(level),
        value: level,
      });
    }
    const longTest = validateBoundingBoxLong(
      pudItem.address.geoLocation[0],
      country,
    );
    if (longTest !== true) {
      const level = PropertyHealthLevel.ERROR;
      propertyCheck.tooltipType = level;
      propertyCheck.tooltipColor = returnColor(level);
      propertyCheck.items.unshift({
        key: 'boundingBoxLong',
        description: `${longTest}`,
        color: returnColor(level),
        value: level,
      });
    }
  }

  // validate weight requirement

  let weightError = false;
  const weightAdded: boolean = pudItem.weight !== null;
  if (
    !weightAdded &&
    weightRequirement &&
    weightRequirement.requiredAtBooking
  ) {
    if (
      weightRequirement.allLegs &&
      weightRequirement.pickup &&
      weightRequirement.dropoff
    ) {
      weightError = true;
    }
    if (isPickup) {
      if (weightRequirement.allLegs) {
        weightError = true;
      }
      if (isFirstPickup && !weightRequirement.allLegs) {
        weightError = true;
      }
    }
  }

  // Check for errors in the address validation
  if (weightError) {
    const level = PropertyHealthLevel.ERROR;
    propertyCheck.tooltipType = level;
    propertyCheck.tooltipColor = returnColor(level);
    propertyCheck.items.unshift({
      key: 'weight',
      description: 'Weight Required.',
      color: returnColor(level),
      value: level,
    });
  }
  if (propertyCheck.items.length > 0) {
    propertyCheck.isVisible = true;
  }
  return propertyCheck;
}

export function generateJobOperationSummary(
  jobDetails: JobDetails,
): OperationJobSummary {
  // ########### Most Recent Dispatch note ##############
  let mostRecentDispatchNote: string = '';
  const dispatchNotes = jobDetails.notes.filter(
    (note) =>
      note.type?.id === 3 &&
      note.type.communicationDetails.visibleTo.includes(7),
  );
  if (dispatchNotes && dispatchNotes.length > 0) {
    const mostRecentDispatchNoteCommunication = dispatchNotes.reduce((p, c) =>
      p.epoch > c.epoch ? p : c,
    );
    mostRecentDispatchNote = mostRecentDispatchNoteCommunication
      ? mostRecentDispatchNoteCommunication.body
      : '';
  }

  // ########### check if a startOfDayCheck note exists ##############
  // Check if start time is between midnight and 6am
  // If so, check if a startOfDayCheck note exists
  let requiresStartOfDayCheck: boolean = false;
  const startTime = jobDetails.pudItems[0].epochTime;

  const startOfDay = returnStartOfDayFromEpoch();
  const workStart = returnHoursAfterStartOfDay(6);
  // If the startTime is outside of the allowable hours, return false
  if (startTime < startOfDay || startTime > workStart) {
    requiresStartOfDayCheck = false;
  } else {
    const hasStartOfDayCheckNote = jobDetails.notes.some(
      (note) =>
        note.type?.id === 3 &&
        note.type.communicationDetails.visibleTo.includes(8),
    );
    // If it's within the hours, and if there is NOT a start of day
    // note, then a check is required
    requiresStartOfDayCheck =
      jobDetails.workStatus >= WorkStatus.BOOKED &&
      jobDetails.workStatus <= WorkStatus.IN_PROGRESS &&
      !hasStartOfDayCheckNote;
  }
  // Scheduled time of first pudItem
  const date = jobDetails.pudItems[0]
    ? jobDetails.pudItems[0].epochTime
    : moment().valueOf();
  // Find 'Booked' event in eventList, then return name of person who actioned
  // the event
  const bookedEvent = jobDetails.eventList.find(
    (e) => e.updatedStatus === 'Booked',
  );
  const bookedBy = bookedEvent ? bookedEvent.editedBy : '';

  const pudItems: PUDItemShort[] = jobDetails.pudItems.map((x: PUDItem) => {
    return {
      pudId: x.pudId,
      suburb: x.address.suburb,
      createdByDriver: x.createdByDriver,
      status: x.status,
      geoLocation: x.address.geoLocation,
      timeDefinition: x.timeDefinition,
    };
  });

  const jobOperationSummary: OperationJobSummary = Object.assign(
    new OperationJobSummary(),
    {
      _id: jobDetails._id,
      jobId: jobDetails.jobId,
      recurringJobId: jobDetails.recurringJobId,
      clientRateTypeId: jobDetails.serviceTypeObject.rateTypeId,
      serviceTypeId: jobDetails.serviceTypeId,
      date,
      clientName: jobDetails.client.clientName,
      clientId: jobDetails.client.id,
      driverId: jobDetails.driverId,
      fleetAssetId: jobDetails.fleetAssetId,
      dispatchNote: mostRecentDispatchNote,
      startOfDayCheckRequired: requiresStartOfDayCheck,
      additionalEquipment: jobDetails.additionalEquipments,
      driverIsTripRate: !!jobDetails?.accounting?.fleetAssetRates?.[0]
        ? jobDetails.accounting.fleetAssetRates[0].rate.rateTypeId ===
          JobRateType.TRIP
        : false,
      reference:
        jobDetails.jobReference.length > 0
          ? jobDetails.jobReference[0].reference
          : '',
      pudItems,
      additionalAssets: jobDetails.additionalAssets,
      statusList: jobDetails.statusList,
      isUnassignedPudType: false,
      bookedBy,
      exportType: jobDetails.exportType,
      jobSourceType: jobDetails.jobSourceType,
      workStatus: jobDetails.workStatus,
      revenueStatus: jobDetails.revenueStatus,
      expenseStatus: jobDetails.expenseStatus,
      serviceFailure: jobDetails.serviceFailure,
    },
  );
  jobOperationSummary.setAdditionalInformation();
  return jobOperationSummary;
}

// Return a readable string representation of the attachment type
export function returnAttachmentTypeName(documentTypeId: AttachmentTypes) {
  switch (documentTypeId) {
    case AttachmentTypes.PUD_ITEM_DOCUMENT:
      return 'Document';
    case AttachmentTypes.PUD_ITEM_IMAGE:
      return 'POD Image';
    case AttachmentTypes.PUD_ITEM_SIGNATURE:
      return 'Signature';
    case AttachmentTypes.SUPPORT_TICKET_DOCUMENT:
      return 'Support Ticket Document';
    case AttachmentTypes.INVOICE_ADJUSTMENT_DOCUMENT:
      return 'Invoice Adjustment Document';
    case AttachmentTypes.CHECKLIST_ATTACHMENT:
      return 'Driver Checklist Attachment';
    case AttachmentTypes.CHECKLIST_SIGNATURE:
      return 'Driver Checklist Signature';
  }
  return '-';
}
// Generates ObjectId. Call as method like so:
// const objId = ObjectId();
// Can be used as an alternative to UUID for nested properties in larger
// documents
export const ObjectId = (
  m = Math,
  d = Date,
  h = 16,
  s = (s2: number) => m.floor(s2).toString(h),
) => s(d.now() / 1000) + ' '.repeat(h).replace(/./g, () => s(m.random() * h));

// convert normal JobDetails into AccountingJobSummary
export function generateAccountingJobSummary(
  jobDetails: JobDetails,
): AccountingJobSummary {
  const fuelSurchargeRate: ChargeTypeSubtotal = {
    client: jobDetails.accounting.additionalCharges?.clientFuelSurcharge
      ? jobDetails.accounting.additionalCharges?.clientFuelSurcharge
          ?.appliedFuelSurchargeRate
      : 0.0,
    fleetAsset: jobDetails.accounting.additionalCharges?.fleetAssetFuelSurcharge
      ? jobDetails.accounting.additionalCharges.fleetAssetFuelSurcharge
          .appliedFuelSurchargeRate
      : 0.0,
  };
  const pickUpDropOffSuburbList: PickUpDropOffSuburb[] =
    jobDetails.pudItems.map((pudItem) => ({
      legTypeFlag: pudItem.legTypeFlag as 'P' | 'D', // Ensuring it matches the expected type
      suburb: pudItem.address.suburb, // Extract suburb from address
    }));

  const accountingJobSummary: IAccountingJobSummary = {
    _id: jobDetails._id ? jobDetails._id : '',
    jobId: jobDetails.jobId ? jobDetails.jobId : -1,
    recurringJobId: jobDetails.recurringJobId,
    clientRateTypeId:
      jobDetails.accounting?.clientRates?.[0]?.rate?.rateTypeId ?? 1,
    clientServiceTypeId:
      jobDetails.accounting?.clientRates?.[0]?.rate?.serviceTypeId ?? 1,
    date: jobDetails.jobRunEpoch,
    clientName: jobDetails.client.clientName,
    clientId: jobDetails.client.id,
    driverId: jobDetails.driverId,
    fleetAssetId: jobDetails.fleetAssetId,
    driverRateTypeId:
      jobDetails?.accounting?.fleetAssetRates?.[0]?.rate?.rateTypeId ?? 1,
    driverServiceTypeId:
      jobDetails?.accounting?.fleetAssetRates?.[0]?.rate?.serviceTypeId ?? 1,
    statusList: jobDetails.statusList,
    margin:
      jobDetails.accounting && jobDetails.accounting.totals
        ? jobDetails.accounting.totals.margin
        : new JobAccountingMargin(),
    finalTotal:
      jobDetails.accounting && jobDetails.accounting.totals
        ? jobDetails.accounting.totals.finalTotal
        : new ChargeTypeSubtotal(),
    fuelSurchargeRate,
    subtotals:
      jobDetails.accounting && jobDetails.accounting.totals
        ? jobDetails.accounting.totals.subtotals
        : new ChargeBreakdown(),
    invoiceId: jobDetails.accounting?.invoiceId || '',
    rctiId: jobDetails.accounting?.rctiId || '',
    reference:
      jobDetails.jobReference.length > 0
        ? jobDetails.jobReference[0].reference
        : '-',
    ownerAffiliationId: '',
    ownerName: '',
    driverName: '',
    isOutsideHire: false,
    csrAssignedId: '',
    workStatus: jobDetails.workStatus,
    revenueStatus: jobDetails.revenueStatus,
    expenseStatus: jobDetails.expenseStatus,
    workDate: jobDetails.workDate,
    createdDate: jobDetails.createdDate,
    jobSourceType: jobDetails.jobSourceType,
    exportType: jobDetails.exportType,
    serviceFailure: jobDetails.serviceFailure,
    plannedDistanceTravelled: jobDetails.distanceTravelled?.planned ?? 0,
    readableBilledDuration: jobDetails.accounting.finishedJobData
      .fleetAssetDurations
      ? jobDetails.accounting.finishedJobData.fleetAssetDurations
          .readableBilledDuration
      : jobDetails.accounting.finishedJobData.clientDurations
          .readableBilledDuration,
    editedTravelDistance: jobDetails.distanceTravelled?.gpsEstimate ?? 0,
    pickUpDropOffSuburbList: pickUpDropOffSuburbList,
  };
  const initialisedAccountingJobSummary = Object.assign(
    new AccountingJobSummary(),
    accountingJobSummary,
  );
  initialisedAccountingJobSummary.setAdditionalInformation();
  return initialisedAccountingJobSummary;
}

// takes in a list of notes and adds the supplied note to the list
export function appendJobCommunicationNote(
  notes: Communication[],
  visibility: number[],
  note: string,
): void {
  if (!note) {
    return;
  }

  const communicationType: JobCommunication = new JobCommunication();
  communicationType.visibleTo = visibility;

  const communication: Communication = {
    id: uuidv4().split('-').join(''),
    epoch: moment().valueOf(),
    type: {
      id: 3,
      communicationDetails: communicationType,
    },
    user: sessionManager.getUserName(),
    body: note,
    hidden: false,
  } as Communication;

  // push note into notes list
  notes.push(communication);
}
// Pushing in relevant information into JobDetails and designated PudItem when associating an unassignedPudItem to a leg.
// pudItemToLink: the current PudItem being merged into
// pudItemFromUnassigned: the UnassignedPudItem transformed into PudItem model
export function mergePudItemDetailsWithUnassigned(
  pudItemToLink: PUDItem,
  pudItemFromUnassigned: PUDItem,
) {
  // Set unassignedPud reference to empty array if it was previously null
  if (!Array.isArray(pudItemToLink.unassignedPudItemReference)) {
    pudItemToLink.unassignedPudItemReference = [];
  }

  // For each change we make in the pud we should keep a history of these
  // changes as operations notes on the pud.

  // If this is the first unassignedPudAssociation we should check if the
  // incoming UPI address is different to the current PudItem address. Update
  // the address and add a note if the addresses don't match
  if (
    pudItemToLink.unassignedPudItemReference.length === 0 &&
    pudItemToLink.address.formattedAddress !==
      pudItemFromUnassigned.address.formattedAddress
  ) {
    appendJobCommunicationNote(
      pudItemToLink.notes,
      [2],
      'Address Updated from: ' +
        pudItemToLink.address.formattedAddress +
        ' to Unassigned Leg address: ' +
        pudItemFromUnassigned.address.formattedAddress,
    );
    // copy unassigned pud address to pud on job
    pudItemToLink.address = JSON.parse(
      JSON.stringify(pudItemFromUnassigned.address),
    );
  }

  // If unassignedPudItemReference is currently null or empty then set to be value from UnassignedPudItem
  if (
    !pudItemToLink.unassignedPudItemReference ||
    pudItemToLink.unassignedPudItemReference.length === 0
  ) {
    // Set unassignedPudItemReference to that of unassigned pud
    pudItemToLink.unassignedPudItemReference =
      pudItemFromUnassigned.unassignedPudItemReference;
  } else {
    // If unassignedPudItemReference is currently NOT empty, then concat list
    // with incoming unassigned pud item reference
    if (pudItemFromUnassigned.unassignedPudItemReference) {
      pudItemToLink.unassignedPudItemReference =
        pudItemToLink.unassignedPudItemReference.concat(
          pudItemFromUnassigned.unassignedPudItemReference,
        );
      pudItemToLink.unassignedPudItemReference = [
        ...new Set(pudItemToLink.unassignedPudItemReference),
      ];
    }
  }

  const unassignedPudReferences: JobReferenceDetails[] =
    pudItemFromUnassigned.legTypeFlag === 'P'
      ? pudItemFromUnassigned.pickupReference
      : pudItemFromUnassigned.dropoffReference;
  const currentPudReferences: JobReferenceDetails[] =
    pudItemToLink.legTypeFlag === 'P'
      ? pudItemToLink.pickupReference
      : pudItemToLink.dropoffReference;

  // add references and notes to pud
  addUnassignedPudReferencesToPud(
    unassignedPudReferences,
    currentPudReferences,
    pudItemToLink.notes,
  );
  // add unassigned pud payload information to jobs pud manifest list. This
  // method also creates notes on the original manifest items on the leg that
  // were removed.
  pudItemToLink.manifest = addUnassignedPudPayloads(
    pudItemFromUnassigned.manifest,
    pudItemToLink.manifest,
    pudItemToLink.notes,
  );
}

// pushes unassigned pud references into job pud if they are missing.
export function addUnassignedPudReferencesToPud(
  unassignedPudReferences: JobReferenceDetails[],
  pudReferences: JobReferenceDetails[],
  notes: Communication[],
): void {
  // iterate over unassigned pud references
  for (const unassignedPudReference of unassignedPudReferences) {
    // find if current unassigned pud reference exists in jobs pud references
    const existsInJobPud = pudReferences.find(
      (x: JobReferenceDetails) =>
        x.reference === unassignedPudReference.reference,
    );
    // If the reference already exists we don't want to add a duplicate. Move on to next
    if (existsInJobPud) {
      continue;
    }
    pudReferences.push(unassignedPudReference);
    appendJobCommunicationNote(
      notes,
      [2],
      'unassigned Leg Reference added:  ' + unassignedPudReference.reference,
    );
  }
}

// adds unassigned pud manifest payload to another pud
// isOriginalPud refers to if the pud that is about to have information replaces is the first original pud item on the job that has had no overwrites.
export function addUnassignedPudPayloads(
  unassignedPudPayloads: Manifest[],
  pudPayloads: Manifest[],
  notes: Communication[],
): Manifest[] {
  // Convert current pud details manifest items to a note.
  for (const payload of pudPayloads) {
    const description: string = payload.description
      ? 'description: ' + payload.description
      : '';
    const weight: string = payload.weight ? 'weight: ' + payload.weight : '';
    const quantity: string = payload.quantity
      ? 'quantity: ' + payload.quantity
      : '';
    const height: string = payload.height ? 'height: ' + payload.height : '';
    const length: string = payload.length ? 'weight: ' + payload.length : '';
    const width: string = payload.width ? 'width: ' + payload.width : '';
    const volume: string = payload.volume ? 'volume: ' + payload.volume : '';
    const instructions: string = payload.instructions
      ? payload.instructions
      : '';
    const barcode: string = payload.barcode ? payload.barcode : '';
    // Add notes to array so we can filter out empty strings and use join
    const noteItems: string[] = [
      description,
      barcode,
      length,
      width,
      height,
      weight,
      volume,
      quantity,
      instructions,
    ].filter((x: string) => x !== '');

    // if the payload information is empty we should not add a note.
    if (noteItems.join('').length === 0) {
      continue;
    }

    const payloadNote = 'Removed Payload: ' + noteItems.join(', ');

    // We should now check to make sure that our payloadNote does not already exist on that jobs. We want to minimise duplicating notes as unassigned puds get matched and unmatched.

    const noteAlreadyExists = notes.find(
      (x: Communication) => x.body === payloadNote,
    );

    if (noteAlreadyExists) {
      continue;
    }
    appendJobCommunicationNote(notes, [2], payloadNote);
  }

  return unassignedPudPayloads;
}

// adds our unassigned pud reference as a job reference.
export function addUnassignedPudJobReference(
  groupId: string,
  jobReferences: JobReferenceDetails[],
) {
  // check if the reference already exists. We don't want to add duplicate job references.
  const referenceAlreadyExists = jobReferences.find(
    (x: JobReferenceDetails) => x.reference === groupId,
  );

  if (!referenceAlreadyExists) {
    // before we add the reference to the job we should check if the first reference on the job is empty. We don't want to push a new reference into the job if the first one is empty.
    if (
      jobReferences.length > 0 &&
      jobReferences[0].reference === '' &&
      jobReferences[0].referenceTypeId === 4
    ) {
      jobReferences[0].reference = groupId;
    } else {
      jobReferences.push(new JobReferenceDetails(4, groupId));
    }
  }
}
// Check if clientReferences already exist in the puds references list
export function checkExistingPudReferences(
  clientReferences: ClientReferenceDetails[],
  pudReferenceList: JobReferenceDetails[],
  isPickup: boolean,
) {
  for (const clientReference of clientReferences) {
    const referenceTypeAlreadyExists = pudReferenceList.find(
      (x: JobReferenceDetails) =>
        x.referenceTypeId === clientReference.referenceTypeId,
    );
    if (referenceTypeAlreadyExists || !clientReference.referenceTypeId) {
      continue;
    }
    const PUDScreenReference = new JobReferenceDetails();
    PUDScreenReference.referenceTypeId =
      clientReference.referenceTypeId as number;

    const referenceRequired = isPickup
      ? clientReference.requiredTypeId === 1 ||
        clientReference.requiredTypeId === 4 ||
        clientReference.requiredTypeId === 5 ||
        clientReference.requiredTypeId === 8 ||
        clientReference.requiredTypeId === 7
      : clientReference.requiredTypeId === 1 ||
        clientReference.requiredTypeId === 4 ||
        clientReference.requiredTypeId === 6 ||
        clientReference.requiredTypeId === 9 ||
        clientReference.requiredTypeId === 7;

    if (referenceRequired) {
      pudReferenceList.push(PUDScreenReference);
    }
  }
  // If there are no refr
  if (pudReferenceList.length === 0) {
    pudReferenceList.push(new JobReferenceDetails(4, ''));
  }
}

// Sets the default value for pud references based on the supplied pud type string
// Added to reduce duplicated logic
export function setDefaultPudReferences(
  type: string,
  pudItem: PUDItem,
  clientDetails: ClientDetails | null,
) {
  if (clientDetails) {
    if (pudItem.legTypeFlag === 'P' && pudItem.dropoffReference.length > 0) {
      const dropoffReferences = JSON.parse(
        JSON.stringify(pudItem.dropoffReference),
      );
      pudItem.pickupReference = dropoffReferences;
      pudItem.dropoffReference = [];
    } else if (
      pudItem.legTypeFlag === 'D' &&
      pudItem.pickupReference.length > 0
    ) {
      const pickupReferences = JSON.parse(
        JSON.stringify(pudItem.pickupReference),
      );
      pudItem.dropoffReference = pickupReferences;
      pudItem.pickupReference = [];
    }

    const pudReferences =
      pudItem.legTypeFlag === 'P'
        ? pudItem.pickupReference
        : pudItem.dropoffReference;
    checkExistingPudReferences(
      clientDetails.references.pudScreen,
      pudReferences,
      pudItem.legTypeFlag === 'P',
    );
  } else {
    if (type === 'pickup') {
      pudItem.pickupReference.push(new JobReferenceDetails(4, ''));
    }
    if (type === 'dropoff') {
      pudItem.dropoffReference.push(new JobReferenceDetails(4, ''));
    }
  }
}

// Switch legTypeFlag-specific fields with their equivalent fields for the supplied leg type flag
export function copyLegSpecificValuesToNewType(
  type: string,
  pudItem: PUDItem,
  clientDetails: ClientDetails | null,
) {
  // type is that new status that we're switching to
  if (type === 'pickup') {
    pudItem.legTypeFlag = 'P';
    setDefaultPudReferences(type, pudItem, clientDetails);
    pudItem.loadTime = moment
      .duration(getPudLoadDuration(true, clientDetails), 'minutes')
      .asMilliseconds();
    if (pudItem.rateDetails) {
      if (pudItem.rateDetails.unitDropOffs) {
        pudItem.rateDetails.unitPickUps = pudItem.rateDetails.unitDropOffs;
        pudItem.rateDetails.unitDropOffs = 0;
      }
    }
  }
  if (type === 'dropoff') {
    pudItem.legTypeFlag = 'D';
    setDefaultPudReferences(type, pudItem, clientDetails);

    pudItem.loadTime = moment
      .duration(getPudLoadDuration(false, clientDetails), 'minutes')
      .asMilliseconds();
    if (pudItem.rateDetails) {
      if (pudItem.rateDetails.unitPickUps) {
        pudItem.rateDetails.unitDropOffs = pudItem.rateDetails.unitPickUps;
        pudItem.rateDetails.unitPickUps = 0;
      }
    }
  }
}

// TODO: Logic duplicated here and in FleetAssetModule.getFleetAssetFromFleetAssetId
// Attempt to find solution where we can use getter from store
export function getFleetAssetFromFleetAssetId(
  fleetAssetId: string | null | undefined,
): FleetAssetSummary | undefined {
  if (
    fleetAssetId === '' ||
    fleetAssetId === null ||
    fleetAssetId === undefined
  ) {
    return;
  }
  // Try to find associated trucks
  return useFleetAssetStore().fleetAssetSummaryMap.get(fleetAssetId);
}

// Pass in driver and retrieve the associated DriverDetails from drivers map
export function getDriverFromDriverId(
  driverId: string | null | undefined,
): DriverDetailsSummary | undefined {
  if (driverId === '' || driverId === null || driverId === undefined) {
    return;
  }
  return (
    useDriverDetailsStore().driverSummaryMap as Map<
      string,
      DriverDetailsSummary
    >
  ).get(driverId);
}
// TODO: Logic duplicated here and in FleetAssetModule.getFleetAssetFromFleetAssetId
// Attempt to find solution where we can use getter from store
// Pass in ownerId and retrieve the associated owner from owners map
export function getOwnerFromOwnerId(
  ownerId: string | null | undefined,
): FleetAssetOwnerSummary | undefined {
  const owners = useFleetAssetOwnerStore().fleetAssetOwnerSummaryMap as Map<
    string,
    FleetAssetOwnerSummary
  >;
  if (ownerId === '' || ownerId === null || ownerId === undefined) {
    return;
  }
  return owners.get(ownerId);
}

// returns a list of puds that are in sync with the event times.
export function getPudOrderBasedOnEventTimes(
  pudItems: PUDItem[],
  eventList: JobStatusUpdate[],
) {
  pudItems = pudItems.map((p) => initialisePudItem(p));
  // get a list of arrived event pudIds and sort them by their event time. We also dont require event times for puds that no longer exist on the job.
  const arrivedEventPudIds: string[] = eventList
    .filter(
      (x: JobStatusUpdate) =>
        x.updatedStatus === 'ARRIVED' &&
        x.pudId &&
        pudItems.find((pud: PUDItem) => pud.pudId === x.pudId) !== undefined,
    )
    .sort((a, b) => a.correctEventTime - b.correctEventTime)
    .map((x: JobStatusUpdate) => x.pudId as string);

  for (let i = 0; i < arrivedEventPudIds.length; i++) {
    if (arrivedEventPudIds[i] !== pudItems[i].pudId) {
      const fromIndex = pudItems.findIndex(
        (x: PUDItem) => x.pudId === arrivedEventPudIds[i],
      );
      const pudItem: PUDItem = pudItems[fromIndex];
      pudItems.splice(fromIndex, 1);
      pudItems.splice(i, 0, pudItem);
    }
  }
  return pudItems;
}

// Takes in a list of communications and returns a list of communications that should be visible to the users specific role.
export function getVisibleNotesByUserRole(
  notes: Communication[],
): Communication[] {
  try {
    // If the user is not a client person role we should return all notes.
    if (sessionManager.getSecurityLevel() !== 'Client') {
      return notes;
    }
    const communicationList: Communication[] = [];
    for (const communication of notes) {
      if (
        !communication.type ||
        !communication.type.communicationDetails ||
        communication.type.communicationDetails instanceof ChatMessage
      ) {
        // note should not be shown as it is either corrupted or a chat message
        continue;
      }

      const noteDetails:
        | GenericCommunication
        | ClientInstructions
        | JobCommunication = communication.type.communicationDetails;

      const clientInvoiceVisibility: number = 1;
      const clientVisibility: number = 6;
      // If note contains client visibility we should add it to the list that will be returned.
      if (
        noteDetails.visibleTo.some((i: number) =>
          [clientInvoiceVisibility, clientVisibility].includes(i),
        )
      ) {
        communicationList.push(communication);
      }
    }
    return communicationList;
  } catch (e) {
    return [];
  }
}
