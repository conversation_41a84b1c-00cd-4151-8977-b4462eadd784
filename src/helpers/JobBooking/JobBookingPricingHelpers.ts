import {
  GST_RATE,
  returnRoundedChargeTypeSubtotal,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { initialiseJobAccountingDetails } from '@/helpers/classInitialisers/InitialiseJobDetails';
import {
  getUserLocale,
  returnFormattedDate,
  returnFormattedTime,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import { returnCorrectDuration } from '@/helpers/DateTimeHelpers/DurationHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  computeAdditionalChargeSubtotals,
  computeAdditionalChargeSubtotalValueForRateBasis,
} from '@/helpers/RateHelpers/AdditionalChargeHelpers';
import { generateDemurrages } from '@/helpers/RateHelpers/DemurrageHelpers';
import {
  generateDistanceRateData,
  returnOriginalDistanceForChargeBasis,
} from '@/helpers/RateHelpers/DistanceRateHelpers';
import { generateFuelSurcharges } from '@/helpers/RateHelpers/FuelSurchargeHelpers';
import {
  returnLegDurationsTimeDifferential,
  standbyFuelSurchargeApplies,
} from '@/helpers/RateHelpers/RateHelpers';
import {
  isDistanceRateTypeObject,
  isTimeRateTypeObject,
  isUnitRateTypeObject,
  isZoneRateTypeObject,
  isZoneToZoneRateTypeObject,
} from '@/helpers/RateHelpers/RateTableItemHelpers';
import { generateStandbyRateTotals } from '@/helpers/RateHelpers/StandbyHelpers';
import {
  timeRateCalculation,
  timeRateMinChargeMs,
} from '@/helpers/RateHelpers/TimeRateHelpers';
import { unitRateCalculation } from '@/helpers/RateHelpers/UnitRateHelpers';
import { generateZoneBreakdowns } from '@/helpers/RateHelpers/ZoneRateHelpers';
import {
  addZoneIdToPudItems,
  generateZoneToZoneRateData,
} from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import { AdditionalChargeApplicationType } from '@/interface-models/AdditionalCharges/AdditionalChargeApplicationType';
import { AdditionalChargeRateBasis } from '@/interface-models/AdditionalCharges/AdditionalChargeRateBasis';
import { AdditionalChargeType } from '@/interface-models/AdditionalCharges/AdditionalChargeType';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import DivisionDetails from '@/interface-models/Company/DivisionDetails';
import { AdditionalChargeList } from '@/interface-models/Generic/Accounting/AdditionalChargeList';
import FinishedJobData from '@/interface-models/Generic/Accounting/FinishedJobDetails/FinishedJobData';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import AdditionalChargeSubtotal from '@/interface-models/Generic/Accounting/JobAccountingTotals/AdditionalChargeSubtotal';
import ChargeTypeSubtotal from '@/interface-models/Generic/Accounting/JobAccountingTotals/ChargeTypeSubtotal';
import JobAccountingMargin from '@/interface-models/Generic/Accounting/JobAccountingTotals/JobAccountingMargin';
import { JobAccountingTotals } from '@/interface-models/Generic/Accounting/JobAccountingTotals/JobAccountingTotals';
import { TollChargeBreakdown } from '@/interface-models/Generic/Accounting/JobAccountingTotals/TollChargeBreakdown';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import StandbyCharge from '@/interface-models/Generic/Accounting/Standby/StandbyCharge';
import StandbyChargeBreakdown from '@/interface-models/Generic/Accounting/Standby/StandbyChargeBreakdown';
import ORSRoute from '@/interface-models/Generic/Route/ORSRoute';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import FinishedJobDetails from '@/interface-models/Jobs/FinishedJobDetails/FinishedJobDetails';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import LegDuration from '@/interface-models/Jobs/LegDuration';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { FuelSurchargeCalculations } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeCalculations';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { ChargeBasis } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import TripRate from '@/interface-models/ServiceRates/ServiceTypes/TripRate/TripRate';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import moment from 'moment-timezone';

/**
 * Estimates client duration data for a job based on the planned route, expected
 * arrival time, and expected load durations. Used to estimated client totals in
 * the booking screen.
 *
 * @param {PUDItem[]} pudItems - The PUD items to calculate with.
 * @param {ORSRoute} plannedRoute - The planned route.
 * @param {JobAccountingDetails} accounting - The job accounting details.
 * @returns {FinishedJobDetails} The finished job details.
 * @throws {Error} If there are no PUD items, client rate, or planned route
 * segments.
 */
export function estimateClientDurationData(
  pudItems: PUDItem[],
  plannedRoute: ORSRoute,
  accounting: JobAccountingDetails,
  legDurations: LegDuration,
  chargeableDistance?: number,
): FinishedJobDetails {
  const clientRate = accounting.clientRates[0];

  if (!pudItems.length || !clientRate) {
    throw new Error('No stops or client rate');
  }
  if (!plannedRoute.routes) {
    throw new Error('No planned route data. Cannot estimate client totals');
  }

  const information: FinishedJobDetails = new FinishedJobDetails();

  // =========================================================================
  // COMMON CALCULATIONS
  // =========================================================================
  const startedEpoch = pudItems[0]?.epochTime ?? 0;
  const startedReadableTime = returnFormattedDate(startedEpoch, '', true);
  information.actualJobDate = returnStartOfDayFromEpoch(startedEpoch);
  information.readableJobDate = startedReadableTime;

  // =========================================================================
  // SET DISTANCE AND DURATIONS
  // =========================================================================
  information.additionalDurations = legDurations;
  // =========================================================================
  // CLIENT CALCULATIONS
  // =========================================================================
  const isTimeRate = clientRate.rate.rateTypeId === JobRateType.TIME;
  let clientStartTime = pudItems[0].epochTime;

  // Get the percentage discount/surcharge applied to this rate and service
  const rateVariationPct =
    accounting.clientServiceRateVariations?.clientAdjustmentPercentage;

  // If we're dealing with a time rate, we need to adjust the start time based on the leg durations
  if (isTimeRate) {
    const timeDiff = returnLegDurationsTimeDifferential(
      'START',
      clientRate.rate.rateTypeObject as TimeRateType,
      information.additionalDurations,
    );
    clientStartTime += timeDiff;
  }

  information.clientDurations.startTime = clientStartTime;
  information.clientDurations.readableStartTime =
    returnFormattedTime(clientStartTime);

  information.clientDurations.actualStandbyDuration = pudItems.reduce(
    (acc, pud) => (pud.isStandbyRate ? acc + pud.loadTime : acc),
    0,
  );
  information.clientDurations.readableStandbyDuration = returnCorrectDuration(
    information.clientDurations.actualStandbyDuration,
  );

  const lastPud = pudItems[pudItems.length - 1];
  let clientEndTime = lastPud.epochTime + lastPud.loadTime;

  // If we're dealing with a time rate, we need to adjust the end time based on the leg durations
  if (isTimeRate) {
    const timeDiff = returnLegDurationsTimeDifferential(
      'END',
      clientRate.rate.rateTypeObject as TimeRateType,
      information.additionalDurations,
    );
    clientEndTime += timeDiff;
  }

  information.clientDurations.endTime = clientEndTime;
  information.clientDurations.readableEndTime =
    returnFormattedTime(clientEndTime);

  let durationInMilliseconds =
    clientEndTime -
    clientStartTime -
    information.clientDurations.actualStandbyDuration;

  // If we're dealing with a time rate, we need to ensure the duration is at
  // least the minimum chargeable duration
  const minDuration = isTimeRate
    ? timeRateMinChargeMs(clientRate.rate.rateTypeObject as TimeRateType)
    : 0;
  // Set expected billed duration
  information.clientDurations.expectedBilledDuration = durationInMilliseconds;

  // If the duration is less than the minimum chargeable duration, set it to the
  // minimum chargeable duration. Use this value for accounting calculations
  durationInMilliseconds = Math.max(durationInMilliseconds, minDuration);
  information.clientDurations.actualBilledDuration = durationInMilliseconds;
  information.clientDurations.readableBilledDuration = returnCorrectDuration(
    durationInMilliseconds,
  );

  // CLIENT RATE AMOUNT; PASSED INTO RETURN TOTALS
  const rateTypeId = clientRate.rate.rateTypeId;

  switch (rateTypeId) {
    case JobRateType.TIME: {
      if (
        isTimeRateTypeObject(
          clientRate.rate.rateTypeId,
          clientRate.rate.rateTypeObject,
        )
      ) {
        information.clientRateAmount = timeRateCalculation({
          rate: clientRate.rate.rateTypeObject.rate,
          multiplier: clientRate.rate.rateTypeObject.rateMultiplier,
          durationInMs: information.clientDurations.actualBilledDuration,
          variancePct: rateVariationPct,
        });
        information.clientStandbyRateAmount = timeRateCalculation({
          rate: clientRate.rate.rateTypeObject.standbyRate,
          multiplier: clientRate.rate.rateTypeObject.standbyMultiplier,
          durationInMs: information.clientDurations.actualStandbyDuration,
          variancePct: rateVariationPct,
        });
      }
      break;
    }
    case JobRateType.ZONE: {
      const clientZoneRates = clientRate.rate.rateTypeObject as ZoneRateType[];
      information.clientRateAmount = generateZoneBreakdowns(
        pudItems,
        clientZoneRates,
        rateVariationPct,
      ).rate;
      break;
    }
    case JobRateType.DISTANCE: {
      if (
        isDistanceRateTypeObject(
          clientRate.rate.rateTypeId,
          clientRate.rate.rateTypeObject,
        )
      ) {
        const distanceAdditionalData = accounting.additionalData?.distanceRate;
        if (!distanceAdditionalData) {
          throw new Error(
            'Distance rate additional data is missing or invalid.',
          );
        }
        // Check which additional travel to use based on the chargeBasis
        const additionalTravelDistances =
          clientRate.rate.rateTypeObject.chargeBasis ===
          ChargeBasis.SUBURB_CENTRES
            ? distanceAdditionalData.additionalTravelSuburbCentres.client
            : distanceAdditionalData.additionalTravelDistances.client;

        // Return the appropriate original distance
        const originalDistance = returnOriginalDistanceForChargeBasis(
          distanceAdditionalData.chargeableClientDistance,
          clientRate.rate.rateTypeObject.chargeBasis,
        );

        // Generate the distance rate data
        const distanceRateData = generateDistanceRateData({
          travelDistance: originalDistance,
          distanceRate: clientRate.rate.rateTypeObject,
          additionalDistances: additionalTravelDistances,
          additionalDurations: information.additionalDurations,
          editedTravelDistance: chargeableDistance ?? originalDistance,
          variancePct: rateVariationPct,
        });
        if (distanceRateData === null) {
          throw new Error(
            'generateDistanceRateData failed in estimateClientDurationData. clientRateAmount could not be set.',
          );
        }
        information.clientRateAmount = distanceRateData.chargeExclGst;
      }
      break;
    }
    case JobRateType.POINT_TO_POINT: {
      const rateTypeObject = clientRate.rate.rateTypeObject;
      if (Array.isArray(rateTypeObject) && rateTypeObject.length > 0) {
        information.clientRateAmount = RoundCurrencyValue(
          (rateTypeObject as PointToPointRateType[])[0].rate,
        );
      } else {
        information.clientRateAmount = RoundCurrencyValue(
          (rateTypeObject as PointToPointRateType).rate,
        );
      }
      break;
    }
    case JobRateType.UNIT: {
      const clientUnitRate = clientRate.rate.rateTypeObject as UnitRate[];
      const rateCharge = unitRateCalculation(
        pudItems,
        clientUnitRate,
        RateEntityType.CLIENT,
        null,
        null,
        rateVariationPct,
      );
      if (rateCharge) {
        information.clientRateAmount = rateCharge.chargesTotalExclGst;
      }
      break;
    }
    case JobRateType.TRIP: {
      const tripRateTypeObject = clientRate.rate.rateTypeObject as TripRate;
      information.clientRateAmount = RoundCurrencyValue(
        tripRateTypeObject.rate,
      );
      break;
    }
    case JobRateType.ZONE_TO_ZONE: {
      if (
        isZoneToZoneRateTypeObject(
          clientRate.rate.rateTypeId,
          clientRate.rate.rateTypeObject,
        )
      ) {
        const zoneToZoneRateData = generateZoneToZoneRateData({
          type: RateEntityType.CLIENT,
          rateTypes: clientRate.rate.rateTypeObject,
          isGstRegistered: true,
          variancePct: rateVariationPct,
        });
        if (zoneToZoneRateData === null) {
          throw new Error(
            'generateZoneToZoneRateData failed in estimateClientDurationData. clientRateAmount could not be set.',
          );
        }
        information.clientRateAmount = zoneToZoneRateData.chargeExclGst;
      }
      break;
    }
  }

  // Find first and last leg duration, so we can add it to the estimated
  // duration.
  const firstAndLastLegDuration =
    pudItems.length > 1
      ? pudItems[0].loadTime + pudItems[pudItems.length - 1].loadTime
      : pudItems.length === 1
        ? pudItems[0].loadTime
        : 0;
  information.clientDurations.firstAndLastLegDuration = firstAndLastLegDuration;

  // Calculate load durations
  const loadTime = pudItems.reduce((acc, pud) => acc + pud.loadTime, 0);
  information.loadDurations.actualLoadDuration = loadTime;
  information.loadDurations.readableLoadDuration =
    returnCorrectDuration(loadTime);

  information.clientDurations.actualDriveDuration =
    durationInMilliseconds - loadTime;
  information.clientDurations.readableDriveDuration = returnCorrectDuration(
    information.clientDurations.actualDriveDuration,
  );

  // Find expected load time
  const expectedLoadTime = pudItems.reduce((acc, pud) => {
    if (pud.legTypeFlag === 'P' || pud.legTypeFlag === 'D') {
      return acc + pud.loadTime;
    }
    return acc;
  }, 0);

  information.loadDurations.expectedLoadDuration = expectedLoadTime;

  information.clientDurations.expectedDriveDuration =
    information.clientDurations.expectedBilledDuration - expectedLoadTime;

  return information;
}

/**
 * Estimates the client totals for a job and updates the job accounting details.
 *
 * @param {JobDetails} params.jobDetails - The details of the job.
 * @param {PUDItem[]} params.pudItems - The PUD items for the job.
 * @param {JobAccountingDetails} params.accounting - The current job accounting details.
 * @param {AdditionalChargeType[]} params.additionalChargeTypeList - The list of additional charge types.
 * @param {number | null} params.clientOutsideMetroRate - The outside metro rate for the client.
 * @param {string} params.tollAdminAndHandlingId - The ID of the toll admin and handling charge item.
 * @param {ClientCommonAddress[]} params.commonAddresses - The common addresses for the client.
 * @param {number} [params.chargeableDistance] - The chargeable distance for the job (used for distance rates).
 * @returns {JobAccountingDetails | null} - The updated job accounting details, or null if an error occurs.
 * @throws {Error} - Throws an error if something goes wrong in one of the nested functions.
 */
export function estimateClientTotals({
  jobDetails,
  pudItems,
  accounting,
  additionalChargeTypeList,
  clientOutsideMetroRate,
  commonAddresses,
  chargeableDistance,
  isClientPortal,
  divisionDetails,
}: {
  jobDetails: JobDetails;
  pudItems: PUDItem[];
  accounting: JobAccountingDetails;
  additionalChargeTypeList: AdditionalChargeType[];
  clientOutsideMetroRate: number | null;
  commonAddresses: ClientCommonAddress[];
  chargeableDistance?: number;
  isClientPortal: boolean;
  divisionDetails: DivisionDetails | null;
}): JobAccountingDetails | null {
  try {
    if (divisionDetails === null) {
      throw new Error(
        'Division details are required to estimate client totals.',
      );
    }
    const clientGstRegistered = true;

    const jobData: FinishedJobDetails = estimateClientDurationData(
      pudItems,
      jobDetails.plannedRoute,
      accounting,
      jobDetails.legDurations,
      chargeableDistance,
    );

    // Deep copy of jobDetails.accounting. This will be what we mutate throughout
    // this function, then return at the end

    const accountingDetails = initialiseJobAccountingDetails(accounting);

    const clientRate: JobPrimaryRate = accountingDetails.clientRates[0];
    const additionalCharges: AdditionalChargeList =
      accountingDetails.additionalCharges;
    // const finishedJobData = accountingDetails.finishedJobData;

    const baseFreightCharge = new ChargeTypeSubtotal(
      RoundCurrencyValue(jobData.clientRateAmount),
      0,
    );
    const originalFreightGstCharge = new ChargeTypeSubtotal(
      RoundCurrencyValue(
        clientGstRegistered ? baseFreightCharge.client * GST_RATE : 0,
      ),
      0,
    );

    // =========================================================================
    //  FREIGHT-TYPE ADDITIONAL CHARGES
    // =========================================================================

    // Use the division customConfig to determine if the rate variations apply
    // to demurrage calculations. If applyRateVariationsToDemurrage is true,
    // use the regular percent for demurrage. If false, use 0
    const clientDemurrageVariancePct = divisionDetails.customConfig?.accounting
      ?.applyRateVariationsToDemurrage
      ? accounting.clientServiceRateVariations?.clientAdjustmentPercentage
      : 0;

    // Calculate the subtotals for all freight-type additional charges. These
    // are additional charges that adjust the freight charge, and will have fuel
    // levy and other charges applies to them.
    const freightAdditionalChargeSubtotals: AdditionalChargeSubtotal[] =
      computeAdditionalChargeSubtotals({
        type: AdditionalChargeApplicationType.FREIGHT,
        additionalChargeTypes: additionalChargeTypeList,
        additionalChargeItems: additionalCharges.chargeList,
        clientApplicableCharge: baseFreightCharge.client,
        fleetAssetApplicableCharge: 0,
        clientGstRegistered: clientGstRegistered,
        fleetAssetGstRegistered: false,
      });

    const freightChargeSummedSubtotal: ChargeTypeSubtotal =
      returnRoundedChargeTypeSubtotal(
        freightAdditionalChargeSubtotals.reduce(
          (acc, charge) => acc + charge.total.client,
          0,
        ),
      );

    const freightChargeSummedGstSubtotal: ChargeTypeSubtotal =
      returnRoundedChargeTypeSubtotal(
        clientGstRegistered ? freightChargeSummedSubtotal.client * GST_RATE : 0,
      );
    // Get the fixed and percentage portions of the summed subtotal
    const fixedFreightAdditionalChargeSubtotal: ChargeTypeSubtotal =
      computeAdditionalChargeSubtotalValueForRateBasis(
        additionalCharges.chargeList,
        freightAdditionalChargeSubtotals,
        AdditionalChargeRateBasis.FIXED,
      );

    // =========================================================================
    // FREIGHT CHARGE
    // =========================================================================

    const freightCharge: ChargeTypeSubtotal = new ChargeTypeSubtotal();
    freightCharge.client = RoundCurrencyValue(
      baseFreightCharge.client + freightChargeSummedSubtotal.client,
    );
    const freightGstCharge: ChargeTypeSubtotal = new ChargeTypeSubtotal();
    freightGstCharge.client = RoundCurrencyValue(
      clientGstRegistered ? freightCharge.client * GST_RATE : 0,
    );

    // =========================================================================
    // OUTSIDE METRO CHARGE
    // =========================================================================

    const clientOutsideMetroChargeHolder = clientOutsideMetroRate
      ? RoundCurrencyValue(
          (baseFreightCharge.client +
            fixedFreightAdditionalChargeSubtotal.client) *
            (clientOutsideMetroRate / 100),
        )
      : 0;

    const outsideMetroCharge: ChargeTypeSubtotal = new ChargeTypeSubtotal();
    outsideMetroCharge.client = clientOutsideMetroChargeHolder;

    const outsideMetroGstCharge: ChargeTypeSubtotal = new ChargeTypeSubtotal();
    outsideMetroGstCharge.client = RoundCurrencyValue(
      clientGstRegistered ? outsideMetroCharge.client * GST_RATE : 0,
    );

    // =========================================================================
    // Standby CHARGE
    // =========================================================================

    const standbyCharge = new ChargeTypeSubtotal(
      RoundCurrencyValue(jobData.clientStandbyRateAmount),
      0,
    );
    const standbyGstCharge = new ChargeTypeSubtotal(
      RoundCurrencyValue(
        clientGstRegistered ? standbyCharge.client * GST_RATE : 0,
      ),
      0,
    );

    const clientStandbyChargeBreakdown: StandbyChargeBreakdown[] =
      clientRate.rate.rateTypeId === 1
        ? generateStandbyRateTotals(
            (clientRate.rate.rateTypeObject as TimeRateType).standbyRate,
            (clientRate.rate.rateTypeObject as TimeRateType).standbyMultiplier,
            clientRate.standbyDuration.durations || [],
            clientGstRegistered,
          )
        : [];

    // =========================================================================
    // FUEL SURCHARGE
    // =========================================================================
    if (
      additionalCharges.clientFuelSurcharge?.rateBrackets &&
      additionalCharges.clientFuelSurcharge.rateBrackets.length > 1
    ) {
      throw new Error(
        'Fuel surcharge rateBrackets should have a maximum of one element when used in JobDetails',
      );
    }
    const clientFuelSurchargeRate =
      additionalCharges.clientFuelSurcharge?.appliedFuelSurchargeRate || 0;

    // =========================================================================
    // DEMURRAGE CHARGE
    // =========================================================================
    accountingDetails.finishedJobData.clientDemurrageBreakdown =
      generateDemurrages({
        rate: clientRate.rate,
        jobDetails,
        commonAddresses,
        gstRegistered: clientGstRegistered,
        existingDemurrageRateData:
          accountingDetails.finishedJobData.clientDemurrageBreakdown,
        isClient: true,
        workDiaryList: clientRate.breakDuration?.breakSummaryList || [],
        clientRateInfo: null,
        variancePct: clientDemurrageVariancePct,
      });

    const clientDemurrageChargeExclGst =
      accountingDetails.finishedJobData.clientDemurrageBreakdown.reduce(
        (acc, demurrage) => acc + demurrage.demurrageChargeExclGst,
        0,
      );
    const clientDemurrageChargeGst =
      accountingDetails.finishedJobData.clientDemurrageBreakdown.reduce(
        (acc, demurrage) => acc + demurrage.demurrageChargeGst,
        0,
      );

    const demurrageChargeTotals = new ChargeTypeSubtotal();
    demurrageChargeTotals.client = clientDemurrageChargeExclGst;

    const demurrageChargeGstTotals = new ChargeTypeSubtotal();
    demurrageChargeGstTotals.client = clientDemurrageChargeGst;

    // =========================================================================
    // FUEL SURCHARGE
    // =========================================================================

    const clientStandbyFuelSurchargeApplies = standbyFuelSurchargeApplies(
      clientRate.rate,
    );
    const clientFuelStandbyCharge = clientStandbyFuelSurchargeApplies
      ? standbyCharge.client
      : 0;

    const fuelSurcharge = new ChargeTypeSubtotal();
    const fuelGst = new ChargeTypeSubtotal();

    setEstimatedFinishedJobData(
      pudItems,
      accountingDetails,
      jobData,
      chargeableDistance,
    );

    const clientFuelSurchargeCalculations: FuelSurchargeCalculations =
      generateFuelSurcharges({
        type: RateEntityType.CLIENT,
        rate: clientRate.rate,
        fuelSurchargeRate: clientFuelSurchargeRate,
        clientFuelSurchargeRate: null,
        freightCharge: freightCharge.client,
        standbyCharge: clientFuelStandbyCharge,
        outsideMetroCharge: clientOutsideMetroChargeHolder,
        gstRegistered: clientGstRegistered,
        rateData: accountingDetails.finishedJobData.clientRateData,
        demurrageBreakdown:
          accountingDetails.finishedJobData.clientDemurrageBreakdown,
        standbyBreakdown: clientStandbyChargeBreakdown,
        freightAdjustmentCharge: freightChargeSummedSubtotal.client || 0,
      });

    fuelSurcharge.client = RoundCurrencyValue(
      clientFuelSurchargeCalculations.fuelSurchargeExclGst,
    );
    fuelGst.client = RoundCurrencyValue(
      clientFuelSurchargeCalculations.fuelSurchargeGst,
    );

    accountingDetails.finishedJobData.clientDemurrageFuelSurchargeBreakdown =
      clientFuelSurchargeCalculations.demurrageFuelSurchargeBreakdown;
    accountingDetails.finishedJobData.clientStandbyFuelSurchargeBreakdown =
      clientFuelSurchargeCalculations.standbyFuelSurchargeBreakdown;

    // =========================================================================
    // Additional Charges
    // =========================================================================

    // Sum together the totals thus far. This will be used in the additional
    // charge calculations
    const nonFreightAdditionalChargeApplicableTotal =
      freightCharge.client +
      standbyCharge.client +
      demurrageChargeTotals.client +
      fuelSurcharge.client;
    const nonFreightAdditionalChargeSubtotals: AdditionalChargeSubtotal[] =
      computeAdditionalChargeSubtotals({
        type: AdditionalChargeApplicationType.NON_FREIGHT,
        additionalChargeTypes: additionalChargeTypeList,
        additionalChargeItems: additionalCharges.chargeList,
        clientApplicableCharge: nonFreightAdditionalChargeApplicableTotal,
        fleetAssetApplicableCharge: 0,
        clientGstRegistered: clientGstRegistered,
        fleetAssetGstRegistered: false,
      });

    const additionalChargeTotal = new ChargeTypeSubtotal();
    const additionalChargeGstTotal = new ChargeTypeSubtotal();
    nonFreightAdditionalChargeSubtotals.forEach((charge) => {
      additionalChargeTotal.client += charge.total.client;
      additionalChargeGstTotal.client += charge.gstCharge.client;
    });

    additionalChargeTotal.client = RoundCurrencyValue(
      additionalChargeTotal.client,
    );
    additionalChargeGstTotal.client = RoundCurrencyValue(
      additionalChargeGstTotal.client,
    );

    const allAdditionalChargeSubtotals = [
      ...nonFreightAdditionalChargeSubtotals,
      ...freightAdditionalChargeSubtotals,
    ];

    // =========================================================================
    // GST Total
    // =========================================================================

    const gstTotal = new ChargeTypeSubtotal();
    gstTotal.client = RoundCurrencyValue(
      freightGstCharge.client +
        additionalChargeGstTotal.client +
        fuelGst.client +
        standbyGstCharge.client +
        outsideMetroGstCharge.client +
        demurrageChargeGstTotals.client,
    );

    // =========================================================================
    // Overall Totals
    // =========================================================================

    const finalTotalLessGst = new ChargeTypeSubtotal();
    finalTotalLessGst.client = RoundCurrencyValue(
      freightCharge.client +
        additionalChargeTotal.client +
        fuelSurcharge.client +
        standbyCharge.client +
        demurrageChargeTotals.client,
    );

    const finalTotal = new ChargeTypeSubtotal();
    finalTotal.client = RoundCurrencyValue(
      finalTotalLessGst.client + gstTotal.client,
    );

    // =========================================================================
    // Toll Subtotals
    // =========================================================================
    // If we're not in the client portal, calculate the toll charge subtotals
    // TODO: Currently this logic is not necessary because it's not possible to
    // add additional charges in the client portal (and we don't currently
    // request the AdditionalChargeItems from client portal)
    const tollChargeSubtotals = new TollChargeBreakdown();
    if (!isClientPortal) {
      const tollCategoryId = additionalChargeTypeList.find(
        (type) => type.longName === 'Tolls',
      )?._id;
      if (!tollCategoryId) {
        throw new Error(
          'Toll charge category not found in additional charge type list',
        );
      }

      const foundChargeGroup = allAdditionalChargeSubtotals.find(
        (type) => type.chargeRef === tollCategoryId,
      );

      if (foundChargeGroup) {
        tollChargeSubtotals.tollCharges = foundChargeGroup.total;
        tollChargeSubtotals.tollGstCharges = foundChargeGroup.gstCharge;
        tollChargeSubtotals.tollTotal.client = RoundCurrencyValue(
          foundChargeGroup.total.client + foundChargeGroup.gstCharge.client,
        );
        tollChargeSubtotals.jobTotalMinusTollCharges.client =
          RoundCurrencyValue(finalTotal.client - foundChargeGroup.total.client);
        tollChargeSubtotals.jobGstMinusTollGst.client = RoundCurrencyValue(
          gstTotal.client - foundChargeGroup.gstCharge.client,
        );
      }
    }

    // =========================================================================
    // Freight Totals
    // =========================================================================

    freightCharge.client = RoundCurrencyValue(
      freightCharge.client + outsideMetroCharge.client,
    );
    freightGstCharge.client = RoundCurrencyValue(
      freightGstCharge.client + outsideMetroGstCharge.client,
    );
    finalTotalLessGst.client = RoundCurrencyValue(
      finalTotalLessGst.client + outsideMetroCharge.client,
    );
    finalTotal.client = RoundCurrencyValue(
      finalTotal.client + outsideMetroCharge.client,
    );

    // =========================================================================
    // Margin Calculations - no equipment hire
    // =========================================================================

    const clientFreightTotal = RoundCurrencyValue(
      freightCharge.client +
        standbyCharge.client +
        demurrageChargeTotals.client,
    );
    const fleetAssetFreightTotal = 0;

    const overallMargin = new JobAccountingMargin(
      RoundCurrencyValue(clientFreightTotal - fleetAssetFreightTotal),
      RoundCurrencyValue(
        ((clientFreightTotal - fleetAssetFreightTotal) / clientFreightTotal) *
          100,
      ),
    );

    // =========================================================================
    // Set Data to return
    // =========================================================================

    const totals: JobAccountingTotals = {
      subtotals: {
        freightCharges: baseFreightCharge,
        freightGstCharges: originalFreightGstCharge,
        freightAdjustmentCharges: freightChargeSummedSubtotal,
        freightAdjustmentGstCharges: freightChargeSummedGstSubtotal,
        freightChargeTotals: freightCharge,
        freightChargeGstTotals: freightGstCharge,
        standbyChargeTotals: standbyCharge,
        standbyChargeGstTotals: standbyGstCharge,
        outsideMetroChargeTotals: outsideMetroCharge,
        outsideMetroChargeGstTotals: outsideMetroGstCharge,
        additionalCharges: additionalChargeTotal,
        additionalGstCharges: additionalChargeGstTotal,
        additionalChargeItems: allAdditionalChargeSubtotals,
        equipmentHireCharges: [],
        equipmentHireTotal: 0,
        tollCharges: tollChargeSubtotals,
        gstCharges: gstTotal,
        fuelSurcharges: fuelSurcharge,
        fuelGstSurcharges: fuelGst,
        lessGst: finalTotalLessGst,
        plusGst: finalTotal,
        standbyChargeBreakdown: new StandbyCharge(
          clientStandbyChargeBreakdown,
          [],
        ),
        demurrageChargeTotals,
        demurrageChargeGstTotals,
      },
      finalTotal,
      margin: overallMargin,
      hireMargin: new JobAccountingMargin(),
    };

    if (totals) {
      accountingDetails.totals = totals;
    } else {
      throw new Error(
        'The calculated totals were null while estimating totals.',
      );
    }

    return accountingDetails;
  } catch (error) {
    logConsoleError('Error estimating client totals', error);
    return null;
  }
}
/**
 * Constructs a fresh FinishedJobData object with the provided data.
 * @param pudItems - The PUD items to calculate with.
 * @param jobAccountingDetails - The current JobAccountingDetails object.
 * @param jobDurationData - The finished job details object
 * @param chargeableDistance - The chargeable distance for the job (used for
 * distance rates)
 * @returns - The constructed FinishedJobData object, or null if the
 * jobDurationData is undefined.
 * @throws - Error if something goes wrong in one of the nested functions.
 */
export function setEstimatedFinishedJobData(
  pudItems: PUDItem[],
  jobAccountingDetails: JobAccountingDetails,
  jobDurationData: FinishedJobDetails,
  chargeableDistance?: number,
) {
  if (jobDurationData === undefined) {
    return;
  }
  if (jobAccountingDetails.finishedJobData === null) {
    jobAccountingDetails.finishedJobData = new FinishedJobData();
  }

  const actualJobDate = pudItems[0] ? pudItems[0].epochTime : 0;

  jobAccountingDetails.finishedJobData.actualJobDate = moment(actualJobDate)
    .tz(getUserLocale())
    .startOf('day')
    .valueOf();
  jobAccountingDetails.finishedJobData.readableJobDate = returnFormattedDate(
    actualJobDate,
    'DD/MM/YY',
  );
  jobAccountingDetails.finishedJobData.loadDurations =
    jobDurationData.loadDurations;
  jobAccountingDetails.finishedJobData.clientDurations =
    jobDurationData.clientDurations;
  jobAccountingDetails.finishedJobData.fleetAssetDurations =
    jobDurationData.fleetAssetDurations;

  // Add rate-specific data
  const rateTableItem = jobAccountingDetails.clientRates[0].rate;
  if (
    isZoneRateTypeObject(rateTableItem.rateTypeId, rateTableItem.rateTypeObject)
  ) {
    const clientRateData = generateZoneBreakdowns(
      pudItems,
      rateTableItem.rateTypeObject,
      jobAccountingDetails.clientServiceRateVariations
        ?.clientAdjustmentPercentage,
    );
    jobAccountingDetails.finishedJobData.clientRateData =
      clientRateData.zoneCharges;
  } else if (
    isDistanceRateTypeObject(
      rateTableItem.rateTypeId,
      rateTableItem.rateTypeObject,
    )
  ) {
    const distanceAdditionalData =
      jobAccountingDetails.additionalData?.distanceRate;
    if (!distanceAdditionalData) {
      throw new Error('Distance rate additional data is missing or invalid.');
    } // Check which additional travel to use based on the chargeBasis
    const additionalTravelDistances =
      rateTableItem.rateTypeObject.chargeBasis === ChargeBasis.SUBURB_CENTRES
        ? distanceAdditionalData.additionalTravelSuburbCentres.client
        : distanceAdditionalData.additionalTravelDistances.client;

    // Return the appropriate original distance
    const originalDistance = returnOriginalDistanceForChargeBasis(
      distanceAdditionalData.chargeableClientDistance,
      rateTableItem.rateTypeObject.chargeBasis,
    );

    // Generate the distance rate data
    const distanceRateData = generateDistanceRateData({
      travelDistance: originalDistance,
      distanceRate: rateTableItem.rateTypeObject,
      additionalDistances: additionalTravelDistances,
      additionalDurations: new LegDuration(),
      editedTravelDistance: chargeableDistance,
      variancePct:
        jobAccountingDetails.clientServiceRateVariations
          ?.clientAdjustmentPercentage,
    });

    if (!distanceRateData) {
      throw new Error(
        'Error generating DistanceRateData in setEstimatedFinishedJobData',
      );
    }
    jobAccountingDetails.additionalData!.distanceRate!.chargeableClientDistance.edited =
      distanceRateData.editedTravelDistance;
    jobAccountingDetails.finishedJobData.clientRateData = distanceRateData;
  } else if (
    isUnitRateTypeObject(rateTableItem.rateTypeId, rateTableItem.rateTypeObject)
  ) {
    const calculatedUnitRate = unitRateCalculation(
      pudItems,
      rateTableItem.rateTypeObject,
      RateEntityType.CLIENT,
      null,
      null,
      jobAccountingDetails.clientServiceRateVariations
        ?.clientAdjustmentPercentage,
    );
    if (!calculatedUnitRate) {
      throw new Error(
        'Error generating UnitRateData in setEstimatedFinishedJobData',
      );
    }
    jobAccountingDetails.finishedJobData.clientRateData = calculatedUnitRate;
  } else if (
    isZoneToZoneRateTypeObject(
      rateTableItem.rateTypeId,
      rateTableItem.rateTypeObject,
    )
  ) {
    const zoneToZoneRateData = generateZoneToZoneRateData({
      type: RateEntityType.CLIENT,
      rateTypes: rateTableItem.rateTypeObject,
      isGstRegistered: true,
      variancePct:
        jobAccountingDetails.clientServiceRateVariations
          ?.clientAdjustmentPercentage,
    });
    if (zoneToZoneRateData === null) {
      throw new Error(
        'Error generating ZoneToZoneRateData in setEstimatedFinishedJobData',
      );
    }
    jobAccountingDetails.finishedJobData.clientRateData = zoneToZoneRateData;

    // Update the zoneIds in the pudItems
    addZoneIdToPudItems(pudItems, rateTableItem.rateTypeObject);
  }
}
