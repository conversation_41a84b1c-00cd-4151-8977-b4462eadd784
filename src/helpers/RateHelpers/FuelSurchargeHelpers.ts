import {
  calculateGstInclusiveTotals,
  GST_RATE,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnCorrectDuration } from '@/helpers/DateTimeHelpers/DurationHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import {
  isDistanceRateData,
  isZoneToZoneRateData,
  RateData,
} from '@/helpers/RateHelpers/RateDataHelpers';
import {
  isDistanceRateTypeObject,
  isZoneToZoneRateTypeObject,
} from '@/helpers/RateHelpers/RateTableItemHelpers';
import { isSameZoneLocation } from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import StandbyChargeBreakdown from '@/interface-models/Generic/Accounting/Standby/StandbyChargeBreakdown';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { DistanceRateData } from '@/interface-models/Jobs/FinishedJobDetails/DistanceRate/DistanceRateData';
import UnitRateData from '@/interface-models/Jobs/FinishedJobDetails/UnitRateData';
import ZonedUnitRateData from '@/interface-models/Jobs/FinishedJobDetails/ZonedUnitRateData';
import ZoneRateData from '@/interface-models/Jobs/FinishedJobDetails/zoneRateData';
import { ZoneToZoneRateData } from '@/interface-models/Jobs/FinishedJobDetails/ZoneToZoneRate/ZoneToZoneRateData';
import type { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { DemurrageRateData } from '@/interface-models/ServiceRates/Demurrage/DemurrageRateData';
import { FuelSurchargeBreakdown } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeBreakdown';
import { FuelSurchargeCalculations } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeCalculations';
import { FuelSurchargeRate } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeRate';
import { FuelSurchargeType } from '@/interface-models/ServiceRates/FuelSurcharge/FuelSurchargeType';
import { RangeDeterminant } from '@/interface-models/ServiceRates/FuelSurcharge/RangeDeterminant';
import { RangedFlexRate } from '@/interface-models/ServiceRates/FuelSurcharge/RangedFlexRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import DistanceRateType from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/DistanceRateType';
import { RateBracketType } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/RateBracketType';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { ZoneToZoneRateType } from '@/interface-models/ServiceRates/ServiceTypes/ZoneToZone/ZoneToZoneRateType';

/**
 * Entry point for our generation of fuel surcharges and breakdowns of fuel
 * surcharges if rate is not time or trip/Quoted rate. Calls a specific function based
 * on the rate type (TIME, DISTANCE etc).
 *
 * @param {RateEntityType} params.type - The type of rate entity, either CLIENT
 * or FLEET_ASSET.
 * @param {RateTableItems} params.rate - The rate table items.
 * @param {number} params.fuelSurchargeRate - The fuel surcharge rate (%).
 * @param {number} params.freightCharge - The freight charge ($).
 * @param {number} params.standbyCharge - The standby charge ($).
 * @param {number} params.outsideMetroCharge - The outside metro charge (%).
 * @param {boolean} params.gstRegistered - A boolean indicating whether the
 * entity is registered for GST.
 * @param {RateData} params.rateData - The rate data.
 * @param {DemurrageRateData[]} params.demurrageBreakdown - The demurrage
 * breakdown data.
 * @param {StandbyChargeBreakdown[]} params.standbyBreakdown - The standby
 * breakdown data.
 * @param {number} params.freightAdjustmentCharge - The freight adjustment
 * charge.
 * @param {number | null} params.clientFuelSurchargeRate - The client fuel
 * surcharge rate (%), null if type is CLIENT.
 * @returns {FuelSurchargeCalculations} - The fuel surcharge calculations.
 * Values in the response are added to the FinishedJobData object in the
 * accounting details object.
 */
export function generateFuelSurcharges(
  params:
    | {
        type: RateEntityType.CLIENT;
        rate: RateTableItems;
        fuelSurchargeRate: number;
        freightCharge: number;
        standbyCharge: number;
        outsideMetroCharge: number;
        gstRegistered: boolean;
        rateData: RateData;
        demurrageBreakdown: DemurrageRateData[];
        standbyBreakdown: StandbyChargeBreakdown[];
        freightAdjustmentCharge: number;
        clientFuelSurchargeRate: null;
      }
    | {
        type: RateEntityType.FLEET_ASSET;
        rate: RateTableItems;
        fuelSurchargeRate: number;
        freightCharge: number;
        standbyCharge: number;
        outsideMetroCharge: number;
        gstRegistered: boolean;
        rateData: RateData;
        demurrageBreakdown: DemurrageRateData[];
        standbyBreakdown: StandbyChargeBreakdown[];
        freightAdjustmentCharge: number;
        clientFuelSurchargeRate: number;
      },
): FuelSurchargeCalculations {
  const {
    type,
    rate,
    fuelSurchargeRate,
    clientFuelSurchargeRate,
    freightCharge,
    standbyCharge,
    outsideMetroCharge,
    gstRegistered,
    rateData,
    demurrageBreakdown,
    standbyBreakdown,
    freightAdjustmentCharge,
  } = params;
  let fuelSurchargeCalculations: FuelSurchargeCalculations | null = null;

  const isClient = type === RateEntityType.CLIENT;
  // based on what the rate is we require different logic. switch on rate and implement logic.
  switch (rate.rateTypeId) {
    case JobRateType.TIME:
      fuelSurchargeCalculations = generateFixedRateFuelSurcharges(
        rate,
        fuelSurchargeRate,
        freightCharge,
        standbyCharge,
        standbyBreakdown,
        outsideMetroCharge,
        gstRegistered,
      );
      break;
    case JobRateType.ZONE:
      fuelSurchargeCalculations = generateZoneFuelSurcharges(
        rate.rateTypeObject as ZoneRateType[],
        rateData as ZoneRateData[],
        fuelSurchargeRate,
        gstRegistered,
        clientFuelSurchargeRate,
        demurrageBreakdown,
        isClient,
        freightAdjustmentCharge,
      );
      break;
    case JobRateType.DISTANCE:
      if (
        isDistanceRateTypeObject(rate.rateTypeId, rate.rateTypeObject) &&
        isDistanceRateData(rate.rateTypeId, rateData)
      ) {
        fuelSurchargeCalculations = generateDistanceRateFuelSurcharge(
          rate.rateTypeObject,
          rateData,
          fuelSurchargeRate,
          gstRegistered,
          freightAdjustmentCharge,
          demurrageBreakdown,
        );
      } else {
        throw new TypeError(
          'DISTANCE RateTypeObject or RateData missing or not of correct type',
        );
      }

      break;
    case JobRateType.POINT_TO_POINT:
      fuelSurchargeCalculations = generatePointToPointFuelSurcharges(
        rate.rateTypeObject as PointToPointRateType,
        freightCharge,
        outsideMetroCharge,
        fuelSurchargeRate,
        gstRegistered,
        clientFuelSurchargeRate,
        demurrageBreakdown,
      );
      break;

    case JobRateType.UNIT:
      fuelSurchargeCalculations = generateUnitFuelSurcharges(
        rate.rateTypeObject as UnitRate[],
        (rateData as UnitRateData).zonedUnitRateData,
        fuelSurchargeRate,
        gstRegistered,
        clientFuelSurchargeRate,
        demurrageBreakdown,
        isClient,
        (rateData as UnitRateData).chargesTotalExclGst,
        freightAdjustmentCharge,
      );
      break;
    case JobRateType.TRIP:
      fuelSurchargeCalculations = generateFixedRateFuelSurcharges(
        rate,
        fuelSurchargeRate,
        freightCharge,
        standbyCharge,
        [],
        outsideMetroCharge,
        gstRegistered,
      );
      break;
    case JobRateType.ZONE_TO_ZONE:
      if (
        isZoneToZoneRateTypeObject(rate.rateTypeId, rate.rateTypeObject) &&
        isZoneToZoneRateData(rate.rateTypeId, rateData)
      ) {
        fuelSurchargeCalculations = generateZoneToZoneFuelSurcharges({
          zoneToZoneRates: rate.rateTypeObject,
          rateData: rateData,
          fuelSurchargeRate: fuelSurchargeRate,
          gstRegistered: gstRegistered,
          demurrageBreakdown: demurrageBreakdown,
          freightAdjustmentCharge: freightAdjustmentCharge,
          clientFuelSurchargeRate: clientFuelSurchargeRate,
        });
      } else {
        throw new TypeError(
          'ZONE TO ZONE RateTypeObject or RateData missing or not of correct type',
        );
      }
      break;
  }
  if (!fuelSurchargeCalculations) {
    throw new Error('An error occurred while generating fuel surcharge data');
  }
  return fuelSurchargeCalculations;
}

// For Time And Trip/Quoted Rate
function generateFixedRateFuelSurcharges(
  rate: RateTableItems,
  fuelSurchargeRate: number,
  freightCharge: number,
  standbyCharge: number,
  standbyBreakdown: StandbyChargeBreakdown[],
  outsideMetroCharge: number,
  gstRegistered: boolean,
): FuelSurchargeCalculations {
  const fuelRateMultiplier = fuelSurchargeRate ? fuelSurchargeRate / 100 : 0;

  // generate standby fuel surcharge breakdown. If the standby freight is equal to 0 it means that there is no standby charge or that fuel does not apply to standby.
  const standbyFuelSurchargeBreakdown: FuelSurchargeBreakdown[] =
    standbyCharge > 0
      ? standbyBreakdown.map((x: StandbyChargeBreakdown) => {
          const standbyFuelSurchargeExclGst = RoundCurrencyValue(
            x.totalExclGst * fuelRateMultiplier,
          );
          const standbyFuelSurchargeGst: number = RoundCurrencyValue(
            gstRegistered ? standbyFuelSurchargeExclGst * GST_RATE : 0,
          );
          return {
            pudId: x.pudId,
            fuelSurchargeRate,
            appliedFuelSurchargeId: (rate.rateTypeObject as TimeRateType)
              .appliedFuelSurchargeId,
            fuelSurchargeExclGst: standbyFuelSurchargeExclGst,
            fuelSurchargeGst: standbyFuelSurchargeGst,
            fuelSurchargeTotal: RoundCurrencyValue(
              standbyFuelSurchargeExclGst + standbyFuelSurchargeGst,
            ),
          };
        })
      : [];

  // if standby fuel surcharge rate applies we set our standby rate else set value to 0;
  const fuelSurchargeExclGst = RoundCurrencyValue(
    (freightCharge + outsideMetroCharge + standbyCharge) * fuelRateMultiplier,
  );
  const fuelSurchargeGst: number = RoundCurrencyValue(
    gstRegistered ? fuelSurchargeExclGst * GST_RATE : 0,
  );
  return {
    fuelSurchargeExclGst,
    fuelSurchargeGst,
    fuelSurchargeBreakdown: [],
    demurrageFuelSurchargeBreakdown: [],
    standbyFuelSurchargeBreakdown,
  };
}

function generateDistanceRateFuelSurcharge(
  distanceRate: DistanceRateType,
  rateData: DistanceRateData,
  fuelSurchargeRate: number,
  isGstRegistered: boolean,
  freightAdjustmentCharge: number,
  demurrageBreakdown: DemurrageRateData[],
): FuelSurchargeCalculations {
  const fuelRateMultiplier = fuelSurchargeRate ? fuelSurchargeRate / 100 : 0;

  // fuel totals from all unitRateBreakDown charges. Added to with each iteration below.
  let totalFuelSurchargeExclGst: number = 0;
  let totalFuelSurchargeGst: number = 0;

  const fuelSurchargeExclGst = RoundCurrencyValue(
    rateData.chargeExclGst * fuelRateMultiplier,
  );
  const fuelSurchargeGst = RoundCurrencyValue(
    isGstRegistered ? fuelSurchargeExclGst * GST_RATE : 0,
  );
  totalFuelSurchargeExclGst += fuelSurchargeExclGst;
  totalFuelSurchargeGst += fuelSurchargeGst;

  // FREIGHT ADJUSTMENT: we should calculate the total fuel charged against our
  // freight adjustment charge
  const adjustmentFuelPercentageMultiplier: number = fuelSurchargeRate
    ? fuelSurchargeRate / 100
    : 0;
  const freightAdjustmentFuelSurchargeExclGst = RoundCurrencyValue(
    adjustmentFuelPercentageMultiplier * freightAdjustmentCharge,
  );
  const adjustmentFuelSurchargeGst = RoundCurrencyValue(
    isGstRegistered ? freightAdjustmentFuelSurchargeExclGst * GST_RATE : 0,
  );
  totalFuelSurchargeExclGst += freightAdjustmentFuelSurchargeExclGst;
  totalFuelSurchargeGst += adjustmentFuelSurchargeGst;

  // calculate fuel surcharge cost against demurrage charge
  const demurrageFuelSurchargeBreakdown = demurrageBreakdown.map(
    (x: DemurrageRateData) => {
      const fuelRate = !x.demurrageFuelSurchargeApplies
        ? 0
        : fuelRateMultiplier;
      const demurrageFuelSurchargeExclGst = RoundCurrencyValue(
        x.demurrageChargeExclGst * fuelRate,
      );
      const demurrageFuelSurchargeGst: number = RoundCurrencyValue(
        isGstRegistered ? demurrageFuelSurchargeExclGst * GST_RATE : 0,
      );
      return {
        pudId: x.pudId,
        fuelSurchargeRate,
        appliedFuelSurchargeId: distanceRate.appliedFuelSurchargeId,
        fuelSurchargeExclGst: demurrageFuelSurchargeExclGst,
        fuelSurchargeGst: demurrageFuelSurchargeGst,
        fuelSurchargeTotal: RoundCurrencyValue(
          demurrageFuelSurchargeExclGst + demurrageFuelSurchargeGst,
        ),
      };
    },
  );
  // calculate total cost for all demurrage fuel surcharges.
  let totalDemurrageExclGst: number = 0;
  for (const demurrageFuel of demurrageFuelSurchargeBreakdown) {
    if (demurrageFuel.fuelSurchargeExclGst) {
      totalDemurrageExclGst += demurrageFuel.fuelSurchargeExclGst;
    }
  }
  totalFuelSurchargeExclGst += totalDemurrageExclGst;

  const totalDemurrageFuelSurchargeGst = RoundCurrencyValue(
    isGstRegistered ? totalDemurrageExclGst * GST_RATE : 0,
  );
  totalFuelSurchargeGst += totalDemurrageFuelSurchargeGst;

  return {
    fuelSurchargeExclGst: totalFuelSurchargeExclGst,
    fuelSurchargeGst: totalFuelSurchargeGst,
    fuelSurchargeBreakdown: [],
    demurrageFuelSurchargeBreakdown: demurrageFuelSurchargeBreakdown,
    standbyFuelSurchargeBreakdown: [],
  };
}

function generatePointToPointFuelSurcharges(
  pointToPointRate: PointToPointRateType,
  freightCharge: number,
  outsideMetroCharge: number,
  fuelSurchargeRate: number,
  gstRegistered: boolean,
  clientFuelSurchargeRate: number | null,
  demurrageBreakdown: DemurrageRateData[],
) {
  // fuel totals from out pointToPoint charges.
  let totalFuelSurchargeExclGst: number = 0;
  let totalFuelSurchargeGst: number = 0;

  // Set fuel surcharge rate based on applied fuel id
  const fuelApplicable = isFuelSurchargeApplicable(
    pointToPointRate.appliedFuelSurchargeId,
    clientFuelSurchargeRate,
  );
  const fuelSurchargeRateToApply = fuelApplicable ? fuelSurchargeRate : 0;

  const fuelRateMultiplier = fuelSurchargeRateToApply
    ? fuelSurchargeRateToApply / 100
    : 0;

  // START FUEL SURCHARGES CHARGES AGAINST FREIGHT COSTS
  // fuel surcharge excl gst calculated against freight charge and outsideMetroCharge
  const fuelSurchargeExclGst = RoundCurrencyValue(
    (freightCharge + outsideMetroCharge) * fuelRateMultiplier,
  );

  totalFuelSurchargeExclGst += fuelSurchargeExclGst;

  // fuel surcharge gst calculated against freight charge and outsideMetroCharge
  const fuelSurchargeGst: number = RoundCurrencyValue(
    gstRegistered ? fuelSurchargeExclGst * GST_RATE : 0,
  );
  totalFuelSurchargeGst += fuelSurchargeGst;

  // START FUEL SURCHARGES AGAINST DEMURRAGE COSTS
  // generate demurrage fuel surcharge breakdown. These include the fuel surcharges for each demurrage duration.
  const demurrageFuelSurchargeBreakdown: FuelSurchargeBreakdown[] =
    demurrageBreakdown.map((x: DemurrageRateData) => {
      const fuelRate = !x.demurrageFuelSurchargeApplies
        ? 0
        : fuelRateMultiplier;
      const demurrageFuelSurchargeExclGst = RoundCurrencyValue(
        x.demurrageChargeExclGst * fuelRate,
      );
      const demurrageFuelSurchargeGst: number = RoundCurrencyValue(
        gstRegistered ? demurrageFuelSurchargeExclGst * GST_RATE : 0,
      );
      return {
        pudId: x.pudId,
        fuelSurchargeRate,
        appliedFuelSurchargeId: pointToPointRate.appliedFuelSurchargeId,
        fuelSurchargeExclGst: demurrageFuelSurchargeExclGst,
        fuelSurchargeGst: demurrageFuelSurchargeGst,
        fuelSurchargeTotal: RoundCurrencyValue(
          demurrageFuelSurchargeExclGst + demurrageFuelSurchargeGst,
        ),
      };
    });

  // calculate total cost for all demurrage fuel surcharges.
  let totalDemurrageExclGst: number = 0;
  for (const demurrageRate of demurrageBreakdown) {
    if (demurrageRate.demurrageFuelSurchargeApplies) {
      totalDemurrageExclGst += demurrageRate.demurrageChargeExclGst;
    }
  }
  const totalDemurrageFuelSurchargeExclGst = RoundCurrencyValue(
    fuelRateMultiplier * totalDemurrageExclGst,
  );
  totalFuelSurchargeExclGst += totalDemurrageFuelSurchargeExclGst;

  const totalDemurrageFuelSurchargeGst = RoundCurrencyValue(
    gstRegistered ? totalDemurrageFuelSurchargeExclGst * GST_RATE : 0,
  );
  totalFuelSurchargeGst += totalDemurrageFuelSurchargeGst;
  return new FuelSurchargeCalculations(
    totalFuelSurchargeExclGst,
    totalFuelSurchargeGst,
    [],
    demurrageFuelSurchargeBreakdown,
    [],
  );
}

function generateZoneFuelSurcharges(
  zoneRates: ZoneRateType[],
  zoneBreakDown: ZoneRateData[],
  fuelSurchargeRate: number,
  gstRegistered: boolean,
  clientFuelSurchargeRate: number | null,
  demurrageBreakdown: DemurrageRateData[],
  isClient: boolean,
  freightAdjustmentCharge: number,
): FuelSurchargeCalculations {
  const fuelSurchargeBreakDown: FuelSurchargeBreakdown[] = [];
  let demurrageFuelSurchargeBreakdown: FuelSurchargeBreakdown[] = [];

  // fuel totals from all zoneBreakDown charges. Added to with each iteration below.
  let totalFuelSurchargeExclGst: number = 0;
  let totalFuelSurchargeGst: number = 0;

  // we should calculate the total fuel charged against our freight adjustment Charge before calculating our unit zone fuel surcharges
  const adjustmentFuelPercentageMultiplier: number = fuelSurchargeRate
    ? fuelSurchargeRate / 100
    : 0;
  const freightAdjustmentFuelSurchargeExclGst = RoundCurrencyValue(
    adjustmentFuelPercentageMultiplier * freightAdjustmentCharge,
  );
  totalFuelSurchargeExclGst += freightAdjustmentFuelSurchargeExclGst;

  const adjustmentFuelSurchargeGst = RoundCurrencyValue(
    gstRegistered ? freightAdjustmentFuelSurchargeExclGst * GST_RATE : 0,
  );
  totalFuelSurchargeGst += adjustmentFuelSurchargeGst;

  // we seperate client and fleet asset logic because the client has a list of zones and the fleet asset only has one zone rate as a percentage.
  if (isClient) {
    for (const zoneFreightCharge of zoneBreakDown) {
      const zoneRate = zoneRates.find(
        (x: ZoneRateType) => x.zone === zoneFreightCharge.zoneId,
      );
      if (!zoneRate) {
        continue;
      }
      // set the pudId from this zone freight charge.
      const pudId = zoneFreightCharge.pudId;
      // set the applied fuel surcharge calculation.
      const appliedFuelSurchargeId = zoneRate.appliedFuelSurchargeId;

      // Set fuel surcharge rate based on applied fuel id
      const fuelApplicable = isFuelSurchargeApplicable(
        appliedFuelSurchargeId,
        clientFuelSurchargeRate,
      );
      const fuelSurchargeRateToApply = fuelApplicable ? fuelSurchargeRate : 0;

      // convert percentage to useable number in calculation
      const percentageMultiplier = fuelSurchargeRateToApply
        ? fuelSurchargeRateToApply / 100
        : 0;
      // START FUEL SURCHARGES CHARGES AGAINST FREIGHT COSTS
      // fuel surcharge excl gst
      const fuelSurchargeExclGst = RoundCurrencyValue(
        percentageMultiplier * zoneFreightCharge.freightChargesTotalExclGst,
      );
      // add freight fuel surcharge excl gst cost to our total fuel surcharge excl gst counter.
      totalFuelSurchargeExclGst += fuelSurchargeExclGst;
      // fuel surcharge gst
      const fuelSurchargeGst = RoundCurrencyValue(
        gstRegistered ? fuelSurchargeExclGst * GST_RATE : 0,
      );
      // add freight fuel surcharge gst cost to our total fuel surcharge gst counter.
      totalFuelSurchargeGst += fuelSurchargeGst;
      const fuelSurchargeTotal = RoundCurrencyValue(
        fuelSurchargeExclGst + fuelSurchargeGst,
      );
      // create fuel surcharge for the freight zone charge
      const zoneFuelSurcharge: FuelSurchargeBreakdown = {
        pudId,
        fuelSurchargeRate: fuelSurchargeRateToApply,
        appliedFuelSurchargeId,
        fuelSurchargeExclGst,
        fuelSurchargeGst,
        fuelSurchargeTotal,
      };
      fuelSurchargeBreakDown.push(zoneFuelSurcharge);

      // START FUEL SURCHARGES AGAINST DEMURRAGE COSTS
      // Find the correct demurrage charge for this zone.
      const demurrageRate = demurrageBreakdown.find(
        (x: DemurrageRateData) =>
          x.demurrageFuelSurchargeApplies &&
          x.zoneId === zoneFreightCharge.zoneId &&
          x.pudId === pudId,
      );
      // demurrage charge excl gst for this zone.
      const demurrageFuelSurchargeExclGst =
        !demurrageRate || !demurrageRate.demurrageFuelSurchargeApplies
          ? 0
          : RoundCurrencyValue(
              percentageMultiplier * demurrageRate.demurrageChargeExclGst,
            );

      // add this zones total cost excl gst to our counter
      totalFuelSurchargeExclGst += demurrageFuelSurchargeExclGst;

      // demurrage charge gst for this zone.
      const demurrageFuelSurchargeGst = RoundCurrencyValue(
        gstRegistered ? demurrageFuelSurchargeExclGst * GST_RATE : 0,
      );
      // add this zones total cost gst to our counter
      totalFuelSurchargeGst += demurrageFuelSurchargeGst;
      // calculate total demurrage cost for this zone
      const demurrageFuelSurchargeTotal = RoundCurrencyValue(
        demurrageFuelSurchargeExclGst + demurrageFuelSurchargeGst,
      );

      // create fuel surcharge breadown for this zone
      const demurrageFuelSurcharge: FuelSurchargeBreakdown = {
        pudId,
        fuelSurchargeRate: fuelSurchargeRateToApply,
        appliedFuelSurchargeId,
        fuelSurchargeExclGst: demurrageFuelSurchargeExclGst,
        fuelSurchargeGst: demurrageFuelSurchargeGst,
        fuelSurchargeTotal: demurrageFuelSurchargeTotal,
      };
      demurrageFuelSurchargeBreakdown.push(demurrageFuelSurcharge);
    }
  } else {
    // Set fuel surcharge rate based on applied fuel id
    const fuelApplicable = isFuelSurchargeApplicable(
      zoneRates[0].appliedFuelSurchargeId,
      clientFuelSurchargeRate,
    );
    const fuelSurchargeRateToApply = fuelApplicable ? fuelSurchargeRate : 0;
    const percentageMultiplier = fuelSurchargeRateToApply
      ? fuelSurchargeRateToApply / 100
      : 0;

    // calculate total fuel surcharge for zone freight charge
    const zoneFreightFuelSurchargeExclGst = RoundCurrencyValue(
      zoneBreakDown[0].freightChargesTotalExclGst * percentageMultiplier,
    );
    totalFuelSurchargeExclGst += zoneFreightFuelSurchargeExclGst;
    const zoneFreightFuelSurchargeGst: number = RoundCurrencyValue(
      gstRegistered ? zoneFreightFuelSurchargeExclGst * GST_RATE : 0,
    );
    totalFuelSurchargeGst += zoneFreightFuelSurchargeGst;
    // calculate total fuel surcharge against demurrage charges
    demurrageFuelSurchargeBreakdown = demurrageBreakdown.map(
      (x: DemurrageRateData) => {
        const fuelRate = !x.demurrageFuelSurchargeApplies
          ? 0
          : percentageMultiplier;
        const demurrageFuelSurchargeExclGst = RoundCurrencyValue(
          x.demurrageChargeExclGst * fuelRate,
        );
        const demurrageFuelSurchargeGst: number = RoundCurrencyValue(
          gstRegistered ? demurrageFuelSurchargeExclGst * GST_RATE : 0,
        );
        return {
          pudId: x.pudId,
          fuelSurchargeRate,
          appliedFuelSurchargeId: zoneRates[0].appliedFuelSurchargeId,
          fuelSurchargeExclGst: demurrageFuelSurchargeExclGst,
          fuelSurchargeGst: demurrageFuelSurchargeGst,
          fuelSurchargeTotal: RoundCurrencyValue(
            demurrageFuelSurchargeExclGst + demurrageFuelSurchargeGst,
          ),
        };
      },
    );

    // calculate total cost for all demurrage fuel surcharges.
    let totalDemurrageExclGst: number = 0;
    for (const demurrageFuel of demurrageFuelSurchargeBreakdown) {
      if (demurrageFuel.fuelSurchargeExclGst) {
        totalDemurrageExclGst += demurrageFuel.fuelSurchargeExclGst;
      }
    }

    totalFuelSurchargeExclGst += totalDemurrageExclGst;

    const totalDemurrageFuelSurchargeGst = RoundCurrencyValue(
      gstRegistered ? totalDemurrageExclGst * GST_RATE : 0,
    );
    totalFuelSurchargeGst += totalDemurrageFuelSurchargeGst;
  }
  return new FuelSurchargeCalculations(
    totalFuelSurchargeExclGst,
    totalFuelSurchargeGst,
    fuelSurchargeBreakDown,
    demurrageFuelSurchargeBreakdown,
    [],
  );
}

function generateUnitFuelSurcharges(
  unitRates: UnitRate[],
  unitRateBreakDown: ZonedUnitRateData[],
  fuelSurchargeRate: number,
  gstRegistered: boolean,
  clientFuelSurchargeRate: number | null,
  demurrageBreakdown: DemurrageRateData[],
  isClient: boolean,
  fleetAssetTotalFreightExclGst: number,
  freightAdjustmentCharge: number,
): FuelSurchargeCalculations {
  const fuelSurchargeBreakdown: FuelSurchargeBreakdown[] = [];
  let demurrageFuelSurchargeBreakdown: FuelSurchargeBreakdown[] = [];

  // fuel totals from all unitRateBreakDown charges. Added to with each iteration below.
  let totalFuelSurchargeExclGst: number = 0;
  let totalFuelSurchargeGst: number = 0;

  // we should calculate the total fuel charged against our freight adjustmentCharge before calculating our unit zone fuel surcharges
  const adjustmentFuelPercentageMultiplier: number = fuelSurchargeRate
    ? fuelSurchargeRate / 100
    : 0;
  const freightAdjustmentFuelSurchargeExclGst = RoundCurrencyValue(
    adjustmentFuelPercentageMultiplier * freightAdjustmentCharge,
  );
  totalFuelSurchargeExclGst += freightAdjustmentFuelSurchargeExclGst;

  const adjustmentFuelSurchargeGst = RoundCurrencyValue(
    gstRegistered ? freightAdjustmentFuelSurchargeExclGst * GST_RATE : 0,
  );
  totalFuelSurchargeGst += adjustmentFuelSurchargeGst;
  // we seperate client and fleet asset logic because the client has a list of units and the fleet asset only has one unit rate as a percentage.
  if (isClient) {
    for (const unitFreightCharge of unitRateBreakDown) {
      const unitRate = unitRates.find(
        (x: UnitRate) => x.zoneId === unitFreightCharge.zoneId,
      );
      if (!unitRate) {
        continue;
      }
      // Set fuel surcharge rate based on applied fuel id
      const fuelApplicable = isFuelSurchargeApplicable(
        unitRate.appliedFuelSurchargeId,
        clientFuelSurchargeRate,
      );
      const fuelSurchargeRateToApply = fuelApplicable ? fuelSurchargeRate : 0;

      const pudId = unitFreightCharge.pudId;
      const appliedFuelSurchargeId = unitRate.appliedFuelSurchargeId;

      const percentageMultiplier = fuelSurchargeRateToApply
        ? fuelSurchargeRateToApply / 100
        : 0;

      // CALCULATE FUEL SURCHARGES FOR FREIGHT UNIT RATE
      const fuelSurchargeExclGst = RoundCurrencyValue(
        percentageMultiplier * unitFreightCharge.freightChargesTotalExclGst,
      );
      totalFuelSurchargeExclGst += fuelSurchargeExclGst;

      const fuelSurchargeGst = RoundCurrencyValue(
        gstRegistered ? fuelSurchargeExclGst * GST_RATE : 0,
      );

      totalFuelSurchargeGst += fuelSurchargeGst;
      const fuelSurchargeTotal = RoundCurrencyValue(
        fuelSurchargeExclGst + fuelSurchargeGst,
      );
      const unitFuelSurcharge: FuelSurchargeBreakdown = {
        pudId,
        fuelSurchargeRate: fuelSurchargeRateToApply,
        appliedFuelSurchargeId,
        fuelSurchargeExclGst,
        fuelSurchargeGst,
        fuelSurchargeTotal,
      };
      fuelSurchargeBreakdown.push(unitFuelSurcharge);

      // CALCULATE FUEL SURCHARGE FOR FLAG FALL ON THIS STOPS UNIT RATE
      const flagFallFuelSurchargeExclGst: number =
        unitRate.fuelIsAppliedToFlagFalls
          ? RoundCurrencyValue(
              percentageMultiplier * unitFreightCharge.loadChargesTotalExclGst,
            )
          : 0;

      totalFuelSurchargeExclGst += flagFallFuelSurchargeExclGst;
      const flagFallFuelSurchargeGst = RoundCurrencyValue(
        gstRegistered ? flagFallFuelSurchargeExclGst * GST_RATE : 0,
      );
      totalFuelSurchargeGst += flagFallFuelSurchargeGst;

      // Find the correct demurrage charge for this unit.
      const demurrageRate = demurrageBreakdown.find(
        (x: DemurrageRateData) =>
          x.demurrageFuelSurchargeApplies &&
          x.zoneId === unitRate.zoneId &&
          x.pudId === pudId,
      );
      // demurrage charge excl gst for this zoneBreakDown.
      const demurrageFuelSurchargeExclGst =
        !demurrageRate || !demurrageRate.demurrageFuelSurchargeApplies
          ? 0
          : RoundCurrencyValue(
              percentageMultiplier * demurrageRate.demurrageChargeExclGst,
            );
      totalFuelSurchargeExclGst += demurrageFuelSurchargeExclGst;
      const demurrageFuelSurchargeGst = RoundCurrencyValue(
        gstRegistered ? demurrageFuelSurchargeExclGst * GST_RATE : 0,
      );
      totalFuelSurchargeGst += demurrageFuelSurchargeGst;
      const demurrageFuelSurchargeTotal = RoundCurrencyValue(
        demurrageFuelSurchargeExclGst + demurrageFuelSurchargeGst,
      );
      const demurrageFuelSurcharge: FuelSurchargeBreakdown = {
        pudId,
        fuelSurchargeRate: fuelSurchargeRateToApply,
        appliedFuelSurchargeId,
        fuelSurchargeExclGst: demurrageFuelSurchargeExclGst,
        fuelSurchargeGst: demurrageFuelSurchargeGst,
        fuelSurchargeTotal: demurrageFuelSurchargeTotal,
      };
      demurrageFuelSurchargeBreakdown.push(demurrageFuelSurcharge);
    }
  } else {
    // Set fuel surcharge rate based on applied fuel id
    const fuelApplicable = isFuelSurchargeApplicable(
      unitRates[0].appliedFuelSurchargeId,
      clientFuelSurchargeRate,
    );
    const fuelSurchargeRateToApply = fuelApplicable ? fuelSurchargeRate : 0;
    const percentageMultiplier = fuelSurchargeRateToApply
      ? fuelSurchargeRateToApply / 100
      : 0;

    // calculate total fuel surcharge cost against unit freight charge
    const unitFreightFuelSurchargeExclGst = RoundCurrencyValue(
      fleetAssetTotalFreightExclGst * percentageMultiplier,
    );
    totalFuelSurchargeExclGst += unitFreightFuelSurchargeExclGst;
    const unitFreightFuelSurchargeGst: number = RoundCurrencyValue(
      gstRegistered ? unitFreightFuelSurchargeExclGst * GST_RATE : 0,
    );
    totalFuelSurchargeGst += unitFreightFuelSurchargeGst;

    // calculate fuel surcharge cost against demurrage charge
    demurrageFuelSurchargeBreakdown = demurrageBreakdown.map(
      (x: DemurrageRateData) => {
        const fuelRate = !x.demurrageFuelSurchargeApplies
          ? 0
          : percentageMultiplier;
        const demurrageFuelSurchargeExclGst = RoundCurrencyValue(
          x.demurrageChargeExclGst * fuelRate,
        );
        const demurrageFuelSurchargeGst: number = RoundCurrencyValue(
          gstRegistered ? demurrageFuelSurchargeExclGst * GST_RATE : 0,
        );
        return {
          pudId: x.pudId,
          fuelSurchargeRate,
          appliedFuelSurchargeId: unitRates[0].appliedFuelSurchargeId,
          fuelSurchargeExclGst: demurrageFuelSurchargeExclGst,
          fuelSurchargeGst: demurrageFuelSurchargeGst,
          fuelSurchargeTotal: RoundCurrencyValue(
            demurrageFuelSurchargeExclGst + demurrageFuelSurchargeGst,
          ),
        };
      },
    );
    // calculate total cost for all demurrage fuel surcharges.
    let totalDemurrageExclGst: number = 0;
    for (const demurrageFuel of demurrageFuelSurchargeBreakdown) {
      if (demurrageFuel.fuelSurchargeExclGst) {
        totalDemurrageExclGst += demurrageFuel.fuelSurchargeExclGst;
      }
    }

    totalFuelSurchargeExclGst += totalDemurrageExclGst;

    const totalDemurrageFuelSurchargeGst = RoundCurrencyValue(
      gstRegistered ? totalDemurrageExclGst * GST_RATE : 0,
    );
    totalFuelSurchargeGst += totalDemurrageFuelSurchargeGst;
  }
  return new FuelSurchargeCalculations(
    totalFuelSurchargeExclGst,
    totalFuelSurchargeGst,
    fuelSurchargeBreakdown,
    demurrageFuelSurchargeBreakdown,
    [],
  );
}

/**
 * Generates fuel surcharges and breakdowns for zone-to-zone rates.
 *
 * @param {RateEntityType} type - The type of rate entity, either CLIENT or FLEET_ASSET.
 * @param {ZoneToZoneRateType[]} zoneToZoneRates - The zone-to-zone rate types.
 * @param {ZoneToZoneRateData} rateData - The rate data for zone-to-zone rates.
 * @param {number} fuelSurchargeRate - The fuel surcharge rate.
 * @param {boolean} gstRegistered - A boolean indicating whether the entity is registered for GST.
 * @param {DemurrageRateData[]} demurrageBreakdown - The demurrage breakdown data.
 * @param {number} freightAdjustmentCharge - The freight adjustment charge.
 * @param {number | null} clientFuelSurchargeRate - The client fuel surcharge rate, null if type is CLIENT.
 * @returns {FuelSurchargeCalculations} - The fuel surcharge calculations.
 */
function generateZoneToZoneFuelSurcharges(
  params:
    | {
        zoneToZoneRates: ZoneToZoneRateType[];
        rateData: ZoneToZoneRateData;
        fuelSurchargeRate: number;
        gstRegistered: boolean;
        demurrageBreakdown: DemurrageRateData[];
        freightAdjustmentCharge: number;
        clientFuelSurchargeRate: null;
      }
    | {
        zoneToZoneRates: ZoneToZoneRateType[];
        rateData: ZoneToZoneRateData;
        fuelSurchargeRate: number;
        gstRegistered: boolean;
        demurrageBreakdown: DemurrageRateData[];
        freightAdjustmentCharge: number;
        clientFuelSurchargeRate: number;
      },
): FuelSurchargeCalculations {
  const {
    zoneToZoneRates,
    rateData,
    fuelSurchargeRate,
    gstRegistered,
    clientFuelSurchargeRate,
    demurrageBreakdown,
    freightAdjustmentCharge,
  } = params;

  const fuelSurchargeBreakDown: FuelSurchargeBreakdown[] = [];
  const demurrageFuelSurchargeBreakdown: FuelSurchargeBreakdown[] = [];

  // Fuel totals from all zoneBreakDown charges. Added to with each iteration
  // below.
  let totalFuelSurchargeExclGst: number = 0;
  let totalFuelSurchargeGst: number = 0;

  // We should calculate the total fuel charged against our freight adjustment
  // Charge before calculating our unit zone fuel surcharges
  const adjustmentFuelPercentageMultiplier: number = fuelSurchargeRate
    ? fuelSurchargeRate / 100
    : 0;
  const freightAdjustmentFuelSurchargeExclGst = RoundCurrencyValue(
    adjustmentFuelPercentageMultiplier * freightAdjustmentCharge,
  );
  totalFuelSurchargeExclGst += freightAdjustmentFuelSurchargeExclGst;

  const adjustmentFuelSurchargeGst = RoundCurrencyValue(
    gstRegistered ? freightAdjustmentFuelSurchargeExclGst * GST_RATE : 0,
  );
  totalFuelSurchargeGst += adjustmentFuelSurchargeGst;

  // We separate client and fleet asset logic because the client has a list of
  // zones and the fleet asset only has one zone rate as a percentage.
  for (const zzSubtotal of rateData.zoneRateSubtotals) {
    const rate = zoneToZoneRates.find(
      (x: ZoneToZoneRateType) =>
        isSameZoneLocation(x.pickupLocation, zzSubtotal.pickupLocation) &&
        isSameZoneLocation(x.deliveryLocation, zzSubtotal.deliveryLocation),
    );
    if (!rate) {
      continue;
    }
    // Set the pudId from this zone freight charge.
    const pudId = zzSubtotal.deliveryLocation.pudId;

    // Set the applied fuel surcharge calculation.
    const appliedFuelSurchargeId = rate.appliedFuelSurchargeId;

    // Set fuel surcharge rate based on applied fuel id
    const fuelApplicable = isFuelSurchargeApplicable(
      appliedFuelSurchargeId,
      clientFuelSurchargeRate,
    );
    const fuelSurchargeRateToApply = fuelApplicable ? fuelSurchargeRate : 0;

    // Convert percentage to useable number in calculation
    const percentageMultiplier = fuelSurchargeRateToApply
      ? fuelSurchargeRateToApply / 100
      : 0;

    // Calculate fuel cost breakdowns using percentage and freight
    const fuelSurchargeTotals = calculateGstInclusiveTotals(
      percentageMultiplier * zzSubtotal.chargeExclGst,
      gstRegistered,
    );
    // add freight fuel surcharge excl gst cost to our total fuel surcharge excl gst counter.
    totalFuelSurchargeExclGst += fuelSurchargeTotals.exclGst;
    totalFuelSurchargeGst += fuelSurchargeTotals.gst;
    // create fuel surcharge for the freight zone charge
    const zoneFuelSurcharge: FuelSurchargeBreakdown = {
      pudId,
      fuelSurchargeRate: fuelSurchargeRateToApply,
      appliedFuelSurchargeId,
      fuelSurchargeExclGst: fuelSurchargeTotals.exclGst,
      fuelSurchargeGst: fuelSurchargeTotals.gst,
      fuelSurchargeTotal: fuelSurchargeTotals.total,
    };
    fuelSurchargeBreakDown.push(zoneFuelSurcharge);

    // START FUEL SURCHARGES AGAINST DEMURRAGE COSTS
    // Find the correct demurrage charge for this zone.
    const demurrageRate = demurrageBreakdown.find(
      (x: DemurrageRateData) =>
        x.demurrageFuelSurchargeApplies && x.pudId === pudId,
    );
    // demurrage charge excl gst for this zone.
    const demurrageFuelSurchargeExclGst =
      !demurrageRate || !demurrageRate.demurrageFuelSurchargeApplies
        ? 0
        : RoundCurrencyValue(
            percentageMultiplier * demurrageRate.demurrageChargeExclGst,
          );

    // add this zones total cost excl gst to our counter
    totalFuelSurchargeExclGst += demurrageFuelSurchargeExclGst;

    // demurrage charge gst for this zone.
    const demurrageFuelSurchargeGst = RoundCurrencyValue(
      gstRegistered ? demurrageFuelSurchargeExclGst * GST_RATE : 0,
    );
    // add this zones total cost gst to our counter
    totalFuelSurchargeGst += demurrageFuelSurchargeGst;
    // calculate total demurrage cost for this zone
    const demurrageFuelSurchargeTotal = RoundCurrencyValue(
      demurrageFuelSurchargeExclGst + demurrageFuelSurchargeGst,
    );

    // create fuel surcharge breakdown for this zone
    const demurrageFuelSurcharge: FuelSurchargeBreakdown = {
      pudId,
      fuelSurchargeRate: fuelSurchargeRateToApply,
      appliedFuelSurchargeId,
      fuelSurchargeExclGst: demurrageFuelSurchargeExclGst,
      fuelSurchargeGst: demurrageFuelSurchargeGst,
      fuelSurchargeTotal: demurrageFuelSurchargeTotal,
    };
    demurrageFuelSurchargeBreakdown.push(demurrageFuelSurcharge);
  }

  return new FuelSurchargeCalculations(
    totalFuelSurchargeExclGst,
    totalFuelSurchargeGst,
    fuelSurchargeBreakDown,
    demurrageFuelSurchargeBreakdown,
    [],
  );
}

/**
 * Determines if a fuel surcharge applies based on the provided fuel surcharge
 * ID and client fuel surcharge rate.
 *
 * @param appliedFuelSurchargeId - The ID indicating the type of fuel surcharge
 * application. If `appliedFuelSurchargeId` is 2, the fuel surcharge applies
 * only if `clientFuelSurchargeRate` is greater than 0.
 * @param clientFuelSurchargeRate - The client's fuel surcharge rate, which can
 * be null.
 * @returns A boolean indicating whether the fuel surcharge applies.
 * @see applicableFuelSurcharge.ts
 */
export function isFuelSurchargeApplicable(
  appliedFuelSurchargeId: number,
  clientFuelSurchargeRate: number | null,
): boolean {
  let fuelApplied: boolean = true;
  switch (appliedFuelSurchargeId) {
    case 1:
      fuelApplied = true;
      break;
    case 2: // only apply if client fuel surcharge > 0
      if (clientFuelSurchargeRate !== null && clientFuelSurchargeRate <= 0) {
        fuelApplied = false;
      }
      break;
    case 3: // Do not apply fuel surcharge
      fuelApplied = false;
      break;
  }

  return fuelApplied;
}

/**
 * Returns the most suitable fuel surcharge rate for a given job based on the
 * provided fuel surcharge rates.
 *
 * Selection order:
 * 1. If the job already has an applied bracket (from accounting details), return the rate containing that bracket.
 * 2. Otherwise, rates are selected in the following priority:
 *    a) Service type is explicitly matched (rate.serviceTypes includes job.serviceTypeId):
 *       i. Ranged rates: Return the first ranged rate where the job falls within a bracket (using isRangedFuelSurchargeSuitableForJob).
 *       ii. Constant rates: Return the first constant rate.
 *    b) Service type is a wildcard (rate.serviceTypes is null or empty):
 *       i. Ranged rates: Return the first ranged rate where the job falls within a bracket.
 *       ii. Constant rates: Return the first constant rate.
 *    c) Service type is not matched (rate.serviceTypes does not include job.serviceTypeId and is not a wildcard):
 *       i. Ranged rates: Return the first ranged rate where the job falls within a bracket.
 *       ii. Constant rates: Return the first constant rate.
 * 3. If no suitable rate is found, return null.
 *
 * The function prioritizes explicit service type matches, then wildcard matches, then non-matches.
 * Within each group, ranged rates are preferred if the job fits a bracket; otherwise, constant rates are considered.
 *
 * @param jobDetails - The details of the job for which to find a suitable fuel
 * surcharge.
 * @param fuelSurchargeRates - The list of available client fuel surcharge
 * rates, or `null` if none are available.
 * @returns The most suitable `ClientFuelSurchargeRate` for the job, or `null`
 * if none are found.
 */
export function returnMostSuitableFuelSurcharge<T extends FuelSurchargeRate>(
  jobDetails: JobDetails,
  fuelSurchargeRates: T[] | null,
  appliedRateBracketId?: string | null,
): T | null {
  try {
    if (!fuelSurchargeRates) {
      return null;
    }

    if (appliedRateBracketId) {
      const foundSelectedRate = fuelSurchargeRates.find(
        (rate) =>
          !!rate.rateBrackets.find(
            (bracket) => bracket.bracketId === appliedRateBracketId,
          ),
      );
      if (foundSelectedRate) {
        return foundSelectedRate;
      }
    }

    // Helper to filter by service type match
    const isExplicitMatch = (rate: FuelSurchargeRate) =>
      rate.serviceTypes?.includes(jobDetails.serviceTypeId);
    const isWildcardMatch = (rate: FuelSurchargeRate) =>
      !rate.serviceTypes || rate.serviceTypes.length === 0;
    const isNoMatch = (rate: FuelSurchargeRate) =>
      !isExplicitMatch(rate) && !isWildcardMatch(rate);

    // 1a. Service type contained, ranged
    const rangedExplicit = fuelSurchargeRates.filter(
      (rate) =>
        rate.fuelSurchargeApplicationType === FuelSurchargeType.RANGED_RATE &&
        isExplicitMatch(rate),
    );
    const foundRangedExplicit = rangedExplicit.find((rate) =>
      isRangedFuelSurchargeSuitableForJob(jobDetails, rate),
    );
    if (foundRangedExplicit) {
      return foundRangedExplicit;
    }

    // 1b. Service type contained, constant
    const constantExplicit = fuelSurchargeRates.filter(
      (rate) =>
        rate.fuelSurchargeApplicationType === FuelSurchargeType.CONSTANT &&
        isExplicitMatch(rate),
    );
    if (constantExplicit.length > 0) {
      return constantExplicit[0];
    }

    // 2a. Service type is null, ranged
    const rangedWildcard = fuelSurchargeRates.filter(
      (rate) =>
        rate.fuelSurchargeApplicationType === FuelSurchargeType.RANGED_RATE &&
        isWildcardMatch(rate),
    );
    const foundRangedWildcard = rangedWildcard.find((rate) =>
      isRangedFuelSurchargeSuitableForJob(jobDetails, rate),
    );
    if (foundRangedWildcard) {
      return foundRangedWildcard;
    }

    // 2b. Service type is null, constant
    const constantWildcard = fuelSurchargeRates.filter(
      (rate) =>
        rate.fuelSurchargeApplicationType === FuelSurchargeType.CONSTANT &&
        isWildcardMatch(rate),
    );
    if (constantWildcard.length > 0) {
      return constantWildcard[0];
    }

    // 3a. Service type not a match, ranged
    const rangedNoMatch = fuelSurchargeRates.filter(
      (rate) =>
        rate.fuelSurchargeApplicationType === FuelSurchargeType.RANGED_RATE &&
        isNoMatch(rate),
    );
    const foundRangedNoMatch = rangedNoMatch.find((rate) =>
      isRangedFuelSurchargeSuitableForJob(jobDetails, rate),
    );
    if (foundRangedNoMatch) {
      return foundRangedNoMatch;
    }

    // 3b. Service type not a match, constant
    const constantNoMatch = fuelSurchargeRates.filter(
      (rate) =>
        rate.fuelSurchargeApplicationType === FuelSurchargeType.CONSTANT &&
        isNoMatch(rate),
    );
    if (constantNoMatch.length > 0) {
      return constantNoMatch[0];
    }

    // If no suitable rate is found, try to return the first result, else return null
    return fuelSurchargeRates.length > 0 ? fuelSurchargeRates[0] : null;
  } catch (error) {
    logConsoleError('Error in returnMostSuitableFuelSurcharge:', error);
    return null;
  }
}
/**
 * Calculates the value to check against fuel surcharge brackets based on the job details and the range determinant.
 *
 * @param jobDetails - The details of the job to evaluate.
 * @param rangeDeterminant - The type of range determinant.
 * @returns The value to check, or null if not applicable.
 */
function getFuelSurchargeBracketValue(
  jobDetails: JobDetails,
  rangeDeterminant: RangeDeterminant | null | undefined,
): number | null {
  switch (rangeDeterminant) {
    case RangeDeterminant.DISTANCE_TRAVELLED:
      // If the job is not completed yet, use the planned distance
      if (jobDetails.workStatus < WorkStatus.DRIVER_COMPLETED) {
        return jobDetails.distanceTravelled?.planned ?? 0;
      } else {
        return jobDetails.distanceTravelled?.gpsEstimate ?? 0;
      }
    case RangeDeterminant.ANTICIPATED_ROUTE:
      return jobDetails.distanceTravelled?.planned ?? 0;
    case RangeDeterminant.SUBURB_CENTRES:
      return jobDetails.distanceTravelled?.suburbCentres ?? 0;
    case RangeDeterminant.ACTUAL_TIME:
      if (jobDetails.pudItems.length === 0) {
        return 0;
      } else {
        const firstPud = jobDetails.pudItems[0];
        const lastPud = jobDetails.pudItems[jobDetails.pudItems.length - 1];

        if (jobDetails.workStatus < WorkStatus.DRIVER_COMPLETED) {
          return lastPud.epochTime - firstPud.epochTime;
        } else {
          const firstArrivedEvent = jobDetails.returnSpecifiedEvent(
            'ARRIVED',
            firstPud.pudId,
          );
          const lastFinishedEvent = jobDetails.returnSpecifiedEvent(
            'FINISHED',
            lastPud.pudId,
          );
          if (firstArrivedEvent && lastFinishedEvent) {
            return lastFinishedEvent.changeTime - firstArrivedEvent.changeTime;
          }
        }
      }
      break;
    default:
      return null;
  }
  return null;
}

/**
 * Determines whether a ranged fuel surcharge rate is suitable for a given job
 * based on the job details and the specified fuel surcharge rate configuration.
 *
 * @param jobDetails - The details of the job to evaluate.
 * @param fuelSurchargeRate - The fuel surcharge rate configuration to check
 * against.
 * @returns `true` if the fuel surcharge rate is suitable for the job;
 * otherwise, `false`.
 */
function isRangedFuelSurchargeSuitableForJob(
  jobDetails: JobDetails,
  fuelSurchargeRate: FuelSurchargeRate,
): boolean {
  const valueToCheck = getFuelSurchargeBracketValue(
    jobDetails,
    fuelSurchargeRate.rangeDeterminant,
  );
  return fuelSurchargeRate.isValueWithinBrackets(valueToCheck);
}

/**
 * Returns the bracket from a ranged fuel surcharge rate that matches the job's value,
 * or null if no bracket matches.
 *
 * @param jobDetails - The details of the job to evaluate.
 * @param fuelSurchargeRate - The fuel surcharge rate configuration to check against.
 * @returns The matching RangedFlexRate bracket, or null if none match.
 */
export function getMatchingFuelBracketForJob<T extends FuelSurchargeRate>(
  jobDetails: JobDetails,
  fuelSurchargeRate: T,
): RangedFlexRate | null {
  const valueToCheck = getFuelSurchargeBracketValue(
    jobDetails,
    fuelSurchargeRate.rangeDeterminant,
  );

  if (valueToCheck === null) {
    return null;
  }

  return (
    fuelSurchargeRate.rateBrackets.find((bracket) => {
      const min = bracket.bracketMin;
      const max = bracket.bracketMax;
      if (max === -1) {
        return valueToCheck >= min;
      }
      return valueToCheck >= min && valueToCheck <= max;
    }) ?? null
  );
}

/**
 * Returns a human-readable description for a fuel surcharge rate bracket.
 * @param bracket The rate bracket to describe.
 * @param rangeDeterminant The type of range determinant (e.g. ACTUAL_TIME,
 * DISTANCE_TRAVELLED, etc).
 * @returns A string describing the bracket range.
 */
export function describeFuelSurchargeBracket(
  bracket: RangedFlexRate,
  rangeDeterminant: RangeDeterminant,
): string {
  let min: string;
  let max: string;

  if (rangeDeterminant === RangeDeterminant.ACTUAL_TIME) {
    // Use returnCorrectDuration for formatting
    min = returnCorrectDuration(bracket.bracketMin);

    if (bracket.bracketMax === -1) {
      max = `> ${min}`;
    } else {
      max = returnCorrectDuration(bracket.bracketMax);
      max = `${min} - ${max}`;
    }
  } else {
    // All other determinants use km
    min = `${bracket.bracketMin}km`;
    if (bracket.bracketMax === -1) {
      max = `> ${bracket.bracketMin}km`;
    } else {
      max = `${bracket.bracketMax}km`;
    }
  }

  // If max is open-ended, just return max, otherwise return the range
  return bracket.bracketMax === -1 ? max : `${min}-${max}`;
}

/**
 * Calculates and returns the appropriate fuel surcharge rate for accounting purposes,
 * based on job details, fuel applicability, available surcharge rates, and an optional selected bracket.
 *
 * - If fuel is applicable, finds the most suitable fuel surcharge rate.
 * - Constructs a new instance of either `ClientFuelSurchargeRate` or `FleetAssetFuelSurchargeRate`
 *   depending on the type of the selected surcharge.
 * - If a specific fuel bracket is selected, applies only that bracket.
 * - Otherwise, determines the matching bracket for the job and applies it.
 *
 * @param jobDetails - The details of the job for which the fuel surcharge is being calculated.
 * @param isFuelApplicable - Indicates whether fuel surcharge should be applied.
 * @param fuelSurcharges - Array of available fuel surcharge rates, or null if none are available.
 * @param selectedFuelBracketId - Optional ID of the fuel rate bracket to apply.
 * @returns The constructed fuel surcharge rate with the applied bracket, or `undefined` if not applicable.
 */
export function returnFuelSurchargeForAccounting<T extends FuelSurchargeRate>(
  jobDetails: JobDetails,
  isFuelApplicable: boolean,
  fuelSurcharges: T[] | null,
  selectedFuelBracketId?: string | null,
): T | undefined {
  if (!isFuelApplicable || !fuelSurcharges || fuelSurcharges.length === 0) {
    return undefined;
  }

  const suitableFuelSurcharge = returnMostSuitableFuelSurcharge<T>(
    jobDetails,
    fuelSurcharges,
    selectedFuelBracketId,
  );

  if (!suitableFuelSurcharge) {
    return undefined;
  }

  // Create a new instance of the same type
  const fuelToApply = new (suitableFuelSurcharge.constructor as {
    new (data: T): T;
  })(suitableFuelSurcharge);

  if (selectedFuelBracketId) {
    fuelToApply.appliedRateBracketId = selectedFuelBracketId;
    fuelToApply.rateBrackets = suitableFuelSurcharge.rateBrackets.filter(
      (bracket) => bracket.bracketId === selectedFuelBracketId,
    );
  } else {
    const bracketToApply = getMatchingFuelBracketForJob(
      jobDetails,
      fuelToApply,
    );
    if (bracketToApply) {
      fuelToApply.appliedRateBracketId = bracketToApply.bracketId;
      fuelToApply.rateBrackets = [bracketToApply];
    }
  }

  return fuelToApply;
}

/**
 * Validates a given fuel surcharge object to ensure all required fields are
 * present and valid.
 *
 * @param fuelSurcharge - The fuel surcharge object to validate, or null.
 * @returns `true` if the fuel surcharge is valid; otherwise, returns a string
 * describing the validation error.
 *
 * - Checks for null or undefined values in the main object and its required
 *   properties.
 * - Ensures `rateBrackets` is present and non-empty.
 * - Validates that `fuelSurchargeApplicationType` is defined.
 * - Disallows progressive rate brackets for fuel surcharges.
 * - For `RANGED_RATE` application types, ensures `rangeDeterminant` is present
 *   and all brackets have both `bracketMin` and `bracketMax` defined.
 */
export function validateFuelSurcharge(
  fuelSurcharge: FuelSurchargeRate | null,
): true | string {
  if (!fuelSurcharge) {
    return 'Fuel surcharge is null or undefined.';
  }
  if (!fuelSurcharge.rateBrackets) {
    return 'Fuel surcharge rateBrackets is null or undefined.';
  }
  if (!fuelSurcharge.rateBrackets.length) {
    return 'Fuel surcharge rateBrackets is empty.';
  }
  if (!fuelSurcharge.fuelSurchargeApplicationType) {
    return 'Fuel surcharge type is null or undefined.';
  }
  if (fuelSurcharge.rateBracketType === RateBracketType.PROGRESSIVE) {
    return 'Progressive rate brackets are not supported for fuel surcharges.';
  }
  // Validate required fields for RANGED RATE fuel surcharge
  if (
    fuelSurcharge.fuelSurchargeApplicationType === FuelSurchargeType.RANGED_RATE
  ) {
    if (!fuelSurcharge.rangeDeterminant) {
      return `Ranged rate ${fuelSurcharge.id} missing range determinant.`;
    }
    if (
      fuelSurcharge.rateBrackets.some(
        (bracket) =>
          bracket.bracketMin === null ||
          bracket.bracketMin === undefined ||
          bracket.bracketMax === null ||
          bracket.bracketMax === undefined,
      )
    ) {
      return `Ranged rate ${fuelSurcharge.id} has invalid brackets. All brackets must have both bracketMin and bracketMax defined.`;
    }
  }
  return true;
}

/**
 * Validates a `FuelSurchargeRate` object for use within a Job context.
 *
 * This function checks the following:
 * - Delegates to `validateFuelSurcharge` for base validation.
 * - Ensures that `rateBrackets` contains at most one element when used in JobDetails.
 *
 * @param fuelSurcharge - The `FuelSurchargeRate` object to validate, or `null`.
 * @returns `true` if the fuel surcharge is valid for a job, or a string describing the validation error.
 */
export function validateFuelSurchargeInJob(
  fuelSurcharge: FuelSurchargeRate | null,
): true | string {
  const isValid = validateFuelSurcharge(fuelSurcharge);
  if (isValid !== true) {
    return isValid;
  }

  const validFuelSurcharge = fuelSurcharge!;

  if (
    validFuelSurcharge.rateBrackets &&
    validFuelSurcharge.rateBrackets.length > 1
  ) {
    return 'Fuel surcharge rateBrackets should have a maximum of one element when used in JobDetails';
  }

  return true;
}
