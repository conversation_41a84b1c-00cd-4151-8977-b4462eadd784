import {
  GST_RATE,
  RoundCurrencyValue,
} from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { initialiseRateTableItems } from '@/helpers/classInitialisers/InitialiseRateTableItems';
import {
  nearestMinutes,
  returnFormattedDate,
  returnStartOfDayFromEpoch,
} from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  generateDistanceRateData,
  returnOriginalDistanceForChargeBasis,
} from '@/helpers/RateHelpers/DistanceRateHelpers';
import { isFuelSurchargeApplicable } from '@/helpers/RateHelpers/FuelSurchargeHelpers';
import { isZoneToZoneRateData } from '@/helpers/RateHelpers/RateDataHelpers';
import {
  isDistanceRateTypeObject,
  isTimeRateTypeObject,
  isUnitRateTypeObject,
  isZoneRateTypeObject,
  isZoneToZoneRateTypeObject,
} from '@/helpers/RateHelpers/RateTableItemHelpers';
import { calculateStandbyDurations } from '@/helpers/RateHelpers/StandbyHelpers';
import { unitRateCalculation } from '@/helpers/RateHelpers/UnitRateHelpers';
import {
  fleetAssetZoneBreakDown,
  generateZoneBreakdowns,
} from '@/helpers/RateHelpers/ZoneRateHelpers';
import {
  generateZoneToZoneRateData,
  initZoneToZoneRateTypeObjectIfRequired,
} from '@/helpers/RateHelpers/ZoneToZoneRateHelpers';
import { ClientCommonAddress } from '@/interface-models/Client/ClientCommonAddress';
import WorkDiarySummary from '@/interface-models/Driver/WorkDiary/WorkDiarySummary';
import FleetAssetSummary from '@/interface-models/FleetAsset/Summary/FleetAssetSummary';
import BreakDuration from '@/interface-models/Generic/Accounting/Break/BreakDuration';
import FinishedJobData from '@/interface-models/Generic/Accounting/FinishedJobDetails/FinishedJobData';
import { JobAccountingDetails } from '@/interface-models/Generic/Accounting/JobAccountingDetails';
import JobPrimaryRate from '@/interface-models/Generic/Accounting/JobPrimaryRate';
import { TimeTypeJobRateData } from '@/interface-models/Generic/Accounting/JobRateData/TimeTypeJobRateData';
import StandbyDuration from '@/interface-models/Generic/Accounting/Standby/StandbyDuration';
import rateMultipliers, {
  RateMultipliers,
} from '@/interface-models/Generic/ServiceTypes/RateMultipliers';
import { JobRateType } from '@/interface-models/Generic/ServiceTypes/ServiceTypeRates';
import { ServiceTypes } from '@/interface-models/Generic/ServiceTypes/ServiceTypes';
import FinishedJobDetails from '@/interface-models/Jobs/FinishedJobDetails/FinishedJobDetails';
import UnitRateData from '@/interface-models/Jobs/FinishedJobDetails/UnitRateData';
import { ZoneBreakDown } from '@/interface-models/Jobs/FinishedJobDetails/ZoneBreakDown';
import ZoneRateData from '@/interface-models/Jobs/FinishedJobDetails/zoneRateData';
import JobDetails from '@/interface-models/Jobs/JobDetails';
import LegDuration from '@/interface-models/Jobs/LegDuration';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import ClientFuelSurchargeRate from '@/interface-models/ServiceRates/Client/AdditionalCharges/FuelSurcharge/ClientFuelSurchargeRate';
import ClientServiceRate from '@/interface-models/ServiceRates/Client/ClientServiceRate/ClientServiceRate';
import FleetAssetFuelSurchargeRate from '@/interface-models/ServiceRates/FleetAssetOwner/AdditionalCharges/FuelSurcharge/FleetAssetFuelSurchargeRate';
import FleetAssetServiceRate from '@/interface-models/ServiceRates/FleetAssetOwner/FleetAssetServiceRate/FleetAssetServiceRate';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import RateTableItems from '@/interface-models/ServiceRates/RateTableItems';
import { ChargeBasis } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import PointToPointRateType from '@/interface-models/ServiceRates/ServiceTypes/PointToPointServiceRate/PointToPointRateType';
import TimeRateType from '@/interface-models/ServiceRates/ServiceTypes/TimeServiceRate/TimeRateType';
import UnitRate from '@/interface-models/ServiceRates/ServiceTypes/UnitRate/UnitRate';
import ZoneRateType from '@/interface-models/ServiceRates/ServiceTypes/ZoneServiceRate/ZoneRateType';
import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';
// =============================================================================
// PRIMARY RATE GENERATOR
// =============================================================================
/**
 * Initial calculation of primary rate. Calculates the primary rate for a client
 * and fleet asset based on various parameters.
 *
 * @param clientRatePrimaryData - The primary rate data for the client.
 * @param fleetAssetRatePrimaryData - The primary rate data for the fleet asset.
 * @param jobDetails - The details of the job.
 * @param fleetAsset - The summary of the fleet asset, if available.
 * @param clientServiceRateTable - The service rate table for the client.
 * @param fleetAssetServiceRateTable - The service rate table for the fleet
 * asset.
 * @param fleetAssetRateId - The rate ID for the fleet asset.
 * @param overrideFleetAssetRateTypeId - An optional override rate type ID for
 * the fleet asset.
 * @param clientFuelSurcharge - The fuel surcharge rate for the client.
 * @param fleetAssetFuelSurcharge - The fuel surcharge rate for the fleet asset.
 * @param workDiaryList - A list of work diary summaries.
 * @param clientCommonAddressList - A list of common addresses for the client.
 *
 * @throws Error if something goes wrong. Caught in JobPricingManagement where this is called
 */
export async function calculateJobPrimaryRates({
  jobDetails,
  clientRatePrimaryData,
  fleetAssetRatePrimaryData,
  fleetAsset,
  clientServiceRateTable,
  fleetAssetServiceRateTable,
  fleetAssetRateId,
  overrideFleetAssetRateTypeId = null,
  clientFuelSurcharge,
  fleetAssetFuelSurcharge,
  workDiaryList,
  clientCommonAddressList,
}: {
  jobDetails: JobDetails;
  clientRatePrimaryData: JobPrimaryRate;
  fleetAssetRatePrimaryData: JobPrimaryRate;
  fleetAsset: FleetAssetSummary | undefined;
  clientServiceRateTable: ClientServiceRate;
  fleetAssetServiceRateTable: FleetAssetServiceRate;
  fleetAssetRateId: number;
  overrideFleetAssetRateTypeId: number | null;
  clientFuelSurcharge: ClientFuelSurchargeRate;
  fleetAssetFuelSurcharge: FleetAssetFuelSurchargeRate;
  workDiaryList: WorkDiarySummary[];
  clientCommonAddressList: ClientCommonAddress[];
}): Promise<void> {
  let clientServiceType = jobDetails.serviceTypeId;
  const clientRateType = jobDetails.serviceTypeObject.rateTypeId;

  // we require the service type to be set to 4 as unit rate is also a service type.
  if (jobDetails.serviceTypeObject.rateTypeId === JobRateType.UNIT) {
    clientServiceType = 4;
  }

  // check if rate is trip/Quoted rate.
  let isClientTripRate = false;

  const clientRates = jobDetails.accounting.clientRates[0];
  if (
    clientRates !== undefined &&
    clientRates.rate.rateTypeId === JobRateType.TRIP
  ) {
    isClientTripRate = true;
  }

  let clientRateTableItem: RateTableItems | undefined;

  if (!isClientTripRate) {
    clientRatePrimaryData.validToDate =
      clientServiceRateTable.validToDate || null;
    clientRatePrimaryData.validFromDate =
      clientServiceRateTable.validFromDate || null;

    clientRateTableItem = clientServiceRateTable.rateTableItems.find(
      (rate) =>
        rate.serviceTypeId === clientServiceType &&
        rate.rateTypeId === clientRateType,
    );
  } else {
    clientRateTableItem = clientRates.rate;
  }

  if (!clientRateTableItem) {
    throw new Error(
      `Client rate table item not found for job ${jobDetails.jobId}. Looking for service type: ${clientServiceType}, Rate type: ${clientRateType}`,
    );
  }

  // For a ZONE to ZONE rate job, replace the string rateTypeObject with the
  // zone data. Does nothing for other rate types.
  await initZoneToZoneRateTypeObjectIfRequired(
    RateEntityType.CLIENT,
    clientRateTableItem,
    jobDetails.client.id,
    jobDetails.pudItems,
  );

  clientRatePrimaryData.rate = initialiseRateTableItems(clientRateTableItem);
  clientRatePrimaryData.outsideMetroRate =
    clientRateTableItem.rateTypeId === JobRateType.TIME &&
    clientServiceRateTable.outsideMetroRate
      ? clientServiceRateTable.outsideMetroRate
      : 0;

  clientRatePrimaryData.rateTableName = clientServiceRateTable.name || '';

  setFuelSurchargeRate({
    type: RateEntityType.CLIENT,
    rate: clientRatePrimaryData.rate,
    fuelSurcharge: clientFuelSurcharge,
    pudItems: jobDetails.pudItems,
  });

  // Calculate JobPrimaryRate.rateData
  setPrimaryRateTimeData(
    RateEntityType.CLIENT,
    jobDetails,
    clientRatePrimaryData,
    clientRateTableItem,
    workDiaryList,
    clientCommonAddressList,
  );

  // fleet asset rates pulled from fleet asset
  const fleetAssetRates = jobDetails.accounting.fleetAssetRates[0];
  // If serviceTypeId is defined in fleetAssetRates, and the serviceTypeId is
  // different to the job level serviceTypeId, then use the value from
  // fleetAssetRates
  let fleetAssetServiceType =
    fleetAssetRates?.rate?.serviceTypeId &&
    fleetAssetRates.rate.serviceTypeId !== jobDetails.serviceTypeId
      ? fleetAssetRates.rate.serviceTypeId
      : jobDetails.serviceTypeId;
  // Use overrideFleetAssetRateTypeId if it is provided, otherwise use
  // fleetAssetRateId
  const fleetAssetRateType: number =
    overrideFleetAssetRateTypeId !== null
      ? overrideFleetAssetRateTypeId
      : fleetAssetRateId;

  // If job is UNIT RATE, then use serviceTypeId of 4 to search rateTableItems
  if (fleetAssetRateType === JobRateType.UNIT) {
    fleetAssetServiceType = 4;
  }
  const isFleetAssetTripRate = fleetAssetRateId === JobRateType.TRIP;

  let fleetAssetRateTableItem: RateTableItems | undefined;

  if (!isFleetAssetTripRate) {
    fleetAssetRatePrimaryData.validToDate =
      fleetAssetServiceRateTable.validToDate || null;
    fleetAssetRatePrimaryData.validFromDate =
      fleetAssetServiceRateTable.validFromDate || null;

    fleetAssetRateTableItem = fleetAssetServiceRateTable.rateTableItems.find(
      (rate) =>
        rate.serviceTypeId === fleetAssetServiceType &&
        rate.rateTypeId === fleetAssetRateType,
    );
  } else if (fleetAssetRates !== undefined) {
    fleetAssetRateTableItem = fleetAssetRates.rate;
  }

  if (fleetAssetRateTableItem === undefined) {
    if (fleetAssetServiceType === 4 && fleetAsset) {
      const truckClass = fleetAsset.truckClass;
      const foundService = useCompanyDetailsStore().getServiceTypesList.find(
        (service: ServiceTypes) => service.shortServiceTypeName === truckClass,
      );
      if (foundService !== undefined) {
        fleetAssetRateTableItem =
          fleetAssetServiceRateTable.rateTableItems.find(
            (rate) =>
              rate.serviceTypeId === foundService.serviceTypeId &&
              rate.rateTypeId === JobRateType.TIME,
          );
      }
    } else {
      fleetAssetRateTableItem = fleetAssetServiceRateTable.rateTableItems.find(
        (rate) =>
          rate.serviceTypeId === fleetAssetServiceType &&
          rate.rateTypeId === JobRateType.TIME,
      );
    }
  }
  if (fleetAssetRateTableItem !== undefined) {
    // For a ZONE to ZONE rate job, replace the string rateTypeObject with the
    // zone data. Does nothing for other rate types.
    await initZoneToZoneRateTypeObjectIfRequired(
      RateEntityType.FLEET_ASSET,
      fleetAssetRateTableItem,
      jobDetails.fleetAssetId,
      jobDetails.pudItems,
    );

    fleetAssetRatePrimaryData.rate = initialiseRateTableItems(
      fleetAssetRateTableItem,
    );

    fleetAssetRatePrimaryData.outsideMetroRate =
      fleetAssetRateTableItem.rateTypeId === JobRateType.TIME &&
      fleetAssetServiceRateTable.outsideMetroRate
        ? fleetAssetServiceRateTable.outsideMetroRate
        : 0;

    fleetAssetRatePrimaryData.rateTableName =
      fleetAssetServiceRateTable.name || '';

    setFuelSurchargeRate({
      type: RateEntityType.FLEET_ASSET,
      rate: fleetAssetRatePrimaryData.rate,
      fuelSurcharge: fleetAssetFuelSurcharge,
      clientFuelSurchargeRate: clientFuelSurcharge.appliedFuelSurchargeRate,
      pudItems: jobDetails.pudItems,
    });

    // Calculate JobPrimaryRate.rateData
    setPrimaryRateTimeData(
      RateEntityType.FLEET_ASSET,
      jobDetails,
      fleetAssetRatePrimaryData,
      fleetAssetRateTableItem,
      workDiaryList,
      clientCommonAddressList,
      clientRatePrimaryData,
    );
  } else {
    throw new Error(
      `Fleet asset rate table item not found for job ${jobDetails.jobId}. Looking for service type: ${fleetAssetServiceType}, Rate type: ${fleetAssetRateType}`,
    );
  }
}

/**
 * Sets the rate data for the JobPrimaryRate object. This populates the field at
 * JobPrimaryRate.rateData with values from the event list.
 * @param type - The type of rate entity (client or fleet asset)
 * @param jobDetails - The job details object
 * @param jobPrimaryRate - The JobPrimaryRate object to update with rate data
 * @param rateTableItem - The currently applied rateTableItem
 * @param workDiaryList - The list of driver workDiaries for the job
 * @param commonAddressList - The client's common address (used for point to
 * point)
 * @param clientPrimaryRate - The client's primary rate. Only supplied when type
 * is FLEET_ASSET, such that we can pay the driver as a percentage of the
 * client's rate.
 */
function setPrimaryRateTimeData(
  type: RateEntityType,
  jobDetails: JobDetails,
  jobPrimaryRate: JobPrimaryRate,
  rateTableItem: RateTableItems,
  workDiaryList: WorkDiarySummary[],
  commonAddressList: ClientCommonAddress[],
  clientPrimaryRate?: JobPrimaryRate,
) {
  switch (rateTableItem.rateTypeId) {
    case JobRateType.TIME:
      timeRateHandler(
        jobDetails,
        jobPrimaryRate,
        rateTableItem.rateTypeObject as TimeRateType,
        workDiaryList,
        type,
      );
      break;
    case JobRateType.POINT_TO_POINT:
      if (type === RateEntityType.CLIENT) {
        const foundPointToPointItem = findCorrectPointToPointRate(
          jobPrimaryRate.rate.rateTypeObject as PointToPointRateType[],
          jobDetails,
          commonAddressList,
        );
        if (foundPointToPointItem) {
          jobPrimaryRate.rate.rateTypeObject = foundPointToPointItem;
        }
      } else {
        let pointToPointRate = (
          rateTableItem.rateTypeObject as PointToPointRateType[]
        )[0];

        if (!pointToPointRate) {
          pointToPointRate =
            rateTableItem.rateTypeObject as PointToPointRateType;
        }

        jobDetails.accounting.fleetAssetRates[0].rate.rateTypeObject =
          pointToPointRate;

        jobPrimaryRate.rate.rateTypeObject = pointToPointRate;
        let clientRate = 0;
        if (clientPrimaryRate) {
          clientRate = (
            clientPrimaryRate.rate.rateTypeObject as PointToPointRateType
          ).rate;
        }

        const driverPercentage = (
          jobPrimaryRate.rate.rateTypeObject as PointToPointRateType
        ).percentage;

        const percentToDecimal = driverPercentage / 100;

        (jobPrimaryRate.rate.rateTypeObject as PointToPointRateType).rate =
          RoundCurrencyValue(percentToDecimal * clientRate);

        (
          jobDetails.accounting.fleetAssetRates[0].rate
            .rateTypeObject as PointToPointRateType
        ).rate = RoundCurrencyValue(percentToDecimal * clientRate);
      }
      setRateDataTimes(jobPrimaryRate, jobDetails);
      break;
    case JobRateType.ZONE:
    case JobRateType.DISTANCE:
    case JobRateType.UNIT:
    case JobRateType.TRIP:
    case JobRateType.ZONE_TO_ZONE:
      setRateDataTimes(jobPrimaryRate, jobDetails);
      break;
  }
}

/**
 * Update the existing fuel surcharge rate with the correct value. This depends
 * on what applied fuel surcharge rate is set within the applied rate. When
 * clientFuelSurchargeRate is null we are calculating the clients fuel surcharge
 * rate. When it is not null we are calculating the fleet asset fuel surcharge
 * rate.
 */
export function setFuelSurchargeRate(
  params:
    | {
        type: RateEntityType.CLIENT;
        rate: RateTableItems;
        fuelSurcharge: ClientFuelSurchargeRate;
        clientFuelSurchargeRate?: number | null;
        pudItems: PUDItem[];
      }
    | {
        type: RateEntityType.FLEET_ASSET;
        rate: RateTableItems;
        fuelSurcharge: FleetAssetFuelSurchargeRate;
        clientFuelSurchargeRate?: number | null;
        pudItems: PUDItem[];
      },
): void {
  const { type, rate, fuelSurcharge, clientFuelSurchargeRate, pudItems } =
    params;
  let applicableFuelSurchargeId: number | null = null;
  switch (rate.rateTypeId) {
    case JobRateType.TIME:
      if (isTimeRateTypeObject(rate.rateTypeId, rate.rateTypeObject)) {
        applicableFuelSurchargeId = rate.rateTypeObject.appliedFuelSurchargeId;
      }
      break;

    case JobRateType.ZONE:
      if (clientFuelSurchargeRate !== null) {
        applicableFuelSurchargeId = (rate.rateTypeObject as ZoneRateType[])[0]
          .appliedFuelSurchargeId;
      } else {
        // if the client has any zones with fuel surcharge applied we set applied fuel surcharge id to 1
        const zoneRates = rate.rateTypeObject as ZoneRateType[];
        let fuelIsApplied: boolean = false;
        for (const pud of pudItems) {
          const zone = zoneRates.find(
            (x: ZoneRateType) => x.zone === pud.rateDetails.zoneReference,
          );
          if (zone && zone.appliedFuelSurchargeId === 1) {
            fuelIsApplied = true;
            break;
          }
        }
        applicableFuelSurchargeId = fuelIsApplied ? 1 : 3;
      }
      break;

    case JobRateType.DISTANCE:
      if (isDistanceRateTypeObject(rate.rateTypeId, rate.rateTypeObject)) {
        applicableFuelSurchargeId = rate.rateTypeObject.appliedFuelSurchargeId;
      }
      break;
    case JobRateType.POINT_TO_POINT:
      applicableFuelSurchargeId = (rate.rateTypeObject as PointToPointRateType)
        .appliedFuelSurchargeId;
      break;

    case JobRateType.TRIP:
      if (!rate.fuelSurcharge) {
        if (type === RateEntityType.CLIENT) {
          fuelSurcharge.appliedFuelSurchargeRate = 0;
        } else {
          fuelSurcharge.appliedFuelSurchargeRate = 0;
        }
      }
      break;
    case JobRateType.UNIT:
      if (clientFuelSurchargeRate !== null) {
        applicableFuelSurchargeId = (rate.rateTypeObject as UnitRate[])[0]
          .appliedFuelSurchargeId;
      } else {
        // if the client has any zones within a unit rate where fuel surcharge applied we set applied fuel surcharge id to 1
        const unitRates = rate.rateTypeObject as UnitRate[];
        let fuelIsApplied: boolean = false;
        for (const pud of pudItems) {
          const zone = unitRates.find(
            (x: UnitRate) => x.zoneId === pud.rateDetails.zoneReference,
          );
          if (zone && zone.appliedFuelSurchargeId === 1) {
            fuelIsApplied = true;
            break;
          }
        }
        applicableFuelSurchargeId = fuelIsApplied ? 1 : 3;
      }
      break;
    case JobRateType.ZONE_TO_ZONE:
      // Check the applied fuel surcharge id for each zone-to-zone rate type. If
      // at least 1 is APPLY (1), then set the applicable fuel surcharge id to
      // 1. If at least 1 is APPLY IF CLIENT FUEL LEVY IS GREATER THAN 0 (2),
      // then set the applicable fuel surcharge id to 2. Otherwise, set the
      // applicable fuel surcharge id to 3 (Don't Apply).
      if (isZoneToZoneRateTypeObject(rate.rateTypeId, rate.rateTypeObject)) {
        if (
          rate.rateTypeObject.some((rti) => rti.appliedFuelSurchargeId === 1)
        ) {
          applicableFuelSurchargeId = 1;
        } else if (
          rate.rateTypeObject.some((rti) => rti.appliedFuelSurchargeId === 2)
        ) {
          applicableFuelSurchargeId = 2;
        } else {
          applicableFuelSurchargeId = 3;
        }
      }
      break;
  }

  // Set fuel surcharge rate based on applied fuel id
  if (applicableFuelSurchargeId) {
    const fuelIsApplicable = isFuelSurchargeApplicable(
      applicableFuelSurchargeId,
      clientFuelSurchargeRate ?? null,
    );
    if (type === RateEntityType.CLIENT) {
      fuelSurcharge.appliedFuelSurchargeRate = fuelIsApplicable
        ? fuelSurcharge.appliedFuelSurchargeRate
        : 0;
    } else {
      fuelSurcharge.appliedFuelSurchargeRate = fuelIsApplicable
        ? fuelSurcharge.appliedFuelSurchargeRate
        : 0;
    }
  }
}

export function standbyFuelSurchargeApplies(rate: RateTableItems): boolean {
  let applyFuelSurchargeToStandby: boolean = false;
  switch (rate.rateTypeId) {
    case 1:
      applyFuelSurchargeToStandby = (rate.rateTypeObject as TimeRateType)
        .standbyFuelSurchargeApplies;
      break;
  }
  return applyFuelSurchargeToStandby;
}

// =============================================================================
// 1 - TIME RATE
// =============================================================================

export function timeRateHandler(
  jobDetails: JobDetails,
  primaryRateData: JobPrimaryRate,
  serviceRateItem: TimeRateType,
  workDiaryList: WorkDiarySummary[],
  rateEntityType: RateEntityType,
) {
  (primaryRateData.rateData as TimeTypeJobRateData[]).push(
    generateTimeRateData(
      jobDetails.pudItems[0],
      jobDetails,
      serviceRateItem,
      'START',
    ),
  );
  (primaryRateData.rateData as TimeTypeJobRateData[]).push(
    generateTimeRateData(
      jobDetails.pudItems[jobDetails.pudItems.length - 1],
      jobDetails,
      serviceRateItem,
      'END',
    ),
  );
  const standbyRateApplies = serviceRateItem.standbyRate > 0;
  primaryRateData.standbyDuration = calculateStandbyDurations(
    jobDetails,
    standbyRateApplies,
    workDiaryList,
    rateEntityType,
  );
  primaryRateData.standbyDuration = Object.assign(
    new StandbyDuration(),
    primaryRateData.standbyDuration,
  );
}

/**
 * Generates a TimeTypeJobRateData object for a given PUD item and job details.
 * This is used in the job pricing management component to calculate the
 * chargeable time for a given PUD item, and is filled into the input field for
 * the user to edit.
 *
 * @param pud - The PUD item.
 * @param jobDetails - The job details.
 * @param  serviceRateItem - The service rate item.
 * @param type - Indicates if it's the start leg.
 * @returns {TimeTypeJobRateData} - The generated time rate data.
 */
export function generateTimeRateData(
  pud: PUDItem,
  jobDetails: JobDetails,
  serviceRateItem: TimeRateType,
  type: 'START' | 'END',
): TimeTypeJobRateData {
  const jobRateDataAsset: TimeTypeJobRateData = new TimeTypeJobRateData();
  const companyDetailsStore = useCompanyDetailsStore();

  // Determine the PUD time based on whether it's the start leg or not
  const pudTime = jobDetails.returnSpecifiedEvent(
    type === 'START' ? 'ARRIVED' : 'FINISHED',
    pud.pudId,
  );

  if (pudTime) {
    const correctEventTime = pudTime.correctEventTime;
    const legDuration = returnLegDurationsTimeDifferential(
      type,
      serviceRateItem,
      jobDetails.legDurations,
    );

    jobRateDataAsset.begin = nearestMinutes(
      1,
      moment(correctEventTime + legDuration),
    )
      .tz(companyDetailsStore.userLocale)
      .valueOf();
  }

  jobRateDataAsset.pudId = pud.pudId ?? '';
  jobRateDataAsset.legTypeFlag = pud.legTypeFlag;
  return jobRateDataAsset;
}

/**
 * Used to return the amount of time that should be added or subtracted from the
 * start or end leg based on the first/last leg configurations in a time rate.
 * Returns a number in milliseconds, which can be added to the start or end leg
 * time to determine the chargeable time.
 * @param type - The type of leg (START or END)
 * @param serviceRateItem - The service rate item
 * @param legDurations - The leg durations from the rate table
 * @returns - The time differential in milliseconds
 */
export function returnLegDurationsTimeDifferential(
  type: 'START' | 'END',
  serviceRateItem: TimeRateType,
  legDurations: LegDuration,
): number {
  let diff = 0;
  if (type === 'START') {
    switch (serviceRateItem.firstLegTypeId) {
      case 2: // Depot
        diff = -legDurations.depotToFirstLeg;
        break;
      case 3: // NA
      case null:
        diff = 0;
        break;
    }
  } else {
    switch (serviceRateItem.lastLegTypeId) {
      case 2: // Depot
        diff = legDurations.depotToLastLeg;
        break;
      case 4: // Return
        diff = legDurations.returnToFirstPud;
        break;
      case 3: // NA
      case null:
        diff = 0;
        break;
    }
  }
  return diff;
}

// =============================================================================
// NON-TIME RATE
// =============================================================================

export function setRateDataTimes(
  primaryRateData: JobPrimaryRate,
  jobDetails: JobDetails,
) {
  for (const [pudIndex, pud] of jobDetails.pudItems.entries()) {
    const jobRateDataAsset: TimeTypeJobRateData = new TimeTypeJobRateData();
    let loadTime = 0;

    const foundStart = jobDetails.returnSpecifiedEvent('ARRIVED', pud.pudId);
    const endFound = jobDetails.returnSpecifiedEvent('FINISHED', pud.pudId);

    if (foundStart !== undefined && endFound !== undefined) {
      loadTime += endFound.correctEventTime - foundStart.correctEventTime;
      jobRateDataAsset.begin = foundStart.correctEventTime;
    }

    jobRateDataAsset.pudId = pud.pudId ? pud.pudId : '';
    jobRateDataAsset.legTypeFlag = pud.legTypeFlag;
    jobRateDataAsset.loading = loadTime;
    (primaryRateData.rateData as TimeTypeJobRateData[]).push(jobRateDataAsset);
  }
}

// =============================================================================
// POINT TO POINT
// =============================================================================

/**
 * Looks at the legs on the job and tries to find the associated point to point.
 *  @returns {PointToPointRateType} The associated point to point rate.
 */
export function findCorrectPointToPointRate(
  primaryRateData: PointToPointRateType[],
  jobDetails: JobDetails,
  clientCommonAddressList: ClientCommonAddress[],
): PointToPointRateType | undefined {
  const addressA = jobDetails.pudItems[0].address.formattedAddress;
  const addressB =
    jobDetails.pudItems[jobDetails.pudItems.length - 1].address
      .formattedAddress;

  const foundNicknamedAddressA = clientCommonAddressList.find(
    (addressNickname: ClientCommonAddress) =>
      addressNickname.address.formattedAddress === addressA,
  );

  const foundNicknamedAddressB = clientCommonAddressList.find(
    (clientCommonAddress: ClientCommonAddress) =>
      clientCommonAddress.address.formattedAddress === addressB,
  );
  if (
    foundNicknamedAddressA !== undefined &&
    foundNicknamedAddressB !== undefined
  ) {
    const foundPointToPointInRateIndex = primaryRateData.findIndex(
      (p2p: PointToPointRateType) => {
        return (
          (p2p.fromAddressReference === foundNicknamedAddressA._id &&
            p2p.toAddressReference === foundNicknamedAddressB._id) ||
          (p2p.fromAddressReference === foundNicknamedAddressB._id &&
            p2p.toAddressReference === foundNicknamedAddressA._id)
        );
      },
    );
    if (foundPointToPointInRateIndex !== -1) {
      return primaryRateData[foundPointToPointInRateIndex];
    }
  } else {
    console.error(
      'Point to point Rate: Could not match legs on job to a point to point rate.',
    );
  }
}

// =============================================================================
// ROUNDING
// =============================================================================
export function minimumBillingTimeAdjustments(invoiceDetails: JobPrimaryRate) {
  let clientMinChargeMultiplier: RateMultipliers | undefined;
  if (invoiceDetails.rate.rateTypeId === 1) {
    const multiplerId = (invoiceDetails.rate.rateTypeObject as TimeRateType)
      .minChargeMultiplier;
    clientMinChargeMultiplier = rateMultipliers.find(
      (rateMultiplier: RateMultipliers) => rateMultiplier.id === multiplerId,
    );
  }

  if (
    clientMinChargeMultiplier !== undefined &&
    clientMinChargeMultiplier !== null
  ) {
    const clientEpochMinCharge = clientMinChargeMultiplier.multiplier;

    const clientData: TimeTypeJobRateData[] =
      invoiceDetails.rateData as TimeTypeJobRateData[];

    // find total duration of client job and compare to min charge
    const clientStartRateData: TimeTypeJobRateData =
      clientData[0] as TimeTypeJobRateData;

    const clientEndRateData: TimeTypeJobRateData = clientData[
      clientData.length - 1
    ] as TimeTypeJobRateData;

    const clientStart = clientStartRateData.mostRecentBegin;
    const clientEnd = clientEndRateData.mostRecentBegin;

    const foundClientBreakRate: TimeTypeJobRateData | undefined =
      clientData.find((rate: TimeTypeJobRateData) => rate.legTypeFlag === 'B');
    let clientBreakDurationMilliseconds = 0;

    if (foundClientBreakRate) {
      const clientBreakLoadTime = foundClientBreakRate.mostRecentLoading;
      clientBreakDurationMilliseconds = clientBreakLoadTime;
    }

    const clientJobDuration =
      clientEnd - clientStart - clientBreakDurationMilliseconds;
    // TO DO MINUS BREAK DURATION

    if (clientJobDuration < clientEpochMinCharge) {
      if (clientEndRateData.beginEditList.length > 0) {
        clientEndRateData.beginEditList[
          clientEndRateData.beginEditList.length - 1
        ].newValue =
          clientStart + clientEpochMinCharge + clientBreakDurationMilliseconds;
      } else {
        clientEndRateData.begin =
          clientStart + clientEpochMinCharge + clientBreakDurationMilliseconds;
      }
    }
  }
}
// Return the number of milliseconds of rest time required for the provided
// workingDuration, as dictated by fatigue management requirements
export function requiredBreakFromDuration(workingDuration: number): number {
  const breakPoint1 =
    moment.duration(5, 'hours').asMilliseconds() +
    moment.duration(30, 'minutes').asMilliseconds();
  const breakPoint2 = moment.duration(8, 'hours').asMilliseconds();
  const breakPoint3 = moment.duration(11, 'hours').asMilliseconds();
  const breakPoint4 = moment.duration(1, 'days').asMilliseconds();
  const breakPoint5 = moment.duration(7, 'days').asMilliseconds();
  const breakPoint6 = moment.duration(14, 'days').asMilliseconds();
  if (workingDuration < breakPoint1) {
    return 0;
  }
  if (workingDuration >= breakPoint1 && workingDuration < breakPoint2) {
    return moment.duration(15, 'minutes').asMilliseconds();
  }
  if (workingDuration >= breakPoint2 && workingDuration < breakPoint3) {
    return moment.duration(30, 'minutes').asMilliseconds();
  }
  if (workingDuration >= breakPoint3 && workingDuration < breakPoint4) {
    return moment.duration(1, 'hours').asMilliseconds();
  }
  if (workingDuration >= breakPoint4 && workingDuration < breakPoint5) {
    return moment.duration(7, 'hours').asMilliseconds();
  }
  if (workingDuration >= breakPoint5 && workingDuration < breakPoint6) {
    return moment.duration(1, 'day').asMilliseconds();
  }
  return 0;
}
/**
 * Generates accounting data for a finished job and updates the jobAccountingDetails object.
 * This will get attached to jobDetails after this function is called.
 *
 * @param jobDetails - The details of the job.
 * @param jobAccountingDetails - The accounting details of the job.
 * @param jobDurationData - The duration data of the finished job.
 * @param driverRegisteredForGst - Indicates if the driver is registered for GST.
 */
export function generateAccountingFinishedJobData(
  jobDetails: JobDetails,
  jobAccountingDetails: JobAccountingDetails,
  jobDurationData: FinishedJobDetails,
  driverRegisteredForGst: boolean,
) {
  if (!jobAccountingDetails.clientRates?.[0]?.rate) {
    throw new Error(
      'Client rates empty is missing or invalid. Cannot generate FinishedJobData.',
    );
  }
  if (!jobAccountingDetails.fleetAssetRates?.[0]?.rate) {
    throw new Error(
      'Fleet asset rate is missing or invalid. Cannot generate FinishedJobData.',
    );
  }
  if (!jobDurationData) {
    throw new Error(
      'Job duration data is missing or invalid. Cannot generate FinishedJobData.',
    );
  }
  const clientRates = jobAccountingDetails.clientRates[0];
  const fleetAssetRates = jobAccountingDetails.fleetAssetRates[0];

  if (!jobAccountingDetails.finishedJobData) {
    jobAccountingDetails.finishedJobData = new FinishedJobData();
  }

  const actualJobDate = jobDetails.pudItems[0]?.epochTime || 0;

  jobAccountingDetails.finishedJobData.actualJobDate = moment(actualJobDate)
    .tz(useCompanyDetailsStore().userLocale)
    .startOf('day')
    .valueOf();
  jobAccountingDetails.finishedJobData.readableJobDate = returnFormattedDate(
    actualJobDate,
    'DD/MM/YY',
  );
  jobAccountingDetails.finishedJobData.loadDurations =
    jobDurationData.loadDurations;
  jobAccountingDetails.finishedJobData.clientDurations =
    jobDurationData.clientDurations;
  jobAccountingDetails.finishedJobData.fleetAssetDurations =
    jobDurationData.fleetAssetDurations;

  // Add rate-specific data
  let clientZoneRate = 0;

  // Handle client zone rate
  if (
    isZoneRateTypeObject(
      clientRates.rate.rateTypeId,
      clientRates.rate.rateTypeObject,
    )
  ) {
    const clientRateData = generateZoneBreakdowns(
      jobDetails.pudItems,
      clientRates.rate.rateTypeObject,
      jobAccountingDetails.clientServiceRateVariations
        ?.clientAdjustmentPercentage,
    );
    clientZoneRate = clientRateData.rate;
    jobAccountingDetails.finishedJobData.clientRateData =
      clientRateData.zoneCharges;
  } else if (
    isDistanceRateTypeObject(
      clientRates.rate.rateTypeId,
      clientRates.rate.rateTypeObject,
    )
  ) {
    const distanceAdditionalData =
      jobAccountingDetails.additionalData?.distanceRate;
    if (!distanceAdditionalData) {
      throw new Error('Distance rate additional data is missing or invalid.');
    }

    // Check which additional travel to use based on the chargeBasis
    const additionalTravelDistances =
      clientRates.rate.rateTypeObject.chargeBasis === ChargeBasis.SUBURB_CENTRES
        ? distanceAdditionalData.additionalTravelSuburbCentres.client
        : distanceAdditionalData.additionalTravelDistances.client;

    // Return the appropriate original distance
    const originalDistance = returnOriginalDistanceForChargeBasis(
      distanceAdditionalData.chargeableClientDistance,
      clientRates.rate.rateTypeObject.chargeBasis,
    );
    // Generate the distance rate data
    const distanceRateData = generateDistanceRateData({
      travelDistance: originalDistance,
      distanceRate: clientRates.rate.rateTypeObject,
      additionalDistances: additionalTravelDistances,
      additionalDurations: new LegDuration(),
      editedTravelDistance:
        distanceAdditionalData.chargeableClientDistance?.edited,
      variancePct:
        jobAccountingDetails.clientServiceRateVariations
          ?.clientAdjustmentPercentage,
    });
    if (!distanceRateData) {
      throw new Error(
        'Error generating DistanceRateData in generateAccountingFinishedJobData',
      );
    }
    distanceAdditionalData.chargeableClientDistance.edited =
      distanceRateData.editedTravelDistance;
    jobAccountingDetails.finishedJobData.clientRateData = distanceRateData;
  } else if (
    // Handle client unit rate
    isUnitRateTypeObject(
      clientRates.rate.rateTypeId,
      clientRates.rate.rateTypeObject,
    )
  ) {
    const calculatedUnitRate = unitRateCalculation(
      jobDetails.pudItems,
      clientRates.rate.rateTypeObject,
      RateEntityType.CLIENT,
      null,
      null,
      jobAccountingDetails.clientServiceRateVariations
        ?.clientAdjustmentPercentage,
    );

    if (calculatedUnitRate) {
      jobAccountingDetails.finishedJobData.clientRateData = calculatedUnitRate;
    }
  } else if (
    isZoneToZoneRateTypeObject(
      clientRates.rate.rateTypeId,
      clientRates.rate.rateTypeObject,
    )
  ) {
    const zoneToZoneRateData = generateZoneToZoneRateData({
      type: RateEntityType.CLIENT,
      rateTypes: clientRates.rate.rateTypeObject,
      isGstRegistered: true,
      variancePct:
        jobAccountingDetails.clientServiceRateVariations
          ?.clientAdjustmentPercentage,
    });
    if (zoneToZoneRateData === null) {
      throw new Error(
        'generateZoneToZoneRateData failed for CLIENT in generateAccountingFinishedJobData.',
      );
    }
    jobAccountingDetails.finishedJobData.clientRateData = zoneToZoneRateData;
  }

  // Handle fleet asset zone rate
  if (
    isZoneRateTypeObject(
      fleetAssetRates.rate.rateTypeId,
      fleetAssetRates.rate.rateTypeObject,
    )
  ) {
    jobAccountingDetails.finishedJobData.fleetAssetRateData =
      fleetAssetZoneBreakDown(
        fleetAssetRates.rate.rateTypeObject,
        clientZoneRate,
        driverRegisteredForGst,
        jobAccountingDetails.clientServiceRateVariations
          ?.fleetAssetAdjustmentPercentage,
      ).zoneCharges;
  } else if (
    isDistanceRateTypeObject(
      fleetAssetRates.rate.rateTypeId,
      fleetAssetRates.rate.rateTypeObject,
    )
  ) {
    const distanceAdditionalData =
      jobAccountingDetails.additionalData?.distanceRate;
    if (!distanceAdditionalData) {
      throw new Error('Distance rate additional data is missing or invalid.');
    }
    // Check which additional travel to use based on the chargeBasis
    const additionalTravelDistances =
      fleetAssetRates.rate.rateTypeObject.chargeBasis ===
      ChargeBasis.SUBURB_CENTRES
        ? distanceAdditionalData.additionalTravelSuburbCentres.fleetAsset
        : distanceAdditionalData.additionalTravelDistances.fleetAsset;

    // Return the appropriate original distance
    const originalDistance = returnOriginalDistanceForChargeBasis(
      distanceAdditionalData.chargeableFleetAssetDistance,
      fleetAssetRates.rate.rateTypeObject.chargeBasis,
    );

    // Generate the distance rate data
    const distanceRateData = generateDistanceRateData({
      travelDistance: originalDistance,
      distanceRate: fleetAssetRates.rate.rateTypeObject,
      additionalDistances: additionalTravelDistances,
      additionalDurations: new LegDuration(),
      editedTravelDistance:
        distanceAdditionalData.chargeableFleetAssetDistance?.edited,
      isGstRegistered: driverRegisteredForGst,
      variancePct:
        jobAccountingDetails.clientServiceRateVariations
          ?.fleetAssetAdjustmentPercentage,
    });
    if (!distanceRateData) {
      throw new Error(
        'Error generating DistanceRateData in generateAccountingFinishedJobData',
      );
    }
    jobAccountingDetails.finishedJobData.fleetAssetRateData = distanceRateData;
  } else if (
    // Handle fleet asset unit rate
    isUnitRateTypeObject(
      fleetAssetRates.rate.rateTypeId,
      fleetAssetRates.rate.rateTypeObject,
    )
  ) {
    const clientRateData = jobAccountingDetails.finishedJobData
      .clientRateData as UnitRateData;
    const calculatedUnitRate = unitRateCalculation(
      jobDetails.pudItems,
      fleetAssetRates.rate.rateTypeObject,
      RateEntityType.FLEET_ASSET,
      clientRateData.chargesTotalExclGst,
      driverRegisteredForGst,
      jobAccountingDetails.clientServiceRateVariations
        ?.fleetAssetAdjustmentPercentage,
    );

    if (calculatedUnitRate) {
      jobAccountingDetails.finishedJobData.fleetAssetRateData =
        calculatedUnitRate;
    }
  } else if (
    isZoneToZoneRateTypeObject(
      fleetAssetRates.rate.rateTypeId,
      fleetAssetRates.rate.rateTypeObject,
    )
  ) {
    if (
      !isZoneToZoneRateData(
        clientRates.rate.rateTypeId,
        jobAccountingDetails.finishedJobData.clientRateData,
      )
    ) {
      throw new TypeError(
        'Client rate is not a ZoneToZoneRateType object. Cannot calculate fleet asset rate.',
      );
    }
    const zoneToZoneRateData = generateZoneToZoneRateData({
      type: RateEntityType.FLEET_ASSET,
      rateTypes: fleetAssetRates.rate.rateTypeObject,
      isGstRegistered: true,
      clientSubtotals:
        jobAccountingDetails.finishedJobData.clientRateData.zoneRateSubtotals,
      variancePct:
        jobAccountingDetails.clientServiceRateVariations
          ?.fleetAssetAdjustmentPercentage,
    });
    if (zoneToZoneRateData === null) {
      throw new Error(
        'generateZoneToZoneRateData failed for FLEET_ASS in generateAccountingFinishedJobData.',
      );
    }
    jobAccountingDetails.finishedJobData.fleetAssetRateData =
      zoneToZoneRateData;
  }
}

export function setDefaultFleetAssetServiceRate(
  fleetAssetServiceRate: FleetAssetServiceRate | null,
  latestKnownServiceRate: FleetAssetServiceRate | null,
  fleetAssetId: string,
) {
  if (fleetAssetServiceRate === null) {
    fleetAssetServiceRate = new FleetAssetServiceRate();
    generateStockServiceRate(
      RateEntityType.FLEET_ASSET,
      fleetAssetServiceRate,
      fleetAssetId,
    );
  } else {
    fleetAssetServiceRate.fleetAssetId = fleetAssetId;
    fleetAssetServiceRate.tableId = undefined;
    delete fleetAssetServiceRate._id;
    fleetAssetServiceRate.validToDate = null;

    if (
      latestKnownServiceRate !== null &&
      latestKnownServiceRate.validToDate !== null
    ) {
      fleetAssetServiceRate.validFromDate = moment(
        latestKnownServiceRate.validToDate,
      )
        .tz(useCompanyDetailsStore().userLocale)
        .add(1, 'day')
        .startOf('day')
        .valueOf();
    } else {
      fleetAssetServiceRate.validFromDate = returnStartOfDayFromEpoch();
    }
  }
}
export function setDefaultClientServiceRate(
  clientServiceRate: ClientServiceRate | null,
  latestKnownServiceRate: ClientServiceRate | null,
  clientId: string,
) {
  if (clientServiceRate === null) {
    clientServiceRate = new ClientServiceRate();
    generateStockServiceRate(
      RateEntityType.CLIENT,
      clientServiceRate,
      clientId,
    );
  } else {
    clientServiceRate.clientId = clientId;
    clientServiceRate.tableId = undefined;
    delete clientServiceRate._id;
    clientServiceRate.validToDate = null;

    if (
      latestKnownServiceRate !== null &&
      latestKnownServiceRate.validToDate !== null
    ) {
      clientServiceRate.validFromDate = moment(
        latestKnownServiceRate.validToDate,
      )
        .tz(useCompanyDetailsStore().userLocale)
        .add(1, 'day')
        .startOf('day')
        .valueOf();
    } else {
      clientServiceRate.validFromDate = returnStartOfDayFromEpoch();
    }
  }
}

export function generateStockServiceRate(
  type: RateEntityType,
  serviceRate: FleetAssetServiceRate | ClientServiceRate | null,
  entityId: string,
) {
  if (serviceRate === null) {
    if (type === RateEntityType.FLEET_ASSET) {
      serviceRate = new FleetAssetServiceRate();
    } else {
      serviceRate = new ClientServiceRate();
    }
  }
  if (type === RateEntityType.FLEET_ASSET) {
    (serviceRate as FleetAssetServiceRate).fleetAssetId = entityId;
  } else {
    (serviceRate as ClientServiceRate).clientId = entityId;
  }
  serviceRate.tableId = undefined;
  serviceRate.rateTableItems = [];
  serviceRate.company = sessionManager.getCompanyId();
  serviceRate.division = sessionManager.getDivisionId();
  serviceRate.validFromDate = moment()
    .tz(useCompanyDetailsStore().userLocale)
    .startOf('day')
    .valueOf();
  serviceRate.validToDate = null;

  delete serviceRate._id;
}

export function returnLatestKnownFleetAssetServiceRate(
  serviceRates: FleetAssetServiceRate[],
): FleetAssetServiceRate | null {
  if (serviceRates.length > 0) {
    return serviceRates.reduce(
      (prev: FleetAssetServiceRate, current: FleetAssetServiceRate) => {
        return prev.validToDate! > current.validToDate! ? prev : current;
      },
    );
  } else {
    return null;
  }
}
export function returnLatestKnownClientServiceRate(
  serviceRates: ClientServiceRate[],
): ClientServiceRate | null {
  if (serviceRates.length > 0) {
    return serviceRates.reduce(
      (prev: ClientServiceRate, current: ClientServiceRate) => {
        return prev.validToDate! > current.validToDate! ? prev : current;
      },
    );
  } else {
    return null;
  }
}

export function calculateBreakDuration(
  jobAccountingDetails: JobAccountingDetails,
) {
  // CLIENT BREAK HANDLING ===============================
  const clientRates = jobAccountingDetails.clientRates[0];
  if (!clientRates.breakDuration) {
    clientRates.breakDuration = new BreakDuration();
  }
  const isClientTimeRate: boolean =
    jobAccountingDetails.clientRates[0].rate.rateTypeId === 1;
  // Sum totals from standby
  const clientStandbyTotal =
    isClientTimeRate &&
    clientRates.standbyDuration &&
    clientRates.standbyDuration.durations.length
      ? clientRates.standbyDuration.durations.reduce(
          (total, curr) => total + curr.breakOverlapDurationInMilliseconds,
          0,
        )
      : 0;

  // Check that client has demurrage, then total reduce for total
  const clientDemurrageTotal =
    !isClientTimeRate &&
    jobAccountingDetails.finishedJobData &&
    jobAccountingDetails.finishedJobData.clientDemurrageBreakdown &&
    jobAccountingDetails.finishedJobData.clientDemurrageBreakdown.length
      ? jobAccountingDetails.finishedJobData.clientDemurrageBreakdown.reduce(
          // Return sum of all break overlaps
          (total, curr) => total + curr.breakOverlapDurationInMilliseconds,
          0,
        )
      : 0;
  clientRates.breakDuration.breakSummaryList = clientRates.breakDuration
    .breakSummaryList
    ? clientRates.breakDuration.breakSummaryList
    : [];
  // Sum all breaks where chargeClient is true for overall break total
  clientRates.breakDuration.duration =
    clientRates.breakDuration.breakSummaryList.reduce(
      (total, curr) =>
        total + (!curr.chargeClient ? curr.durationInMilliseconds : 0),
      0,
    );

  // 'Other' type breaks are the overlap from STANDBY or DEMURRAGE
  clientRates.breakDuration.otherBreakDurationInMilliseconds =
    clientStandbyTotal + clientDemurrageTotal;
  // 'Freight' type breaks are the difference between the BREAK TOTAL and OTHER
  // If duration is lower than the other breaks duration then return 0, as
  // duration should never be negative
  clientRates.breakDuration.freightBreakDurationInMilliseconds =
    clientRates.breakDuration.duration >=
    clientRates.breakDuration.otherBreakDurationInMilliseconds
      ? clientRates.breakDuration.duration -
        clientRates.breakDuration.otherBreakDurationInMilliseconds
      : 0;

  // FLEET ASSET BREAKS HANDLING =================================
  const fleetAssetRates = jobAccountingDetails.fleetAssetRates[0];
  if (!fleetAssetRates.breakDuration) {
    fleetAssetRates.breakDuration = new BreakDuration();
  }

  const isFleetAssetTimeRate: boolean =
    jobAccountingDetails.fleetAssetRates[0].rate.rateTypeId === 1;
  // Sum totals from standby
  const fleetAssetStandbyTotal =
    isFleetAssetTimeRate &&
    fleetAssetRates.standbyDuration &&
    fleetAssetRates.standbyDuration.durations.length
      ? fleetAssetRates.standbyDuration.durations.reduce(
          (total, curr) => total + curr.breakOverlapDurationInMilliseconds,
          0,
        )
      : 0;
  // Check that fleetAsset has demurrage, then total reduce for total
  const fleetAssetDemurrageTotal =
    !isFleetAssetTimeRate &&
    jobAccountingDetails.finishedJobData &&
    jobAccountingDetails.finishedJobData.fleetAssetDemurrageBreakdown &&
    jobAccountingDetails.finishedJobData.fleetAssetDemurrageBreakdown.length
      ? jobAccountingDetails.finishedJobData.fleetAssetDemurrageBreakdown.reduce(
          // Return sum of all break overlaps
          (total, curr) => total + curr.breakOverlapDurationInMilliseconds,
          0,
        )
      : 0;
  fleetAssetRates.breakDuration.breakSummaryList = fleetAssetRates.breakDuration
    .breakSummaryList
    ? fleetAssetRates.breakDuration.breakSummaryList
    : [];
  // Sum all breaks where payFleetAsset is true for overall break total
  fleetAssetRates.breakDuration.duration =
    fleetAssetRates.breakDuration.breakSummaryList.reduce(
      (total, curr) =>
        total + (!curr.payFleetAsset ? curr.durationInMilliseconds : 0),
      0,
    );
  // 'Other' type breaks are the overlap from STANDBY or DEMURRAGE
  fleetAssetRates.breakDuration.otherBreakDurationInMilliseconds =
    fleetAssetStandbyTotal + fleetAssetDemurrageTotal;
  // 'Freight' type breaks are the difference between the BREAK TOTAL and OTHER
  // If duration is lower than the other breaks duration then return 0, as
  // duration should never be negative
  fleetAssetRates.breakDuration.freightBreakDurationInMilliseconds =
    fleetAssetRates.breakDuration.duration >=
    fleetAssetRates.breakDuration.otherBreakDurationInMilliseconds
      ? fleetAssetRates.breakDuration.duration -
        fleetAssetRates.breakDuration.otherBreakDurationInMilliseconds
      : 0;
}
