import { RoundCurrencyValue } from '@/helpers/AccountingHelpers/CurrencyHelpers';
import { returnFormattedDate } from '@/helpers/DateTimeHelpers/DateTimeHelpers';
import {
  returnCorrectDuration,
  returnFormattedVariancePercent,
  returnVariancePrefix,
} from '@/helpers/DateTimeHelpers/DurationHelpers';
import { logConsoleError } from '@/helpers/LogHelpers/LogHelpers';
import { isDistanceRateData } from '@/helpers/RateHelpers/RateDataHelpers';
import { DistanceRateTravelSummary } from '@/interface-models/Generic/Accounting/AdditionalAccountingData';
import { Portal } from '@/interface-models/Generic/Portal';
import { DistanceBetweenSuburbsResponse } from '@/interface-models/Generic/Route/DistanceBetweenSuburbs/DistanceBetweenSuburbsResponse';
import { ORSMatrix } from '@/interface-models/Generic/Route/ORSMatrix';
import JobStatusUpdate from '@/interface-models/Jobs/Event/JobStatusUpdate';
import type { JobDetails } from '@/interface-models/Jobs/JobDetails';
import { JobRouteProgress } from '@/interface-models/Jobs/JobRouteProgress';
import PUDItem from '@/interface-models/Jobs/PUD/PUDItem';
import { WorkStatus } from '@/interface-models/Jobs/WorkStatus';
import { RateEntityType } from '@/interface-models/ServiceRates/RateEntityType';
import { ChargeBasis } from '@/interface-models/ServiceRates/ServiceTypes/DistanceRate/ChargeBasis';
import { useAddressingStore } from '@/store/modules/AddressingStore';
import { useGpsStore } from '@/store/modules/GpsStore';
import { sessionManager } from '@/store/session/SessionState';
import moment from 'moment-timezone';

export const ALLOWED_TRAVEL_VARIANCE_PERCENT = 10; // 10% variance allowed for travel time

/**
 * Returns the default distance when initialise the chargeable distance in the
 * additional accounting information. If the editedTravelDistance is present in
 * the rate date, it will return that. Otherwise it will check the chargeBasis
 * and return the appropriate distance.
 * @param type - The type of rate entity to return the default distance for, either CLIENT or FLEET_ASSET.
 * @param jobDetails - The job details to return the default distance for.
 * @param chargeBasis - The charge basis to use when calculating the default distance.
 * @returns
 */
export async function returnKeyJobTravelDistances(
  type: RateEntityType,
  jobDetails: JobDetails,
): Promise<DistanceRateTravelSummary> {
  const chargeableDistance: DistanceRateTravelSummary = {
    plannedRoute: 0,
    suburbCentres: 0,
    gpsData: 0,
    edited: undefined,
  };
  // Check if the rate data is present and already contains an edited distance.
  // If so, return that.
  if (type === RateEntityType.CLIENT) {
    const clientRateTableItem = jobDetails.accounting?.clientRates?.[0]?.rate;
    if (
      isDistanceRateData(
        clientRateTableItem?.rateTypeId,
        jobDetails.accounting?.finishedJobData?.clientRateData,
      ) &&
      jobDetails.accounting.finishedJobData.clientRateData.editedTravelDistance
    ) {
      const editedDistance =
        jobDetails.accounting.finishedJobData.clientRateData
          .editedTravelDistance;
      chargeableDistance.edited = editedDistance;
    }
  } else {
    const driverRateTableItem =
      jobDetails.accounting?.fleetAssetRates?.[0]?.rate;
    if (
      isDistanceRateData(
        driverRateTableItem?.rateTypeId,
        jobDetails.accounting?.finishedJobData?.fleetAssetRateData,
      ) &&
      jobDetails.accounting.finishedJobData.fleetAssetRateData
        .editedTravelDistance
    ) {
      const editedDistance =
        jobDetails.accounting.finishedJobData.fleetAssetRateData
          .editedTravelDistance;
      chargeableDistance.edited = editedDistance;
    }
  }

  // Helper function to be concise
  const getDistance = async (basis: ChargeBasis) => {
    return await computeDefaultDistanceForChargeBasis(basis, jobDetails, type);
  };

  const [plannedRouteDistance, gpsDataDistance, suburbCentresDistance] =
    await Promise.all([
      getDistance(ChargeBasis.ANTICIPATED_ROUTE),
      getDistance(ChargeBasis.DISTANCE_TRAVELLED),
      getDistance(ChargeBasis.SUBURB_CENTRES),
    ]);

  chargeableDistance.plannedRoute = plannedRouteDistance;
  chargeableDistance.gpsData = gpsDataDistance;
  chargeableDistance.suburbCentres = suburbCentresDistance;

  return chargeableDistance;
}

/**
 * Computes and returns the default distance for a given charge basis. Used so we can construct the additionalData for a distance rate job, where we list all of the options for the original distance.
 * @param chargeBasis - The charge basis to use when calculating the default distance.
 * @param jobDetails - The job details to calculate the default distance for.
 * @param type - The type of rate entity to calculate the default distance for, either CLIENT or FLEET_ASSET.
 * @returns - The default distance for the given charge basis.
 */
export async function computeDefaultDistanceForChargeBasis(
  chargeBasis: ChargeBasis,
  jobDetails: JobDetails,
  type: RateEntityType,
): Promise<number> {
  const plannedDistance = jobDetails.plannedRoute?.overallDistanceInKm ?? 0;
  if (chargeBasis === ChargeBasis.ANTICIPATED_ROUTE) {
    console.debug(
      `Using planned distance for ANTICIPATED_ROUTE. Type: ${type} - ChargeBasis: ${chargeBasis}. Returned value: ${plannedDistance}`,
    );
    return plannedDistance;
  } else if (chargeBasis === ChargeBasis.DISTANCE_TRAVELLED) {
    if (jobDetails.workStatus < WorkStatus.DRIVER_COMPLETED) {
      return plannedDistance;
    }
    const distanceTravelled = await requestJobDistanceFromGps(jobDetails);
    console.debug(
      `Requesting job distance for DISTANCE_TRAVELLED. Type: ${type} - ChargeBasis: ${chargeBasis}. Returned value: ${distanceTravelled}`,
    );
    return distanceTravelled;
  } else {
    const suburbCentresDistance =
      await calculateSuburbCentresDistance(jobDetails);
    console.debug(
      `Requesting job distance for SUBURB_CENTRES. Type: ${type} - ChargeBasis: ${chargeBasis}. Returned value: ${suburbCentresDistance}`,
    );
    return suburbCentresDistance;
  }
}
/**
 * Request and response for fetching the distance travelled by a driver over a
 * period of time, as calculated from GPS data.
 */
export async function requestJobDistanceFromGps(
  jobDetails: JobDetails,
): Promise<number> {
  if (!jobDetails.driverId || !jobDetails.driverId) {
    return 0;
  }
  const request = jobDetails.returnJobGpsRequest();
  if (!request) {
    logConsoleError(
      `Could not create GPS request for job ${jobDetails.displayId}`,
    );
    return 0;
  }
  const response = await useGpsStore().retrieveDistanceFromGpsData(request);
  return response?.distanceTravelledInKm
    ? RoundCurrencyValue(response.distanceTravelledInKm)
    : 0;
}

/**
 * Calculates the total distance in kilometres between the suburb centres
 * associated with a job.
 *
 * This function retrieves suburb centre summaries for the given job and sums
 * the distances between each segment. The result is rounded to three decimal
 * places.
 *
 * @param job - The job details containing information about pickup and dropoff
 * items.
 * @returns A promise that resolves to the total distance in kilometers between
 * suburb centres, rounded to three decimal places.
 */
async function calculateSuburbCentresDistance(
  job: JobDetails,
): Promise<number> {
  // const addresses = job.pudItems.map((item) => item.address);
  const result = await requestSuburbCentreSummariesForJob(job);
  if (!result) {
    return 0;
  }

  const distanceInMetres = result.response.distanceInfo.reduce(
    (acc, segment) => acc + segment.distance,
    0,
  );
  return RoundCurrencyValue(distanceInMetres / 1000, 3);
}

/**
 * Fetches a list of total distance from a list of AddressAU objects.
 * @param addresses - The list of AddressAU objects to calculate the distance between.
 * @returns - The total distance in km.
 */
export async function requestSuburbCentreSummariesForJob(
  job: JobDetails,
): Promise<DistanceBetweenSuburbsResponse | null> {
  const addresses = job.pudItems.map((item) => item.address);
  if (addresses.length === 0) {
    return null;
  }
  const result =
    await useAddressingStore().getDistanceBetweenSuburbs(addresses);
  if (
    !result?.response ||
    (addresses.length >= 2 && !result?.response?.distanceInfo?.length)
  ) {
    logConsoleError('No distance info returned from suburb centres');
    return null;
  }

  return result;
}

/**
 * Calculates and sets the progress of a job route, including timing and travel metrics for each stop.
 *
 * For each stop (finished and unfinished), this function:
 * - Determines actual and expected arrival/finish times.
 * - Calculates load times and differences from expected values.
 * - Calculates estimated and actual travel times between stops, and their differences.
 * - Populates a JobRouteProgress object for each stop, used for UI and reporting.
 *
 * @param jobDetails - The job details object containing all PUD items and events.
 * @param matrix - The ORSMatrix routing response containing durations between stops. Can be null if the job is complete or has only one leg.
 * @param jobComplete - If true, indicates the job is complete and route data may be omitted.
 * @param oneLegOnJob - If true, indicates the job only has one leg (special case for progress calculation).
 */
export function setJobRouteProgress(
  jobDetails: JobDetails,
  matrix: ORSMatrix | null,
  jobComplete: boolean = false,
  oneLegOnJob = false,
) {
  try {
    // Defensive: Ensure route matrix is present unless job is complete or has only one leg.
    if ((!matrix || !matrix.durations) && !jobComplete && !oneLegOnJob) {
      throw new Error(
        'Route progress matrix was not defined for jobId ' + jobDetails.jobId,
      );
    }
    // Determine if this is being viewed from the client portal (affects display logic).
    const isClientPortal = sessionManager.getPortalType() === Portal.CLIENT;
    // Check if the job has started (status is IN_PROGRESS).
    const jobStarted: boolean =
      jobDetails.workStatus === WorkStatus.IN_PROGRESS;
    // Array to accumulate progress data for each stop.
    const jobRouteProgress: JobRouteProgress[] = [];
    // Current timestamp for calculations.
    const currentTime = moment().valueOf();
    // Check if the first PUD item is ASAP (timeDefinition === 9).
    const isAsap: boolean = jobDetails.pudItems[0].timeDefinition === 9;
    // Gather all finished stops (PUDs with status 'FINISHED').
    const finishedStops: PUDItem[] = jobDetails.pudItems.filter(
      (stop: PUDItem) =>
        (stop.legTypeFlag === 'P' || stop.legTypeFlag === 'D') &&
        stop.pudId &&
        stop.status === 'FINISHED',
    );
    // --- Process finished stops ---
    for (let i = 0; i < finishedStops.length; i++) {
      const pudItem: PUDItem = finishedStops[i];
      // Find the arrival and finished events for this stop.
      const arrivalEvent: JobStatusUpdate | undefined =
        jobDetails.returnSpecifiedEvent('ARRIVED', pudItem.pudId);
      const finishedEvent: JobStatusUpdate | undefined =
        jobDetails.returnSpecifiedEvent('FINISHED', pudItem.pudId);
      // Calculate the expected arrival time (add 1 hour if ASAP).
      const definedArrivalTime = pudItem.epochTime + (isAsap ? 3600000 : 0);
      // Extract actual arrival and finish times from events.
      const actualArrivalTime: number | null = arrivalEvent
        ? arrivalEvent.correctEventTime
        : null;
      const actualArrivalTimeReadable: string = actualArrivalTime
        ? returnFormattedDate(actualArrivalTime, 'HH:mm')
        : '';
      const actualFinishedTime: number | null = finishedEvent
        ? finishedEvent.correctEventTime
        : null;
      const actualFinishedTimeReadable: string = actualFinishedTime
        ? returnFormattedDate(actualFinishedTime, 'HH:mm')
        : '';
      // Calculate actual load time (time spent at stop).
      const actualLoadTime =
        actualFinishedTime && actualArrivalTime
          ? actualFinishedTime - actualArrivalTime
          : null;
      // Calculate the difference between expected and actual load time.
      const differenceInLoadTime =
        actualFinishedTime && actualArrivalTime
          ? actualFinishedTime - actualArrivalTime - pudItem.loadTime
          : null;

      // --- Travel Time Calculation ---
      const {
        estimatedTravelTime,
        estimatedTravelTimeReadable,
        actualTravelTime,
        actualTravelTimeReadable,
        differenceInTravelTimeMs,
        differenceInTravelTimePercent,
        differenceInTravelTimePercentReadable,
      }: {
        estimatedTravelTime: number | null;
        estimatedTravelTimeReadable: string;
        actualTravelTime: number | null;
        actualTravelTimeReadable: string;
        differenceInTravelTimeMs: number | null;
        differenceInTravelTimePercent: number | null;
        differenceInTravelTimePercentReadable: string;
      } = calculateTravelTimeDifferences(jobDetails, pudItem.pudId);

      // Ensure arrival and load time data is present for finished stops.
      if (finishedEvent) {
        if (!actualArrivalTime) {
          throw new Error(
            'Stop has a defined finished event but no arrival time is known for jobId ' +
              jobDetails.jobId,
          );
        }
        if (differenceInLoadTime !== 0 && !differenceInLoadTime) {
          throw new Error(
            'Difference in defined load time and actual load time could not be calculated for jobId ' +
              jobDetails.jobId,
          );
        }
        // Calculate difference between actual and expected arrival times.
        const arrivalDifference: number =
          actualArrivalTime - definedArrivalTime;
        // Suffix for arrival difference (On Time, Late, Early).
        const arrivalDifferenceSuffix: string =
          arrivalDifference === 0
            ? 'On Time'
            : arrivalDifference > 0
              ? ' Late'
              : ' Early';
        // Human-readable arrival difference (unless client portal).
        const arrivalDifferenceInMins: string = isClientPortal
          ? ''
          : returnCorrectDuration(Math.abs(arrivalDifference)) +
            arrivalDifferenceSuffix;
        // Suffix for load time difference (On Time, Over, Faster).
        const differenceInLoadTimeSuffix: string =
          differenceInLoadTime === 0
            ? 'On Time'
            : differenceInLoadTime > 0
              ? ' Over'
              : ' Faster';
        // Human-readable load time difference.
        const differenceInLoadTimeReadable: string =
          returnCorrectDuration(Math.abs(differenceInLoadTime)) +
          differenceInLoadTimeSuffix;
        const differenceInLoadTimePercent = RoundCurrencyValue(
          (differenceInLoadTime / pudItem.loadTime) * 100,
        );
        const prefix = returnVariancePrefix(differenceInLoadTime);
        const differenceInLoadTimePercentReadable = `${prefix}${Math.abs(
          differenceInLoadTimePercent,
        )}%`;

        // Build the progress object for this stop.
        const pudProgress: JobRouteProgress = {
          pudId: pudItem.pudId,
          actualArrivalTime,
          actualArrivalTimeReadable,
          actualFinishedTime,
          actualFinishedTimeReadable,
          expectedArrivalTime: null,
          expectedArrivalTimeReadable: '',
          arrivalDifferenceInMins,
          arrivalDifferenceWarning: arrivalDifference
            ? arrivalDifference === 0
              ? null
              : arrivalDifference > 0
            : true,
          actualDepartureTime: finishedEvent.correctEventTime,
          actualDepartureTimeReadable: returnFormattedDate(
            finishedEvent.correctEventTime,
            'HH:mm',
          ),
          expectedDepartureTime: null,
          expectedDepartureTimeReadable: '',
          actualLoadTime,
          actualLoadTimeReadable: actualLoadTime
            ? returnCorrectDuration(actualLoadTime)
            : '',
          loadTimeWarning: actualLoadTime
            ? actualLoadTime > pudItem.loadTime
              ? true
              : false
            : false,
          differenceInLoadTime,
          differenceInLoadTimeReadable,
          differenceInLoadTimePercent,
          differenceInLoadTimePercentReadable,
          estimatedTravelTime,
          estimatedTravelTimeReadable,
          actualTravelTime,
          actualTravelTimeReadable,
          differenceInTravelTimeMs,
          differenceInTravelTimePercent,
          differenceInTravelTimePercentReadable,
          driverLocationUnknown: false,
          showLocationIcon: false,
        };
        jobRouteProgress.push(pudProgress);
      } else {
        // If the stop is finished but no finished event is found, log an error.
        throw new Error(
          `Stop has a defined arrival event but no finished event is known for jobId ${jobDetails.jobId}`,
        );
      }
    }
    // --- Process unfinished stops (pending or in progress) ---
    const stops: PUDItem[] = jobDetails.pudItems.filter(
      (stop: PUDItem) =>
        (stop.legTypeFlag === 'P' || stop.legTypeFlag === 'D') &&
        stop.pudId &&
        stop.status !== 'FINISHED',
    );
    // Find if any leg on the job has been actioned (status is not null/undefined).
    const legOnJobIsActioned = jobDetails.pudItems.find(
      (x: PUDItem) => x.status !== null && x.status !== undefined,
    );
    // Find if any leg is yet to depart (status is 'ARRIVED').
    const legIsYetToDepart = jobDetails.pudItems.find(
      (x: PUDItem) => x.status === 'ARRIVED',
    );
    // Determine if the first GPS coordinate in the route is the driver's location.
    let firstGpsCoordinateIsDriverLocation = false;
    if (jobComplete || !matrix || !matrix.durations) {
      firstGpsCoordinateIsDriverLocation = false;
    } else if (
      !legOnJobIsActioned &&
      stops.length + 1 === matrix.durations.length
    ) {
      // If no leg is actioned and the route has one more stop than the number of stops, assume the first coordinate is the driver's location.
      firstGpsCoordinateIsDriverLocation = true;
    } else if (finishedStops.length > 0) {
      // If there are finished stops, check if the last finished stop's location does not match the first location in the route metadata.
      const lastFinishedStop = finishedStops[finishedStops.length - 1];
      const lastFinishedStopLocation = JSON.stringify(
        lastFinishedStop.address.geoLocation,
      );
      const firstRouteLocation = JSON.stringify(
        matrix.metadata.query.locations[0],
      );
      firstGpsCoordinateIsDriverLocation =
        !legIsYetToDepart && lastFinishedStopLocation !== firstRouteLocation;
    } else {
      firstGpsCoordinateIsDriverLocation = false;
    }
    // Track the total estimated duration remaining for the job.
    let totalDurationToGoInMilli: number = 0;

    // --- Process each non-finished stop to estimate arrival, departure, and travel times ---
    for (let i = 0; i < stops.length; i++) {
      const pudItem: PUDItem = stops[i];
      // Calculate the number of stops in the route matrix and remaining stops.
      const numberOfStopsInRouteRequest =
        matrix && matrix.durations ? matrix.durations.length : 0;
      const numberOfStopsToGo = stops.length;
      // Determine the index in the route matrix for this stop.
      const routeDurationIndexForPud: number =
        numberOfStopsInRouteRequest !== numberOfStopsToGo
          ? i > 0
            ? i - 1
            : 0
          : i;
      const durationIndexInRouteMatrix: number =
        numberOfStopsInRouteRequest === numberOfStopsToGo &&
        ((i === 0 && !pudItem.status) || i === numberOfStopsToGo - 1)
          ? i
          : i + 1;
      // Duration from the previous stop to this stop, in milliseconds.
      const durationFromPreviousPudInMilli =
        matrix && matrix.durations
          ? matrix.durations[routeDurationIndexForPud][
              durationIndexInRouteMatrix
            ] * 1000
          : 0;
      // Find arrival and finished events for this stop.
      const arrivalEvent: JobStatusUpdate | undefined =
        jobDetails.returnSpecifiedEvent('ARRIVED', pudItem.pudId);
      const finishedEvent: JobStatusUpdate | undefined =
        jobDetails.returnSpecifiedEvent('FINISHED', pudItem.pudId);
      // Calculate the expected arrival time (add 1 hour if ASAP).
      const definedArrivalTime = pudItem.epochTime + (isAsap ? 3600000 : 0);
      const actualArrivalTime: number | null = arrivalEvent
        ? arrivalEvent.correctEventTime
        : null;
      const actualArrivalTimeReadable: string = actualArrivalTime
        ? returnFormattedDate(actualArrivalTime, 'HH:mm')
        : '';
      const actualFinishedTime: number | null = finishedEvent
        ? finishedEvent.correctEventTime
        : null;
      const actualFinishedTimeReadable: string = actualFinishedTime
        ? returnFormattedDate(actualFinishedTime, 'HH:mm')
        : '';
      // --- Travel Time Calculation ---
      const {
        estimatedTravelTime,
        estimatedTravelTimeReadable,
        actualTravelTime,
        actualTravelTimeReadable,
        differenceInTravelTimeMs,
        differenceInTravelTimePercent,
        differenceInTravelTimePercentReadable,
      }: {
        estimatedTravelTime: number | null;
        estimatedTravelTimeReadable: string;
        actualTravelTime: number | null;
        actualTravelTimeReadable: string;
        differenceInTravelTimeMs: number | null;
        differenceInTravelTimePercent: number | null;
        differenceInTravelTimePercentReadable: string;
      } = calculateTravelTimeDifferences(jobDetails, pudItem.pudId);

      // ##-----------------------------------------------------------##
      // --- Handle stops that have been arrived at but not finished ---
      // ##-----------------------------------------------------------##
      if (arrivalEvent && !finishedEvent) {
        totalDurationToGoInMilli += durationFromPreviousPudInMilli;
        // Calculate time spent on site and remaining expected load time.
        const timeSpentOnSite = currentTime - arrivalEvent.correctEventTime;
        let remainingTimeOnSite = pudItem.loadTime;
        if (timeSpentOnSite > 0) {
          if (timeSpentOnSite <= pudItem.loadTime) {
            remainingTimeOnSite = pudItem.loadTime - timeSpentOnSite;
          } else {
            // If driver is on site longer than expected, set remaining time to 0.
            remainingTimeOnSite = 0;
          }
        }
        // Defensive: Ensure arrival time is known.
        if (!actualArrivalTime) {
          throw new Error(
            'Stop has an defined arrived event but no arrival time is known for jobId ' +
              jobDetails.jobId,
          );
        }
        // Calculate difference between actual and expected arrival times.
        const arrivalDifference: number =
          actualArrivalTime - definedArrivalTime;
        const arrivalDifferenceSuffix =
          arrivalDifference === 0
            ? 'On Time'
            : arrivalDifference > 0
              ? ' Late'
              : ' Early';
        const arrivalDifferenceInMins = isClientPortal
          ? ''
          : returnCorrectDuration(arrivalDifference) + arrivalDifferenceSuffix;
        // Suffix for remaining or over load time.
        const differenceInLoadTimeSuffix: string =
          pudItem.loadTime > timeSpentOnSite ? ' Left' : '  Over';
        // Human-readable remaining or over load time.
        const differenceInLoadTimeReadable: string =
          (pudItem.loadTime > timeSpentOnSite
            ? returnCorrectDuration(remainingTimeOnSite)
            : returnCorrectDuration(timeSpentOnSite - pudItem.loadTime)) +
          differenceInLoadTimeSuffix;

        // Build the progress object for this stop.
        const pudProgress: JobRouteProgress = {
          pudId: pudItem.pudId,
          actualArrivalTime,
          actualArrivalTimeReadable,
          actualFinishedTime,
          actualFinishedTimeReadable,
          expectedArrivalTime: null,
          expectedArrivalTimeReadable: '',
          arrivalDifferenceInMins,
          arrivalDifferenceWarning: arrivalDifference
            ? arrivalDifference === 0
              ? null
              : arrivalDifference > 0
                ? true
                : false
            : null,
          actualDepartureTime: null,
          actualDepartureTimeReadable: '',
          expectedDepartureTime: currentTime + remainingTimeOnSite,
          expectedDepartureTimeReadable: returnFormattedDate(
            currentTime + remainingTimeOnSite,
            'HH:mm',
          ),
          actualLoadTime: null,
          actualLoadTimeReadable: '',
          differenceInLoadTime: null,
          loadTimeWarning: timeSpentOnSite > pudItem.loadTime ? true : false,
          differenceInLoadTimeReadable,
          differenceInLoadTimePercent: null,
          differenceInLoadTimePercentReadable: '',
          estimatedTravelTime,
          estimatedTravelTimeReadable,
          actualTravelTime,
          actualTravelTimeReadable,
          differenceInTravelTimeMs,
          differenceInTravelTimePercent,
          differenceInTravelTimePercentReadable,
          driverLocationUnknown: false,
          showLocationIcon: false,
        };
        jobRouteProgress.push(pudProgress);
        // Add remaining load time to total duration.
        totalDurationToGoInMilli += remainingTimeOnSite;
        continue;
      }
      // ##----------------------------------------------------------##
      // --- Handle stops that have not been arrived at or finished ---
      // ##-----------------------------------------------------------##
      else if (!arrivalEvent && !finishedEvent) {
        totalDurationToGoInMilli += durationFromPreviousPudInMilli;
        // Determine if the driver's location is unknown (for display purposes).
        const driverLocationUnknown: boolean = isClientPortal
          ? false
          : jobStarted &&
            !firstGpsCoordinateIsDriverLocation &&
            i === 0 &&
            finishedStops.length === 0;
        // Estimate expected arrival time.
        const expectedArrivalTime: number =
          currentTime + totalDurationToGoInMilli;
        // Calculate difference between expected and defined arrival times.
        const arrivalDifference: number =
          expectedArrivalTime - definedArrivalTime;
        const arrivalDifferenceSuffix =
          expectedArrivalTime === 0
            ? 'On Time'
            : arrivalDifference > 0
              ? ' Late'
              : jobStarted && !driverLocationUnknown
                ? ' Early'
                : ' To Arrive';
        const arrivalDifferenceInMins = isClientPortal
          ? ''
          : returnCorrectDuration(arrivalDifference) + arrivalDifferenceSuffix;
        // Build the progress object for this stop.
        const pudProgress: JobRouteProgress = {
          pudId: pudItem.pudId,
          actualArrivalTime,
          actualArrivalTimeReadable,
          actualFinishedTime,
          actualFinishedTimeReadable,
          expectedArrivalTime,
          expectedArrivalTimeReadable:
            jobStarted &&
            (legOnJobIsActioned || firstGpsCoordinateIsDriverLocation) &&
            expectedArrivalTime
              ? returnFormattedDate(expectedArrivalTime, 'HH:mm')
              : '',
          arrivalDifferenceInMins,
          arrivalDifferenceWarning: arrivalDifference
            ? arrivalDifference === 0
              ? null
              : arrivalDifference > 0
            : true,
          actualDepartureTime: null,
          actualDepartureTimeReadable: '',
          expectedDepartureTime: expectedArrivalTime + pudItem.loadTime,
          expectedDepartureTimeReadable:
            (legOnJobIsActioned || firstGpsCoordinateIsDriverLocation) &&
            jobStarted
              ? returnFormattedDate(
                  expectedArrivalTime + pudItem.loadTime,
                  'HH:mm',
                )
              : '',
          actualLoadTime: null,
          actualLoadTimeReadable: '',
          loadTimeWarning: false,
          differenceInLoadTime: null,
          differenceInLoadTimeReadable: '',
          differenceInLoadTimePercent: null,
          differenceInLoadTimePercentReadable: '',
          estimatedTravelTime,
          estimatedTravelTimeReadable,
          actualTravelTime,
          actualTravelTimeReadable,
          differenceInTravelTimeMs,
          differenceInTravelTimePercent,
          differenceInTravelTimePercentReadable,
          driverLocationUnknown,
          showLocationIcon: firstGpsCoordinateIsDriverLocation,
        };
        jobRouteProgress.push(pudProgress);
        // Add load time to total duration.
        totalDurationToGoInMilli += pudItem.loadTime;
        continue;
      } else {
        // If the stop is not finished but has an arrival event, log an error.
        throw new Error(
          `Processing non-finished stops, jobId ${jobDetails.jobId} pudId ${pudItem.pudId} has invalid event data for route progress generation`,
        );
      }
    }
    // Store the calculated progress in additionalJobData for use elsewhere in the app.
    if (jobDetails.additionalJobData) {
      jobDetails.additionalJobData.routeProgress = jobRouteProgress;
    }
  } catch (error) {
    // Log and swallow errors to avoid breaking the UI.
    logConsoleError('Generation of JobRouteProgress data failed', error);
    return;
  }
}

/**
 * Returns estimated and actual travel times between stops, and their difference
 * (ms and percent). Only applies to stops after the first. Uses planned route
 * for estimate and event times for actuals.
 *
 * @param jobDetails - The job details object containing PUD items and events.
 * @param pudId - The PUD item ID for which to calculate travel time
 * differences.
 * @returns An object with estimated/actual travel times, their readable forms,
 * and differences.
 */
function calculateTravelTimeDifferences(
  jobDetails: JobDetails,
  pudId: string,
): {
  estimatedTravelTime: number | null;
  estimatedTravelTimeReadable: string;
  actualTravelTime: number | null;
  actualTravelTimeReadable: string;
  differenceInTravelTimeMs: number | null;
  differenceInTravelTimePercent: number | null;
  differenceInTravelTimePercentReadable: string;
} {
  let estimatedTravelTime: number | null = null;
  let estimatedTravelTimeReadable = '';
  let actualTravelTime: number | null = null;
  let actualTravelTimeReadable = '';
  let differenceInTravelTimeMs: number | null = null;
  let differenceInTravelTimePercent: number | null = null;
  let differenceInTravelTimePercentReadable = '';

  // Find the index of this pud in jobDetails.pudItems. We only want to
  // calculate travel times for the stops after the first one (2nd onwards).
  const pudIndex: number = jobDetails.pudItems.findIndex(
    (item: PUDItem) => item.pudId === pudId,
  );

  // If pudIndex is -1, the pudId was not found in the jobDetails.pudItems.
  if (pudIndex === -1 || !jobDetails.pudItems[pudIndex].status) {
    return {
      estimatedTravelTime,
      estimatedTravelTimeReadable,
      actualTravelTime,
      actualTravelTimeReadable,
      differenceInTravelTimeMs,
      differenceInTravelTimePercent,
      differenceInTravelTimePercentReadable,
    };
  }
  if (pudIndex > 0) {
    // For all but the first stop, calculate travel times from previous stop.
    // based on plannedRoute
    const segment =
      jobDetails.plannedRoute?.routes?.[0]?.segments?.[pudIndex - 1];
    if (segment?.duration) {
      estimatedTravelTime = segment.duration * 1000;
      estimatedTravelTimeReadable = returnCorrectDuration(estimatedTravelTime);
    }
    // Actual travel time: difference between previous stop's finish and this stop's arrival (if available)
    const prevStop = jobDetails.pudItems[pudIndex - 1];
    const prevFinishedTime: number | null =
      jobDetails.returnSpecifiedEvent('FINISHED', prevStop.pudId)
        ?.correctEventTime ?? null;
    const actualArrivalTime: number | null =
      jobDetails.returnSpecifiedEvent('ARRIVED', pudId)?.correctEventTime ??
      null;
    if (prevFinishedTime && actualArrivalTime) {
      actualTravelTime = actualArrivalTime - prevFinishedTime;
      actualTravelTimeReadable = actualTravelTime
        ? returnCorrectDuration(actualTravelTime)
        : '';
    }
    // Difference between actual and estimated travel time
    if (actualTravelTime !== null && estimatedTravelTime !== null) {
      differenceInTravelTimeMs = actualTravelTime - estimatedTravelTime;
      differenceInTravelTimePercent = RoundCurrencyValue(
        (differenceInTravelTimeMs / estimatedTravelTime) * 100,
      );
      differenceInTravelTimePercentReadable = returnFormattedVariancePercent(
        differenceInTravelTimePercent,
      );
    }
  }
  return {
    estimatedTravelTime,
    estimatedTravelTimeReadable,
    actualTravelTime,
    actualTravelTimeReadable,
    differenceInTravelTimeMs,
    differenceInTravelTimePercent,
    differenceInTravelTimePercentReadable,
  };
}
