import { useCompanyDetailsStore } from '@/store/modules/CompanyDetailsStore';
import moment from 'moment-timezone';

export const millisecondsInOneDay = 86400000;

/**
 * Returns the current time in milliseconds since epoch
 * @returns {number} - The current time in milliseconds.
 */
export function returnTimeNow(): number {
  return moment().valueOf();
}

/**
 * Gets the user's locale from the division details object in the store. If not
 * available, guesses the timezone.
 * @returns {string} - The user's locale or guessed timezone.
 */
export function getUserLocale(): string {
  return useCompanyDetailsStore().userLocale || moment.tz.guess();
}

/**
 * Returns the formatted time string for a given epoch time. Defaults to 'HH:mm'
 * @param {number} epoch - The epoch time in milliseconds.
 * @param {string} [dateFormat='HH:mm'] - The format string for the time.
 * @returns {string} - The formatted time string.
 */
export function returnFormattedTime(
  epoch: number,
  dateFormat: string = 'HH:mm',
): string {
  const userTimeZone = getUserLocale();
  return moment(epoch).tz(userTimeZone).format(dateFormat);
}

/**
 * Returns the formatted date string for a given epoch time. Defaults to 'DD/MM/YY'
 * @param {number} epoch - The epoch time in milliseconds.
 * @param {string} [dateFormat=''] - The format string for the date.
 * @param {boolean} [startOfDay=false] - Whether to format the date as the start of the day.
 * @returns {string} - The formatted date string.
 */
export function returnFormattedDate(
  epoch: number,
  dateFormat: string = '',
  startOfDay: boolean = false,
): string {
  const dateFormatStr = dateFormat !== '' ? dateFormat : 'DD/MM/YY';
  const userTimeZone = getUserLocale();
  if (startOfDay) {
    return moment(epoch).tz(userTimeZone).startOf('day').format(dateFormatStr);
  } else {
    return moment(epoch).tz(userTimeZone).format(dateFormatStr);
  }
}

/**
 * Returns the duration in milliseconds for a given counter and duration type.
 * @param {number} [counter=1] - The number of units for the duration.
 * @param {moment.unitOfTime.DurationConstructor} [type='days'] - The type of duration (e.g., 'days', 'hours').
 * @returns {number} - The duration in milliseconds.
 */
export function returnMillisecondsFromDuration(
  counter: number = 1,
  type: moment.unitOfTime.DurationConstructor = 'days',
): number {
  const momentDuration = moment.duration(counter, type);
  return momentDuration.asMilliseconds();
}

/**
 * Returns a formatted duration string from a given duration in milliseconds. The
 * format is 'd[d] H[h] m[m]', where 'd' is days, 'h' is hours, and 'm' is minutes.
 * @param {number} durationInMilliseconds - The duration in milliseconds.
 * @returns {string} - The formatted duration string.
 */
export function returnDurationFromMilliseconds(
  durationInMilliseconds: number,
): string {
  const momentDuration = moment.duration(durationInMilliseconds);
  let days: number = momentDuration.asDays();
  const hours: number = momentDuration.asHours();
  if (days > 1) {
    days -= 1;
    return moment
      .utc(momentDuration.asMilliseconds())
      .format(days + '[d]' + ' H[h] m[m]');
  } else if (hours > 0) {
    return moment.utc(momentDuration.asMilliseconds()).format('H[h] m[m]');
  } else {
    return moment.utc(momentDuration.asMilliseconds()).format('m[m]');
  }
}
// Format current time to provided date time format.
export function returnFormattedCurrentTime(
  format: string = 'ddd Do MMM  hh:mm a',
): string {
  const userTimeZone: string = getUserLocale();
  const date = moment(new Date());

  if (userTimeZone) {
    // Return formatted current time using user's time zone
    return date.tz(userTimeZone).format(format);
  } else {
    // Return formatted current time using guessed time zone
    return date.tz(moment.tz.guess()).format(format);
  }
}

// Format current time and returns the result as a list with [date, time] as
// strings
export function returnFormattedCurrentDateAndTime(): [string, string] {
  const userTimeZone: string = getUserLocale();
  const date = moment(new Date());

  if (userTimeZone) {
    // Return array: [formatted current date, formatted current time] in user's time zone
    return [
      date.tz(userTimeZone).format('DD-MMM-YY'),
      date.tz(userTimeZone).format('h:mm a'),
    ];
  } else {
    // Return array: [formatted current date, formatted current time] using guessed time zone
    return [
      date.tz(moment.tz.guess()).format('ddd Do MMM'),
      date.tz(moment.tz.guess()).format('hh:mm a'),
    ];
  }
}

// Accepts milliseconds and returns the number of minutes (rounded down)
export function returnRoundedHoursFromMilliseconds(ms: number) {
  const d = moment.duration(ms, 'milliseconds');
  const hours = Math.floor(d.asHours());
  const mins = Math.floor(d.asMinutes()) - hours * 60;
  return mins;
}
// Accepts milliseconds and returns the number of minutes (rounded down)
export function returnRoundedMinutesFromMilliseconds(ms: number) {
  const d = moment.duration(ms, 'milliseconds');
  const hours = Math.floor(d.asHours());
  const mins = Math.floor(d.asMinutes()) - hours * 60;
  return mins;
}

// Accepts epoch time and returns the epochTime of the end of that day
// If supplied epoch is 0, then use today's date
export function returnEndOfDayFromEpoch(epoch: number = 0): number {
  const userTimeZone = getUserLocale();
  if (epoch) {
    // End of day from supplied time
    return moment(epoch).tz(userTimeZone).endOf('day').valueOf();
  } else {
    // End of today
    return moment().tz(userTimeZone).endOf('day').valueOf();
  }
}

// Accepts epoch time and returns the epochTime of the start of that day
// If supplied epoch is 0, then use today's date
export function returnStartOfDayFromEpoch(epoch: number = 0): number {
  const userTimeZone = getUserLocale();
  if (epoch) {
    // Start of day from supplied time
    return moment(epoch).tz(userTimeZone).startOf('day').valueOf();
  } else {
    // Start of today
    return moment().tz(userTimeZone).startOf('day').valueOf();
  }
}

// Return star of current week (for RCTI table date filter). Optional argument
// for start of the previous week
export function returnStartOfCurrentWeek(previous: boolean = false) {
  const userTimeZone = getUserLocale();
  if (!previous) {
    return moment().tz(userTimeZone).startOf('isoWeek').valueOf() - 1;
  } else {
    return (
      moment()
        .tz(userTimeZone)
        .startOf('isoWeek')
        .subtract(1, 'week')
        .valueOf() - 1
    );
  }
}
// Checks if the provided epochTime is within the start and end of TODAY
export function isToday(epochTime: number): boolean {
  const startOfDay = returnStartOfDayFromEpoch();
  const endOfDay = returnEndOfDayFromEpoch();
  return epochTime >= startOfDay && epochTime < endOfDay;
}
// Returns the epochTime for the n'th hour of the day (denoted by hours)
// i.e. passing in 6 will get the epochTime of 6am today
export function returnHoursAfterStartOfDay(hours: number): number {
  const userTimeZone = getUserLocale();
  return moment().tz(userTimeZone).startOf('day').add(hours, 'hours').valueOf();
}

// check if date is valid. Note that both date and time do not include '/' && ':' character
export function dateAndTimeIsValid(date: string, time: string): boolean {
  const patternTime = /^([01]\d|2[0-3])([0-5]\d)$/;
  const patternDate =
    /^((((0[1-9]|[1-2][0-9]|3[0-1])(0[13578]|10|12))|((0[1-9]|[1-2][0-9])(02))|(((0[1-9])|([1-2][0-9])|(30))(0[469]|11)))((19\d{2})|(2[012]\d{2})))$/;
  const result = patternTime.test(time) && patternDate.test(date);
  return result;
}
// check if date is valid. Note that both date and time do not include '/' && ':' character
export function dateAndTimeIsValidYY(date: string, time: string): boolean {
  const patternTime = /^([01]\d|2[0-3])([0-5]\d)$/;
  const patternDate =
    /^((((0[1-9]|[1-2][0-9]|3[0-1])(0[13578]|10|12))|((0[1-9]|[1-2][0-9])(02))|(((0[1-9])|([1-2][0-9])|(30))(0[469]|11)))(\d{2}))$/;
  const result = patternTime.test(time) && patternDate.test(date);
  return result;
}

// Round down the provided epoch time to the nearest minute
export function roundDownToNearestMinute(epoch: number) {
  const userTimeZone = getUserLocale();
  return moment(epoch).tz(userTimeZone).startOf('minute').valueOf();
}
// Round down the provided epoch time to the nearest hour
export function roundDownToNearestHour(epoch: number = 0) {
  const userTimeZone = getUserLocale();
  if (epoch) {
    return moment(epoch).tz(userTimeZone).startOf('hour').valueOf();
  } else {
    return moment().tz(userTimeZone).startOf('hour').valueOf();
  }
}
// Round down the provided epoch time to the nearest hour
export function roundUpToNearestHour(epoch: number = 0): number {
  const userTimeZone = getUserLocale();
  if (epoch) {
    return moment(epoch)
      .tz(userTimeZone)
      .add(1, 'hour')
      .startOf('hour')
      .valueOf();
  } else {
    return moment().tz(userTimeZone).add(1, 'hour').startOf('hour').valueOf();
  }
}

export function returnEarliestBookingTime(): number {
  const userTimeZone = getUserLocale();
  return moment()
    .tz(userTimeZone)
    .subtract(3, 'months')
    .startOf('day')
    .valueOf();
}

// converts milliseconds to readable format eg 59m || 25h22m
export function convertMillisecondsToTime(milliseconds: number) {
  milliseconds = Math.abs(milliseconds);
  const minutes = Math.floor(milliseconds / 60000);
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  if (hours > 0) {
    const hoursString = hours.toString(10).padStart(2, '0');
    return `${hoursString}h ${remainingMinutes.toString().padStart(2, '0')}m`;
  } else {
    return `${remainingMinutes}m`;
  }
}

export function getMilliDurationAsMinutes(duration: number): number | null {
  try {
    return moment.duration(duration, 'milliseconds').asMinutes();
  } catch (e) {
    return null;
  }
}

/**
 * Returns a natural language representation of the given epoch time in the
 * user's timezone.
 * - If the time is less than an hour ago, it returns a relative time using the
 *   "fromNow" format.
 * - If the time is earlier today, it returns the time of day in the format
 *   "h:mma".
 * - If the time was yesterday, it returns "Yesterday" followed by the time in
 *   the format "h:mma".
 * - If the time is before yesterday, it returns the date and time in the format
 *   "DD/MM/YYYY h:mma".
 * @param epochMs - The epoch time in milliseconds.
 * @returns A string representing the natural language representation of the
 * given epoch time.
 */
export function returnNaturalDateTime(epochMs: number): string {
  const tz: string = getUserLocale();
  const time = moment(epochMs).tz(tz);
  const now = moment().tz(tz);

  if (now.diff(time, 'minutes') < 60) {
    // If the time is less than an hour ago, use fromNow
    return time.fromNow();
  } else if (now.isSame(time, 'day')) {
    // If the time is earlier today, show the time of day
    return time.format('h:mma');
  } else if (now.subtract(1, 'day').isSame(time, 'day')) {
    // If the time was yesterday, show 'Yesterday' and the time
    return `Yesterday ${time.format('h:mma')}`;
  } else {
    // If the time is before yesterday, show the date and time
    return time.format('DD/MM/YYYY h:mma');
  }
}

export function nearestMinutes(interval: any, someMoment: any) {
  const roundedMinutes =
    Math.round(someMoment.clone().minute() / interval) * interval;
  return someMoment.clone().minute(roundedMinutes).second(0).milliseconds(0);
}

export function nearestPastMinutes(interval: any, someMoment: any) {
  const roundedMinutes = Math.floor(someMoment.minute() / interval) * interval;
  return someMoment.clone().minute(roundedMinutes).second(0).milliseconds(0);
}

export function nearestFutureMinutes(interval: any, someMoment: any) {
  const roundedMinutes = Math.ceil(someMoment.minute() / interval) * interval;
  return someMoment.clone().minute(roundedMinutes).second(0).milliseconds(0);
}
